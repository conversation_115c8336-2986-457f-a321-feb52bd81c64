<template>
  <div class="min-h-screen">
    <!-- 头部 -->
    <header class="header">
      <div class="header-container">
        <div class="header-content">
          <!-- 将选择站点按钮放在左边 - 修改为检查会员状态 -->
          <div class="right-actions">
            <button
              class="select-station-button"
              :class="{ 'disabled-button': !isMember }"
              @click="handleStationSelectorClick"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="train-icon"
              >
                <rect width="16" height="6" x="2" y="3" rx="1"></rect>
                <path d="m7 10 1.5 1.5L7 13"></path>
                <path d="m17 10-1.5 1.5L17 13"></path>
                <path d="M8 3v4"></path>
                <path d="M16 3v4"></path>
                <path d="M8 18h.01"></path>
                <path d="M16 18h.01"></path>
                <rect width="16" height="5" x="2" y="14" rx="1"></rect>
              </svg>
              <span>{{ t("member.station.selectStation") }}</span>
              <span class="station-count" v-if="selectedStations.length > 0"
                >({{ selectedStations.length }})</span
              >
              <span class="station-price" v-if="isMember"
                >(¥{{ totalStationPrice }})</span
              >
            </button>
          </div>

          <div class="logo-container">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="crown-icon"
            >
              <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7z"></path>
              <path d="M4 16h16"></path>
            </svg>
            <span class="logo-text">{{ t("member.title") }}</span>
          </div>
          <div class="header-actions">
            <!-- 返回首页按钮 -->
            <div class="back-button" @click="goToHome">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="back-icon"
              >
                <path d="m12 19-7-7 7-7"></path>
                <path d="M19 12H5"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 会员信息 -->
    <main class="main-container">
      <!-- 会员状态卡片 -->
      <div class="section-container">
        <div
          class="status-card"
          :class="{ 'premium-card-status': membershipStatus?.type === 'super' }"
        >
          <div class="card-header">
            <div class="card-header-content">
              <h3 class="card-title">{{ t("member.memberStatus") }}</h3>
              <p class="card-subtitle">{{ t("member.statusDesc") }}</p>
            </div>
            <div class="status-badge" v-if="membershipStatus">
              <span
                :class="
                  membershipStatus.type === 'super'
                    ? 'badge-premium'
                    : 'badge-regular'
                "
              >
                {{
                  membershipStatus.type === "super"
                    ? t("member.premiumMember")
                    : t("member.regularMember")
                }}
              </span>
            </div>
          </div>
          <div class="card-body">
            <div class="member-status">
              <div
                class="status-icon-container"
                :class="{
                  'premium-icon-bg': membershipStatus?.type === 'super',
                }"
              >
                <svg
                  v-if="!membershipStatus"
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="user-icon"
                >
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  width="36"
                  height="36"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  :class="[
                    'crown-icon-large',
                    { premium: membershipStatus.type === 'super' },
                  ]"
                >
                  <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7z"></path>
                  <path d="M4 16h16"></path>
                </svg>
              </div>
              <div class="status-info">
                <div class="status-header">
                  <h3
                    class="status-title"
                    :class="{
                      'premium-text': membershipStatus?.type === 'super',
                    }"
                  >
                    {{
                      membershipStatus
                        ? membershipStatus.name
                        : t("member.notActive")
                    }}
                  </h3>
                  <p class="status-expiry" v-if="membershipStatus">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="expiry-icon"
                    >
                      <rect
                        x="3"
                        y="4"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    {{ t("member.expiryDate") }}:
                    <span class="expiry-date">{{
                      formatDate(membershipStatus.expiryDate)
                    }}</span>
                  </p>
                </div>

                <div
                  v-if="!membershipStatus?.benefits?.length"
                  class="no-benefits"
                >
                  <p class="status-subtitle">
                    {{ t("member.memberBenefits") }}
                  </p>
                  <button
                    class="explore-button"
                    @click="scrollToMembershipPlans"
                    v-if="!membershipStatus"
                  >
                    {{ t("member.buttons.explore") }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 会员类型 -->
      <div class="membership-section" ref="membershipPlansSection">
        <h2 class="section-title">{{ t("member.memberPlans") }}</h2>
        <div class="membership-cards">
          <!-- 普通会员卡片 -->
          <div class="regular-card">
            <div class="card-shine"></div>
            <div class="card-content">
              <div class="card-header-flex">
                <h3 class="membership-title">
                  {{ t("member.regularMember") }}
                </h3>
                <span class="tag tag-recommended">{{
                  t("member.recommended")
                }}</span>
              </div>
              <!-- 普通会员价格显示 -->
              <div class="price-container">
                <span class="price">¥{{ regularMember?.price ?? 0 }}</span>
                <span class="price-period">{{
                  regularMember
                    ? `/${regularMember.validityPeriod}${t(
                        "member.pricePerMonth"
                      )}`
                    : t("member.pricePerMonth")
                }}</span>
              </div>
              <ul class="benefits-list">
                <!-- 使用包装元素移动v-if -->
                <template v-if="regularMember?.memberDescribeList?.length">
                  <li
                    v-for="(benefit, index) in regularMember.memberDescribeList"
                    :key="index"
                    class="benefit-item"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="check-icon"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <div class="benefit-text" v-html="formatBenefitText(benefit.introduce)"></div>
                  </li>
                </template>
                <!-- 无描述时显示默认内容 -->
                <li v-else class="benefit-item">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="check-icon"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>{{ t("member.benefits.unlimitedViews") }}</span>
                </li>
              </ul>
              <div class="card-actions">
                <button
                  class="action-button"
                  :class="
                    membershipStatus?.type === 'super'
                      ? 'premium-disabled'
                      : 'primary-button'
                  "
                  @click="handleActivateClick"
                >
                  <i-lucide-save class="icon" />
                  <span>{{ getButtonText }}</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 超级会员卡片 -->
          <div class="premium-card">
            <div class="card-shine"></div>
            <div class="card-content">
              <div class="card-header-flex">
                <h3 class="membership-title premium-title">
                  {{ t("member.premiumMember") }}
                </h3>
                <span class="tag tag-premium">{{ t("member.premium") }}</span>
              </div>
              <!-- 超级会员价格显示 -->
              <div class="price-container">
                <span class="price premium-price"
                  >¥{{ premiumMember?.price ?? 0 }}</span
                >
                <span class="price-period premium-period">{{
                  premiumMember
                    ? `/${premiumMember.validityPeriod}${t(
                        "member.pricePerMonth"
                      )}`
                    : t("member.pricePerMonth")
                }}</span>
              </div>
              <ul class="benefits-list">
                <!-- 使用包装元素移动v-if -->
                <template v-if="premiumMember?.memberDescribeList?.length">
                  <li
                    v-for="(benefit, index) in premiumMember.memberDescribeList"
                    :key="index"
                    class="benefit-item premium-benefit"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="check-icon-premium"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <div class="benefit-text" v-html="formatBenefitText(benefit.introduce)"></div>
                  </li>
                </template>
                <!-- 无描述时显示默认内容 -->
                <li v-else class="benefit-item premium-benefit">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="check-icon-premium"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>{{ t("member.benefits.allRegularBenefits") }}</span>
                </li>
              </ul>
              <div class="card-actions">
                <button
                  class="action-button premium-button"
                  @click="contactCustomerService"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="phone-icon"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  {{ t("member.buttons.contactService") }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 支付弹窗 - 修改为多优惠券选择 -->
    <div v-if="showPaymentModal" class="modal-container">
      <div class="modal-backdrop" @click="resetPaymentModalState"></div>
      <div class="modal-content payment-modal">
        <div class="modal-header">
          <h3 class="modal-title">
            {{
              isMultiSelectMode
                ? t("member.payment.titleStations") || "站点购买"
                : membershipStatus?.type === "regular" 
                ? t("member.payment.titleRenew")
                : t("member.payment.title")
            }}
          </h3>
        </div>
        <div class="modal-body">
          <div class="payment-details">
            <div class="payment-summary">
              <!-- 仅当不是会员或不是通过站点选择按钮进入时才显示会员费用 -->
              <div
                class="payment-row"
                v-if="!isMember.value || !isMultiSelectMode.value"
              >
                <span class="payment-label">{{
                  t("member.payment.memberAmount") || "会员费用"
                }}</span>
                <span class="payment-value"
                  >¥{{ (regularMember?.price ?? 0).toFixed(2) }}</span
                >
              </div>

              <!-- 添加站点费用明细 -->
              <div v-if="selectedStations.length > 0" class="payment-stations">
                <div class="payment-row payment-station-header">
                  <span class="payment-label">{{
                    t("member.payment.stationAmount") || "站点费用"
                  }}</span>
                  <span class="payment-value"
                    >¥{{ totalStationPrice.toFixed(2) }}</span
                  >
                </div>
                <div class="payment-station-list">
                  <div
                    v-for="(station, index) in selectedStations"
                    :key="station.id"
                    class="payment-station-item"
                  >
                    <span class="station-name"
                      >{{ index + 1 }}. {{ station.stationNameZh }}</span
                    >
                    <span class="station-price"
                      >¥{{ stationPrice.toFixed(2) }}</span
                    >
                  </div>
                </div>
              </div>

              <div class="payment-row" v-if="selectedCoupon">
                <span class="payment-label">{{
                  t("member.payment.discount")
                }}</span>
                <span class="payment-discount"
                  >-¥{{ selectedCoupon.amount.toFixed(2) }}</span
                >
              </div>
              <div class="payment-divider"></div>
              <div class="payment-row payment-total">
                <span class="payment-label">{{
                  t("member.payment.total")
                }}</span>
                <span class="payment-value"
                  >¥{{ calculateTotal().toFixed(2) }}</span
                >
              </div>
            </div>

            <!-- 优惠券选择区域 -->
            <div class="coupon-section">
              <div class="coupon-header" @click="toggleCouponList">
                <span class="coupon-title">{{
                  t("member.payment.coupon.select")
                }}</span>
                <span class="coupon-selected" v-if="selectedCoupon">
                  {{ selectedCoupon.name }} (-¥{{
                    selectedCoupon.amount.toFixed(2)
                  }})
                </span>
                <span class="coupon-selected" v-else>{{
                  t("member.payment.coupon.none")
                }}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  :class="[
                    'coupon-arrow',
                    showCouponList ? 'coupon-arrow-up' : '',
                  ]"
                >
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>

              <!-- 优惠券列表 -->
              <div class="coupon-list" v-if="showCouponList">
                <div class="coupon-item" @click="selectCoupon(null)">
                  <div class="coupon-item-content">
                    <span class="coupon-item-name">{{
                      t("member.payment.coupon.none")
                    }}</span>
                    <span class="coupon-item-check" v-if="!selectedCoupon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                    </span>
                  </div>
                </div>
                <div
                  v-for="(coupon, index) in availableCoupons"
                  :key="index"
                  class="coupon-item"
                  @click="selectCoupon(coupon)"
                >
                  <div class="coupon-item-content">
                    <div class="coupon-item-info">
                      <span class="coupon-item-name">{{ coupon.name }}</span>
                      <span class="coupon-item-desc">{{
                        coupon.description
                      }}</span>
                      <span class="coupon-item-expiry"
                        >{{ t("member.payment.coupon.expiry") }}:
                        {{ formatDate(coupon.expiryDate) }}</span
                      >
                    </div>
                    <span class="coupon-item-amount"
                      >-¥{{ coupon.amount.toFixed(2) }}</span
                    >
                    <span
                      class="coupon-item-check"
                      v-if="selectedCoupon && selectedCoupon.id === coupon.id"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                    </span>
                  </div>
                  <span class="coupon-status coupon-status-available">{{
                    t("userInfo.vouchers.canUse")
                  }}</span>
                </div>
              </div>
            </div>

            <div class="payment-method-section">
              <label class="payment-method-label">{{
                t("member.payment.method.title")
              }}</label>
              <div class="payment-methods">
                <div
                  class="payment-method-option"
                  :class="{
                    'payment-method-selected': paymentMethod === 'wechat',
                  }"
                  @click="paymentMethod = 'wechat'"
                >
                  <span class="payment-method-name wechat">{{
                    t("member.payment.method.wechat")
                  }}</span>
                </div>
                <div
                  class="payment-method-option"
                  :class="{
                    'payment-method-selected': paymentMethod === 'alipay',
                  }"
                  @click="paymentMethod = 'alipay'"
                >
                  <span class="payment-method-name alipay">{{
                    t("member.payment.method.alipay")
                  }}</span>
                </div>
                <div
                  class="payment-method-option"
                  :class="{
                    'payment-method-selected': paymentMethod === 'card',
                  }"
                  @click="paymentMethod = 'card'"
                >
                  <span class="payment-method-name card">{{
                    t("member.payment.method.card")
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-button primary-button" @click="processPayment">
            {{ t("member.payment.confirm") }}
          </button>
          <button
            class="action-button secondary-button"
            @click="resetPaymentModalState"
          >
            {{ t("member.payment.cancel") }}
          </button>
        </div>
      </div>
    </div>

    <!-- 火车站选择弹窗 -->
    <div v-if="showStationSelector" class="modal-container">
      <div class="modal-backdrop" @click="showStationSelector = false"></div>
      <div class="modal-content station-selector-modal">
        <div class="modal-header">
          <div class="header-content">
            <div class="header-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="train-header-icon"
              >
                <rect width="16" height="6" x="2" y="3" rx="1"></rect>
                <path d="m7 10 1.5 1.5L7 13"></path>
                <path d="m17 10-1.5 1.5L17 13"></path>
                <path d="M8 3v4"></path>
                <path d="M16 3v4"></path>
                <path d="M8 18h.01"></path>
                <path d="M16 18h.01"></path>
                <rect width="16" height="5" x="2" y="14" rx="1"></rect>
              </svg>
            </div>
            <div class="header-text">
              <h3 class="modal-title">
                {{ t("member.station.selectStationTitle") }}
              </h3>
              <p class="modal-subtitle">
                {{ t("member.station.selectStationSubtitle") }}
                <span class="station-fee-notice"
                  >{{ t("member.station.stationFee") }}: ¥{{
                    stationPrice.toFixed(2)
                  }}</span
                >
                <span v-if="isMultiSelectMode" class="multi-select-notice">{{
                  t("member.station.multiSelectHint") || "可多选"
                }}</span>
                <span v-else class="single-select-notice">{{
                  t("member.station.singleSelectHint") || "单选模式"
                }}</span>
              </p>
            </div>
          </div>
          <button class="close-button" @click="showStationSelector = false">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
     
        </div>
               <span>&nbsp&nbsp&nbsp&nbsp{{ t("member.station.zhushi") }}</span>
        <div class="modal-body">
          <div class="station-selector-content">
             
            <div class="search-section">
              
              <div class="search-input-container">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="search-icon"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
                  
                <input
                  type="text"
                  v-model="searchQuery"
                  :placeholder="t('member.station.searchPlaceholder')"
                  class="search-input"
                />
              </div>
       
              <!-- 热门站点快捷选择 -->
            </div>

            <div class="stations-grid" v-if="!isLoading">
              <div
                v-for="station in filteredStations"
                :key="station.id"
                class="station-card"
                :class="{
                  'station-selected': isMultiSelectMode
                    ? selectedStations.some((s) => s.id === station.id)
                    : selectedStations.length > 0 &&
                      selectedStations[0].id === station.id,
                  'station-hot': station.isHot === 1,
                  'station-exit-port': station.isExitPort === 1,
                }"
                @click="selectStation(station)"
              >
                <div class="station-header">
                  <div class="station-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <rect width="16" height="6" x="2" y="3" rx="1"></rect>
                      <path d="m7 10 1.5 1.5L7 13"></path>
                      <path d="m17 10-1.5 1.5L17 13"></path>
                      <path d="M8 3v4"></path>
                      <path d="M16 3v4"></path>
                      <path d="M8 18h.01"></path>
                      <path d="M16 18h.01"></path>
                      <rect width="16" height="5" x="2" y="14" rx="1"></rect>
                    </svg>
                  </div>
                  <div class="station-badges">
                    <span v-if="station.isHot === 1" class="badge badge-hot">{{
                      t("member.station.stationHot")
                    }}</span>
                    <span
                      v-if="station.isExitPort === 1"
                      class="badge badge-exit"
                      >{{ t("member.station.stationExit") }}</span
                    >
                  </div>
                </div>
                <div class="station-info">
                  <h4 class="station-name">{{ station.stationNameZh }}</h4>
                  <p class="station-name-en">{{ station.stationNameEn }}</p>
                  <div class="station-details">
                    <div
                      class="station-city"
                      v-if="station.cityNameZh || station.cityNameEn"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"
                        ></path>
                        <circle cx="12" cy="10" r="3"></circle>
                      </svg>
                      <span>{{
                        station.cityNameZh || station.cityNameEn
                      }}</span>
                    </div>
                    <div
                      class="station-coordinates"
                      v-if="station.longitude && station.latitude"
                    >
                      <span class="coordinates-text"
                        >{{ station.longitude.toFixed(2) }},
                        {{ station.latitude.toFixed(2) }}</span
                      >
                    </div>
                  </div>
                </div>
                <div
                  class="station-check"
                  v-if="
                    isMultiSelectMode
                      ? selectedStations.some((s) => s.id === station.id)
                      : selectedStations.length > 0 &&
                        selectedStations[0].id === station.id
                  "
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-container">
              <div class="loading-spinner"></div>
              <p class="loading-text">{{ t("member.station.loading") }}</p>
            </div>

            <!-- 无结果状态 -->
            <div
              v-if="!isLoading && filteredStations.length === 0"
              class="no-results"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="48"
                height="48"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="no-results-icon"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <p class="no-results-text">{{ t("member.station.noResults") }}</p>
              <p class="no-results-hint">
                {{ t("member.station.noResultsHint") }}
              </p>
            </div>

            <!-- 添加已选站点显示区域 -->
            <div
              class="selected-stations-summary"
              v-if="selectedStations.length > 0 && isMultiSelectMode"
            >
              <div class="summary-header">
                <h4 class="summary-title">
                  {{ t("member.station.selectedStations") || "已选站点" }} ({{
                    selectedStations.length
                  }})
                </h4>
                <span class="total-price"
                  >{{ t("member.station.totalPrice") || "总价" }}: ¥{{
                    totalStationPrice.toFixed(2)
                  }}</span
                >
              </div>
              <div class="selected-stations-list">
                <div
                  v-for="station in selectedStations"
                  :key="station.id"
                  class="selected-station-item"
                >
                  <span class="station-name">{{ station.stationNameZh }}</span>
                  <button
                    class="remove-station"
                    @click.stop="removeSelectedStation(station)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 添加单选模式的选中站点显示 -->
            <div
              class="selected-station-single"
              v-if="selectedStations.length > 0 && !isMultiSelectMode"
            >
              <div class="summary-header">
                <h4 class="summary-title">
                  {{ t("member.station.selectedStation") || "已选站点" }}
                </h4>
                <span class="station-price"
                  >¥{{ stationPrice.toFixed(2) }}</span
                >
              </div>
              <div class="selected-station-info">
                <div class="selected-station-name">
                  {{ selectedStations[0].stationNameZh }}
                </div>
                <div
                  class="selected-station-details"
                  v-if="
                    selectedStations[0].cityNameZh ||
                    selectedStations[0].cityNameEn
                  "
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"
                    ></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <span>{{
                    selectedStations[0].cityNameZh ||
                    selectedStations[0].cityNameEn
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-button primary-button"
            :disabled="selectedStations.length === 0"
            @click="confirmStationSelection"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="check-icon"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            {{ t("member.station.confirm") }}
          </button>
          <button
            class="action-button secondary-button"
            @click="showStationSelector = false"
          >
            {{ t("member.station.cancel") }}
          </button>
        </div>
      </div>
    </div>

    <!-- 微信支付二维码弹窗 -->
    <div v-if="showWxQRCode" class="modal-container">
      <div class="modal-backdrop" @click="closeWxQRCode"></div>
      <div class="modal-content payment-modal">
        <div class="modal-header">
          <h3 class="modal-title">{{ t("member.wxpay.title") }}</h3>
          <button class="close-button" @click="closeWxQRCode">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <div class="wx-qrcode-container">
            <img
              v-if="wxQRCodeUrl"
              :src="wxQRCodeUrl"
              alt="微信支付二维码"
              class="wx-qrcode-img"
            />
            <p class="wx-qrcode-tip">{{ t("member.wxpay.scanTip") }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 银行卡对公付款信息弹窗 -->
    <div v-if="showBankTransferModal" class="modal-container">
      <div class="modal-backdrop" @click="closeBankTransferModal"></div>
      <div class="modal-content bank-transfer-modal">
        <div class="modal-header">
          <h3 class="modal-title">
            {{ t("member.payment.bankTransfer.title") }}
          </h3>
          <button class="close-button" @click="closeBankTransferModal">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <div class="bank-transfer-info">
            <div class="info-row">
              <span class="info-label"
                >{{ t("member.payment.bankTransfer.companyName") }}：</span
              >
              <span class="info-value">{{ yinhan.companyName }}</span>
            </div>
            <div class="info-row">
              <span class="info-label"
                >{{ t("member.payment.bankTransfer.bankName") }}：</span
              >
              <span class="info-value">{{ yinhan.bankName }}</span>
            </div>
            <div class="info-row">
              <span class="info-label"
                >{{ t("member.payment.bankTransfer.accountNumber") }}：</span
              >
              <span class="info-value">{{ yinhan.bankAccount }}</span>
            </div>
            <div class="info-row">
              <span class="info-label"
                >{{ t("member.payment.bankTransfer.transferAmount") }}：</span
              >
              <span class="info-value price-highlight"
                >¥{{ calculateTotal().toFixed(2) }}</span
              >
            </div>
            <div class="transfer-note">
              <p class="note-title">转账须知：</p>
              <ol class="note-list">
                <li v-for="item in yinhan.corpPaymentNoticeList" :key="item.id">
                  {{ item.content }}
                </li>
              </ol>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-button primary-button"
            @click="contactCustomerService"
          >
            联系客服
          </button>
          <button
            class="action-button secondary-button"
            @click="closeBankTransferModal"
          >
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 聊天窗口组件 -->
    <ChatModal
      ref="chatModalRef"
      :is-visible="chatModalStore.visible"
      @close="chatModalStore.close"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import "../styles/member.css";
import { useI18n } from "../composables/useI18n";
import { useUserStore } from "@/store/user";
import { useChatModalStore } from "@/store/chatModal";
import { getCouponList } from "@/api/voucher";
import { getMemberList } from "@/api/member";
import { listStation } from "@/api/maps";
import {
  putinvalidateOrder,
  getmembertype,
  getUserMemberExpiryTime,
  getYnghan,
} from "@/api/pay";
import { ElMessage } from "element-plus";
import ChatModal from "@/components/chat_modal.vue";
const yinhan = ref({});
// 获取国际化函数
const { t } = useI18n();

// 会员状态
const currentMembership = ref(null);

// 支付相关
const userStore = useUserStore();
const chatModalStore = useChatModalStore();

// 优惠券相关
const showCouponList = ref(false);
const selectedCoupon = ref(null);

// 会员数据
const memberData = ref([]);

// 站点选择相关
const showStationSelector = ref(false);
const selectedStations = ref([]);
const searchQuery = ref("");
const availableStations = ref([]);
const isLoading = ref(false);

// 微信支付相关
const showWxQRCode = ref(false);
const wxQRCodeUrl = ref("");
const orderId = ref("");
const paymentInterval = ref(null);
const paymentTimeout = ref(null);

// 添加银行卡对公付款信息弹窗的ref
const showBankTransferModal = ref(false);

// 会员套餐区域的ref
const membershipPlansSection = ref(null);

// 添加一个变量标记当前的选择模式
const isMultiSelectMode = ref(false);

// 获取车站列表
const loadStations = async () => {
  isLoading.value = true;
  try {
    const response = await listStation();
    if (response.code === 200) {
      availableStations.value = response.rows;
    } else {
      console.error("获取车站列表失败:", response.msg);
    }
  } catch (error) {
    console.error("获取车站列表失败:", error);
  } finally {
    isLoading.value = false;
  }
};
const isRenewalMode = ref(false);
// 热门站点
const hotStations = computed(() => {
  return availableStations.value
    .filter((station) => station.isHot === 1)
    .slice(0, 8);
});

// 过滤车站
const filteredStations = computed(() => {
  if (!searchQuery.value) {
    return availableStations.value;
  }
  const query = searchQuery.value.toLowerCase();
  return availableStations.value.filter(
    (station) =>
      (station.stationNameZh &&
        station.stationNameZh.toLowerCase().includes(query)) ||
      (station.stationNameEn &&
        station.stationNameEn.toLowerCase().includes(query)) ||
      (station.cityNameZh &&
        station.cityNameZh.toLowerCase().includes(query)) ||
      (station.cityNameEn && station.cityNameEn.toLowerCase().includes(query))
  );
});

// 选择车站
const selectStation = (station) => {
  if (isMultiSelectMode.value) {
    // 多选模式
    const index = selectedStations.value.findIndex((s) => s.id === station.id);
    if (index === -1) {
      // 站点不在选中列表中，添加
      selectedStations.value.push(station);
    } else {
      // 站点已在选中列表中，移除
      selectedStations.value.splice(index, 1);
    }
  } else {
    // 单选模式
    // 清空之前的选择
    selectedStations.value = [];
    // 添加新选择的站点
    selectedStations.value.push(station);
  }
};

// 确认车站选择
const confirmStationSelection = async () => {
  if (selectedStations.value.length === 0) {
    ElMessage.warning("请选择至少一个站点");
    return;
  }

  showStationSelector.value = false;

  // 如果是从会员激活流程进入的，需要包含会员费用
  if (!isMember.value) {
    // 非会员，设置为非续费模式（首次开通会员）
    isRenewalMode.value = false;
    showPaymentModal.value = true;
    return;
  }

  // 如果已经是会员，只需支付站点费用，不需要会员费用
  // 确保设置为非续费模式
  isRenewalMode.value = false;

  // 加载优惠券
  await loadCoupons();
  showPaymentModal.value = true;
};

// 可用优惠券列表
const availableCoupons = ref([]);
const loadCoupons = async () => {
  try {
    const userId = userStore.userInfo.userId;

    // 检查用户是否登录
    if (!userStore.token || !userId) {
      ElMessage.warning(t("userInfo.vouchers.loginRequired"));
      return;
    }

    const response = await getCouponList(userId);
    if (response.code === 200) {
      // Filter vouchers: only include those with isUsed=0 (available for use)
      const validCoupons = response.data.filter(
        (coupon) => coupon.isUsed === 0
      );

      availableCoupons.value = validCoupons.map((coupon) => ({
        id: coupon.id,
        name: coupon.name || t("member.payment.coupon.available"),
        description: coupon.description || t("member.benefits.prioritySupport"),
        amount: coupon.amount || 0,
        expiryDate: coupon.expiryTime,
        isUsed: coupon.isUsed,
        validDays: coupon.validDays,
      }));

      // 按金额从大到小排序
      availableCoupons.value.sort((a, b) => b.amount - a.amount);
    }
  } catch (error) {
    console.error("获取优惠券失败:", error);
  }
};

// 在 mounted 时获取会员数据和车站数据
onMounted(async () => {
  try {
    const response = await getMemberList();
    if (response.code === 200) {
      memberData.value = response.rows;
    }
  } catch (error) {
    console.error("Failed to load member data:", error);
  }
  // userStore.setmembertypeAction('超级会员')
  // userStore.getUserInfoAction();
  // 加载车站数据
  await loadStations();
  const resrule2 = await getUserMemberExpiryTime();
  //拿取数组的最后一个
  userStore.setmemberExpireTimeAction(resrule2.data[resrule2.data.length - 1]?.endTime);
  const resrule3 = await getYnghan();
  yinhan.value = resrule3.data;
  const resrule4 = await getmembertype();
  userStore.setmembertypeAction(resrule4.data);

  // 检查URL参数，如果有action=selectStation或showStationSelector=true且用户是会员，则自动打开站点选择对话框
  const urlParams = new URLSearchParams(window.location.search);
  const action = urlParams.get('action');
  const showSelector = urlParams.get('showStationSelector');
  
  if ((action === 'selectStation' || showSelector === 'true') && isMember.value) {
    // 延迟一点执行，确保数据都已加载完成
    setTimeout(() => {
      isMultiSelectMode.value = true;
      showStationSelector.value = true;
    }, 300);
  }
});

// 计算属性：获取普通会员和超级会员数据
const regularMember = computed(() =>
  memberData.value.find((member) => member.type === "普通会员")
);

const premiumMember = computed(() =>
  memberData.value.find((member) => member.type === "超级会员")
);

// 格式化权益文本，处理换行符
const formatBenefitText = (text) => {
  if (!text) return '';

  // 添加调试信息
  console.log('原始文本:', text);
  console.log('文本包含的字符:', text.split('').map(char => char.charCodeAt(0)));

  // 处理多种可能的换行符格式
  const formatted = text
    .replace(/\\n/g, '<br>')  // 处理 \n
    .replace(/\/n/g, '<br>')  // 处理 /n
    .replace(/\n/g, '<br>')   // 处理实际的换行符
    .replace(/\r\n/g, '<br>') // 处理 Windows 换行符
    .replace(/\r/g, '<br>');  // 处理 Mac 换行符

  console.log('格式化后:', formatted);
  return formatted;
};

// 计算总站点价格
const totalStationPrice = computed(() => {
  // 确保每个站点价格并且总价都是两位小数
  const total = selectedStations.value.reduce(
    (sum, station) => sum + stationPrice.value,
    0
  );
  // 使用parseFloat和toFixed保证最多两位小数
  return parseFloat(total.toFixed(2));
});

// 修改计算总金额的方法，考虑多个站点和会员状态
const calculateTotal = () => {
  // 如果是续费模式，总是包含会员费用；如果不是续费模式，只对非会员收取会员费用
  const basePrice = isRenewalMode.value
    ? regularMember.value?.price ?? 0
    : isMember.value
    ? 0
    : regularMember.value?.price ?? 0;
  // 站点总费用
  const stationsTotal = totalStationPrice.value;
  // 优惠券折扣
  const discount = selectedCoupon.value ? selectedCoupon.value.amount : 0;
  // 总价 = 会员费用 + 站点费用 - 优惠券金额
  const total = basePrice + stationsTotal - discount;
  return parseFloat(total.toFixed(2));
};

// 切换优惠券列表显示
const toggleCouponList = () => {
  showCouponList.value = !showCouponList.value;
};

// 选择优惠券
const selectCoupon = (coupon) => {
  // 只有 isUsed 为 0 的券才能被选择
  if (coupon && coupon.isUsed !== 0) {
    ElMessage.warning(t("member.payment.coupon.invalid"));
    return;
  }

  selectedCoupon.value = coupon;
  showCouponList.value = false;
};

// 关闭支付弹窗时重置状态
const resetPaymentModalState = () => {
  showPaymentModal.value = false;
  // 重置支付相关状态，避免状态残留
  if (!isRenewalMode.value) {
    isMultiSelectMode.value = false;
  }
};

// 处理支付 - 点击确认支付按钮时调用
const showPaymentModal = ref(false);
const paymentMethod = ref("wechat");
const processPayment = async () => {
  // 关闭支付弹窗并重置状态
  resetPaymentModalState();

  // 处理不同支付方式
  if (paymentMethod.value === "alipay") {
    // 支付宝支付: 提示截图联系客服开通
    ElMessage({
      message: t("member.payment.alipay.prompt"),
      type: "info",
      duration: 8000,
      showClose: true,
    });
    // 联系客服开通
    contactCustomerService();
    return;
  } else if (paymentMethod.value === "card") {
    // 银行卡支付: 弹窗展示对公付款信息
    showBankTransferModal.value = true;
    return;
  }

  try {
    // 微信支付流程保持不变
    // 创建订单 - 修改为根据不同场景计算不同的价格
    const stations = selectedStations.value;
    const stationNames = stations.map((s) => s.stationNameZh).join(", ");

    // 根据是否已经是会员调整订单标题和金额
    const orderData = {
      // 如果已经是会员，则只购买站点；否则是开通会员
      title: isMember.value
        ? `站点购买 - ${stationNames}`
        : `${regularMember.value.type}开通 - ${stationNames}`,
      // 总金额计算 - 保留两位小数
      amount: parseFloat(calculateTotal().toFixed(2)),
      // 如果是会员，站点费用可能需要单独考虑 - 保留两位小数
      stationAmount: parseFloat(totalStationPrice.value.toFixed(2)), // 站点费用
      // 如果已经是会员，则会员金额为0
      memberAmount: isMember.value
        ? 0
        : parseFloat((regularMember.value?.price ?? 0).toFixed(2)), // 会员费用
      stationIds: stations.map((s) => s.id),
      userCouponId: selectedCoupon.value ? selectedCoupon.value.id : null,
      isRenewal: isRenewalMode.value,
    };

    const { createWechatOrder, createWechatQrcode } = await import("@/api/pay");
    const orderResponse = await createWechatOrder(orderData);

    if (orderResponse.code === 200) {
      const orderNo = orderResponse.data;
      orderId.value = orderNo;

      // 获取支付二维码
      const qrcodeResponse = await createWechatQrcode(orderNo);

      if (qrcodeResponse.code === 200) {
        // 显示二维码
        const QRCode = (await import("qrcode")).default;
        const qrcodeUrl = qrcodeResponse.msg;

        try {
          // 生成二维码的 base64 图像数据
          wxQRCodeUrl.value = await QRCode.toDataURL(qrcodeUrl);
          showWxQRCode.value = true;

          // 开始轮询支付状态
          startCheckPaymentStatus(orderId.value);
        } catch (qrError) {
          console.error("生成二维码失败:", qrError);
          alert(t("member.wxpay.generateFailed"));
        }
      } else {
        alert(t("member.wxpay.getFailed"));
      }
    } else {
      alert(orderResponse.msg || t("member.wxpay.createOrderFailed"));
    }
  } catch (error) {
    console.error("支付请求失败:", error);
    alert(t("member.wxpay.requestFailed"));
  }
};

// 添加检查支付状态的函数
const startCheckPaymentStatus = (orderNo) => {
  // 清除可能存在的之前的轮询
  if (paymentInterval.value) {
    clearInterval(paymentInterval.value);
    paymentInterval.value = null;
  }

  // 清除可能存在的之前的超时
  if (paymentTimeout.value) {
    clearTimeout(paymentTimeout.value);
    paymentTimeout.value = null;
  }

  // 保存intervalId以便清除
  paymentInterval.value = setInterval(async () => {
    // 如果二维码弹窗已关闭，停止轮询
    if (!showWxQRCode.value) {
      clearInterval(paymentInterval.value);
      paymentInterval.value = null;
      return;
    }

    try {
      const { queryPayStatus } = await import("@/api/pay");
      const response = await queryPayStatus(orderNo);

      if (response.code === 200) {
        clearInterval(paymentInterval.value);
        paymentInterval.value = null;

        // 系统关闭，无需确认
        showWxQRCode.value = false;
        wxQRCodeUrl.value = "";

        alert("支付成功！");
        // 刷新用户信息
        const resrule = await getmembertype();
        userStore.setmembertypeAction(resrule.data);
        const resrule2 = await getUserMemberExpiryTime();
        userStore.setmemberExpireTimeAction(resrule2.data[resrule2.data.length - 1]?.endTime)

        userStore.getUserInfoAction();
        //重定向到该页面
        window.location.reload();
      }
    } catch (error) {
      console.error("检查支付状态失败:", error);
    }
  }, 5000); // 每5秒检查一次

  // 5分钟后自动停止轮询
  paymentTimeout.value = setTimeout(() => {
    if (paymentInterval.value) {
      clearInterval(paymentInterval.value);
      paymentInterval.value = null;

      // 如果二维码仍在显示，且未支付，提示超时
      if (showWxQRCode.value) {
        // 系统关闭，无需确认
        showWxQRCode.value = false;
        wxQRCodeUrl.value = "";

        alert(t("member.wxpay.timeout"));

        // 支付超时时也调用订单失效接口
        invalidateOrder(orderNo);
      }
    }

    paymentTimeout.value = null;
  }, 300000); // 5分钟超时
};

// 关闭二维码弹窗
const closeWxQRCode = () => {
  // 如果是用户主动关闭，显示确认弹窗
  if (showWxQRCode.value && orderId.value) {
    if (confirm(t("member.wxpay.closeConfirm"))) {
      // 用户确认关闭，调用订单失效接口
      invalidateOrder(orderId.value);

      // 清理轮询
      if (paymentInterval.value) {
        clearInterval(paymentInterval.value);
        paymentInterval.value = null;
      }

      // 清理超时
      if (paymentTimeout.value) {
        clearTimeout(paymentTimeout.value);
        paymentTimeout.value = null;
      }

      // 关闭弹窗
      showWxQRCode.value = false;
      wxQRCodeUrl.value = "";
    }
    // 如果用户取消，则不做任何操作，保持弹窗打开
  } else {
    // 如果是系统自动关闭（如支付成功或超时），直接关闭
    if (paymentInterval.value) {
      clearInterval(paymentInterval.value);
      paymentInterval.value = null;
    }

    if (paymentTimeout.value) {
      clearTimeout(paymentTimeout.value);
      paymentTimeout.value = null;
    }

    showWxQRCode.value = false;
    wxQRCodeUrl.value = "";
  }
};

// 订单失效接口调用 - 确保支付失败时不消费优惠券
const invalidateOrder = async (orderNo) => {
  try {
    // 获取站点IDs
    const stationIdsStr = localStorage.getItem("stationIds");
    const stationIds = stationIdsStr ? JSON.parse(stationIdsStr) : [];

    // 调用API
    const response = await putinvalidateOrder(orderNo, stationIds);

    if (response.code === 200) {
      console.log("订单已成功失效");
    } else {
      console.error("订单失效请求失败:", response.msg);
    }
  } catch (error) {
    console.error("调用订单失效接口失败:", error);
  }
};

// 联系客服 - 打开聊天对话框
const contactCustomerService = async () => {
  // 检查用户是否已登录
  if (!userStore.isLogin) {
    // 用户未登录，显示登录对话框
    userStore.showLoginDialog = true;
    return;
  }

  try {
    const { getRandomCustomer } = await import("@/api/pusher");
    const res = await getRandomCustomer();
    if (res.code === 200 && res.data) {
      // 将客服信息转换为聊天所需的格式
      const customerService = {
        id: res.data.userId,
        name: res.data.nickName || res.data.userName || "客服",
        avatar:
          res.data.avatar ||
          "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/3825398873.jpg",
        phonenumber: res.data.phonenumber,
        introduction: res.data.introduction || "欢迎咨询，我是您的专属客服",
        companyName: res.data.companyName,
        memberType: "在线客服",
      };

      // 使用store打开聊天模态框
      chatModalStore.open(customerService);
    } else {
      console.error("获取客服信息失败");
    }
  } catch (error) {
    console.error("获取客服信息出错:", error);
  }
};

// 返回首页
const goToHome = () => {
  // 这里添加返回首页的逻辑，例如路由跳转
  window.location.href = "/";
  // 如果使用 Vue Router，可以使用:
  // router.push('/')
};

// 修改会员状态计算属性
const membershipStatus = computed(() => {
  const memberType = userStore.userInfo?.memberType;
  if (memberType === "超级会员") {
    const member = premiumMember.value;
    return {
      type: "super",
      name: t("member.premiumMember"),
      expiryDate: userStore.userInfo?.memberExpireTime || "2099-12-31",
      benefits: member?.memberDescribeList || [],
    };
  } else if (memberType === "普通会员") {
    const member = regularMember.value;
    return {
      type: "regular",
      name: t("member.regularMember"),
      expiryDate: userStore.userInfo?.memberExpireTime || "2099-12-31",
      benefits: member?.memberDescribeList || [],
    };
  }
  return null;
});

// 判断是否显示续费或激活文本
const getButtonText = computed(() => {
  if (membershipStatus.value?.type === "regular") {
    return t("member.buttons.renew") || "续费";
  } else if (membershipStatus.value?.type === "super") {
    return t("member.alreadyActivated");
  }
  return t("member.buttons.activate");
});

// 修改激活/续费按钮点击处理
const handleActivateClick = async () => {
  // 超级会员不能通过此按钮续费
  if (membershipStatus.value?.type === "super") {
    ElMessage({
      message: t("member.premiumRenewalNotice"),
      type: "info",
      duration: 3000,
    });
    return;
  }

  // 先加载优惠券
  await loadCoupons();

  // 检查是否是续费操作
  const isRenewal = membershipStatus.value?.type === "regular";

  if (isRenewal) {
    // 设置为续费模式，这样计算总价时会包含会员费用
    isRenewalMode.value = true;
    // 重要：确保设置为非多选模式，避免标题混淆
    isMultiSelectMode.value = false;
    // 续费操作，直接显示支付弹窗，不需要选择站点
    showPaymentModal.value = true;
  } else {
    // 新开通会员，不是续费模式
    isRenewalMode.value = false;
    // 设置为单选模式
    isMultiSelectMode.value = false;
    // 清空之前选择的站点
    selectedStations.value = [];
    // 显示站点选择弹窗
    showStationSelector.value = true;
  }
};

// 检查用户是否是会员（普通会员或超级会员）
const isMember = computed(() => {
  const memberType = userStore.userInfo?.memberType;
  return memberType === "普通会员" || memberType === "超级会员";
});

// 处理选择站点按钮点击
const handleStationSelectorClick = () => {
  if (!isMember.value) {
    ElMessage({
      message: "请先成为会员后再选择站点",
      type: "warning",
      duration: 3000,
    });
    return;
  }

  // 设置为多选模式
  isMultiSelectMode.value = true;
  showStationSelector.value = true;
};

// 在组件卸载时清理资源
onUnmounted(() => {
  // 清理轮询
  if (paymentInterval.value) {
    clearInterval(paymentInterval.value);
    paymentInterval.value = null;
  }

  // 清理超时
  if (paymentTimeout.value) {
    clearTimeout(paymentTimeout.value);
    paymentTimeout.value = null;
  }

  // 如果有未完成的订单，调用失效接口
  if (showWxQRCode.value && orderId.value) {
    invalidateOrder(orderId.value);
  }
});

// 添加站点价格计算属性
const stationPrice = computed(() => {
  const memberType = userStore.userInfo?.memberType;
  if (memberType === "超级会员") {
    return premiumMember.value?.stationPrice || 0;
  } else if (memberType === "普通会员") {
    return regularMember.value?.stationPrice || 0;
  }
  return 0;
});

// 格式化日期函数
const formatDate = (dateString) => {
  if (!dateString) return "";

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // 如果日期无效，直接返回原字符串

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")}`;
};

// 关闭银行转账信息弹窗
const closeBankTransferModal = () => {
  showBankTransferModal.value = false;
};

// 添加移除选中站点的方法
const removeSelectedStation = (station) => {
  const index = selectedStations.value.findIndex((s) => s.id === station.id);
  if (index !== -1) {
    selectedStations.value.splice(index, 1);
  }
};

// 清空选中站点
const clearSelectedStations = () => {
  selectedStations.value = [];
};

// 滚动到会员套餐部分
const scrollToMembershipPlans = () => {
  if (membershipPlansSection.value) {
    membershipPlansSection.value.scrollIntoView({ behavior: "smooth" });
  }
};
</script>

<style scoped>
/* 新增多选提示样式 */
.multi-select-notice {
  background: #4caf50;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  font-size: 0.9em;
  font-weight: bold;
}

/* 新增站点数量标签样式 */
.station-count {
  background: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
  font-size: 0.8em;
  font-weight: bold;
}

/* 已选站点摘要区域样式 */
.selected-stations-summary {
  margin-top: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background-color: #f9f9f9;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.summary-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.total-price {
  font-size: 1rem;
  font-weight: 600;
  color: #f59e0b;
}

.selected-stations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-station-item {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 4px 8px 4px 12px;
  font-size: 0.9rem;
}

.remove-station {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 2px;
  margin-left: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-station:hover {
  color: #f56565;
  background: #f8f8f8;
}

.wx-qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
}

.wx-qrcode-img {
  width: 200px;
  height: 200px;
  margin-bottom: 1rem;
}

.wx-qrcode-tip {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
}

/* 设置站点选择弹窗样式 */
.station-selector-modal {
  display: flex;
  flex-direction: column;
  max-width: 85%;
  width: 650px;
  max-height: 85vh;
  height: auto;
}

.station-selector-modal .modal-body {
  flex: 1;
  overflow-y: auto;
}

.station-selector-modal .modal-footer {
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.stations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 10px;
}

.station-card {
  padding: 10px;
}

.hot-stations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
}

/* 添加禁用按钮样式 */
.disabled-button {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 添加站点价格样式 */
.station-price {
  margin-left: 5px;
  font-size: 0.9em;
  color: #f59e0b;
  font-weight: bold;
}

.station-fee-notice {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  font-size: 0.9em;
  font-weight: bold;
}

/* 添加优惠券样式 */
.coupon-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.coupon-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.coupon-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coupon-item-name {
  font-weight: 500;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.coupon-item-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.coupon-item-expiry {
  color: #999;
  font-size: 12px;
}

.coupon-item-amount {
  font-size: 18px;
  font-weight: bold;
  color: #f5222d;
  margin: 0 10px;
}

.coupon-item-check {
  width: 20px;
  height: 20px;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 优惠券状态样式 */
.coupon-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.coupon-status-available {
  background-color: #e6f7ff;
  color: #1890ff;
}

.coupon-status-used {
  background-color: #f5f5f5;
  color: #999;
}

.coupon-status-expired {
  background-color: #fff1f0;
  color: #ff4d4f;
}

@media screen and (max-width: 768px) {
  .modal-content.station-selector-modal {
    width: 95%;
    max-width: 95%;
  }

  .modal-content.station-selector-modal .modal-footer {
    padding: 12px;
  }

  .stations-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .selected-stations-list {
    flex-direction: column;
    gap: 6px;
  }
}

@media screen and (max-height: 600px) {
  .modal-content.station-selector-modal {
    max-height: 95vh;
  }

  .modal-content.station-selector-modal .modal-header {
    padding: 12px;
  }

  .modal-content.station-selector-modal .modal-footer {
    padding: 10px;
  }
}

/* 银行转账模态框样式 */
.bank-transfer-modal {
  max-width: 500px;
  width: 90%;
  padding: 0;
}

.bank-transfer-info {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.info-label {
  min-width: 100px;
  width: auto;
  padding-right: 10px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.info-value {
  flex: 1;
  font-weight: 500;
}

.price-highlight {
  color: #f5222d;
  font-size: 18px;
  font-weight: bold;
}

.transfer-note {
  margin-top: 20px;
  padding: 15px;
  background-color: #f6f9ff;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.note-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #1890ff;
}

.note-list {
  padding-left: 20px;
  margin: 0;
}

.note-list li {
  margin-bottom: 8px;
  color: #444;
  line-height: 1.5;
}

.note-list li:last-child {
  margin-bottom: 0;
}

.payment-stations {
  margin: 10px 0;
  border-left: 2px solid #e0e0e0;
  padding-left: 10px;
}

.payment-station-header {
  margin-bottom: 8px;
}

.payment-station-list {
  padding-left: 10px;
}

.payment-station-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.9em;
  color: #666;
  padding: 4px 0;
}

.payment-station-item .station-name {
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 单选模式提示样式 */
.single-select-notice {
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  font-size: 0.9em;
  font-weight: bold;
}

/* 单选模式已选站点样式 */
.selected-station-single {
  margin-top: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background-color: #f0f7ff;
}

.selected-station-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.selected-station-details {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 0.9rem;
}

.selected-station-info {
  margin-top: 8px;
}

/* Add vertical layout styles for both benefit lists */
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 20px 0;
  padding: 0;
  list-style-type: none;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.benefit-text {
  line-height: 1.5;
  flex: 1;
}

.check-icon,
.check-icon-premium {
  flex-shrink: 0;
}
</style>

