<template>
  <div class="distance-tool">
    <button class="distance-btn" @click="toggleMeasure" :class="{ active: measuring }" title="测量工具">
      <span class="icon">📏</span>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  map: Object
});

const measuring = ref(false);
const points = ref([]);
const polyline = ref(null);
const markers = ref([]); // 新增:存储标记点

// 修改测距开关逻辑
const toggleMeasure = () => {
  measuring.value = !measuring.value;
};

// 使用 watch 来监听 measuring 的变化
watch(measuring, (newValue) => {

  
  if (newValue) {
    startMeasuring();
  } else {
    stopMeasuring();
  }
});

// 修改开始测距逻辑
const startMeasuring = () => {

  // 重置状态
  points.value = [];
  polyline.value = null;
  
  // 修改鼠标样式
  props.map.getContainer().style.cursor = 'crosshair';
  
  // 使用延时确保不会立即触发点击事件
  setTimeout(() => {
    props.map.on('click', handleMapClick);
  }, 100);
};

// 修改停止测距逻辑
const stopMeasuring = () => {
  if (!props.map) return;
  props.map.getContainer().style.cursor = '';
  props.map.off('click', handleMapClick);
  clearMeasurement();
};

// 修改地图点击处理逻辑
const handleMapClick = (e) => {
  const point = [e.latlng.lat, e.latlng.lng];
  points.value.push(point);
  
  // 添加标记点
  const marker = L.marker(point, {
    icon: L.divIcon({
      className: 'measure-point',
      html: `<div class="point-icon">${points.value.length}</div>`,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })
  }).addTo(props.map);
  
  markers.value.push(marker);
  
  // 有两个及以上的点时才画线
  if (points.value.length > 1) {
    updateLine();
  }
};

const updateLine = () => {
  const distance = calculateDistance();
  if (polyline.value) {
    props.map.removeLayer(polyline.value);
  }

  polyline.value = L.polyline(points.value, {
    color: '#1890ff',
    weight: 3
  }).addTo(props.map);

  polyline.value.bindTooltip(`${distance.toFixed(2)} km`, {
    permanent: true,
    direction: 'center'
  });
};

const calculateDistance = () => {
  let total = 0;
  for (let i = 1; i < points.value.length; i++) {
    const p1 = points.value[i - 1];
    const p2 = points.value[i];
    total += getDistanceBetweenPoints(p1, p2);
  }
  return total;
};

const getDistanceBetweenPoints = (p1, p2) => {
  const R = 6371; // 地球半径，单位km
  const dLat = (p2[0] - p1[0]) * Math.PI / 180;
  const dLon = (p2[1] - p1[1]) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(p1[0] * Math.PI / 180) * Math.cos(p2[0] * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// 修改清除测量功能
const clearMeasurement = () => {
  if (polyline.value) {
    props.map.removeLayer(polyline.value);
  }
  // 清除所有标记点
  markers.value.forEach(marker => {
    props.map.removeLayer(marker);
  });
  markers.value = [];
  points.value = [];
  polyline.value = null;
};

// 自动清理
onMounted(() => {
  return () => {
    if (measuring.value) {
      stopMeasuring();
    }
  };
});
</script>

<style scoped>
.distance-tool {
  position: absolute;
  right: 0px;
  top: 100px;
  z-index: 99;
}

.distance-btn {
  width: 30px;
  height: 30px;
  background: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
}

.distance-btn:hover {
  background: #f5f5f5;
}

/* 修改激活状态样式 */
.distance-btn.active {
  background: #1890ff;
  color: white;
}

.distance-btn.active:hover {
  background: #40a9ff;
}

.icon {
  font-size: 16px;
}

/* 添加测距点样式 */
:deep(.measure-point) {
  background: none;
}

:deep(.point-icon) {
  width: 24px;
  height: 24px;
  background: #1890ff;
  border: 2px solid white;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
