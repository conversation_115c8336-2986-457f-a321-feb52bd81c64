import { ref, onMounted, onUnmounted } from 'vue'

export function useOrientation() {
  const isLandscape = ref(false)
  
  const checkOrientation = () => {
    isLandscape.value = window.innerWidth > window.innerHeight
  }

  onMounted(() => {
    checkOrientation()
    window.addEventListener('resize', checkOrientation)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', checkOrientation)
  })

  return {
    isLandscape
  }
}
