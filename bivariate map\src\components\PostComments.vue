<template>
  <div class="post-comments">
    <div class="comments-header">
      <h3 class="comments-title">{{ t('comments.title') }} ({{ totalComments }})</h3>
    </div>

    <!-- 评论表单 -->
    <div class="comment-form">
      <div class="comment-avatar">
        <img :src="userAvatar" :alt="t('comments.anonymous')">
      </div>
      <div class="comment-input-container">
        <textarea v-model="commentText" class="comment-input" :placeholder="isLoggedIn ? t('comments.writeComment') : t('comments.loginFirst')"
          :disabled="!isLoggedIn || isSubmitting" maxlength="80"></textarea>
        <div class="form-footer">
          <div class="char-count" v-if="commentText.length > 0">{{ t('comments.charLimit', { current: commentText.length, max: 80 }) }}</div>
          <button class="submit-btn" :disabled="!isLoggedIn || !commentText.trim() || isSubmitting"
            @click="submitComment">
            <svg v-if="isSubmitting" class="loading-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            {{ isSubmitting ? t('comments.submitting') : t('comments.submit') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-if="comments.length > 0" class="comments-list">
      <div v-for="comment in comments" :key="comment.id" class="comment-item">
        <div class="comment-avatar">
          <img :src="getAvatarUrl(comment.avatar)" :alt="t('comments.anonymous')">
          <!-- 根据会员等级显示VIP徽章 -->
          <div v-if="isVipUser(comment, '普通会员')" class="vip-badge vip-regular">
            VIP
          </div>
          <div v-else-if="isVipUser(comment, '超级会员')" class="vip-badge vip-super">
            SVIP
          </div>
        </div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="commenter-name">{{ comment.nickName || t('comments.anonymous') }}</span>
            <span class="comment-time">{{ formatCommentTime(comment.createTime) }}</span>
          </div>
          <p class="comment-text">{{ comment.content }}</p>
          <div class="comment-actions">
            <button class="action-button reply-button" @click="replyToComment(comment)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path
                  d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z">
                </path>
              </svg>
              {{ t('comments.reply') }}
            </button>
            <button v-if="isCommentOwner(comment)" class="action-button delete-button" @click="confirmDeleteComment(comment)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
              {{ t('comments.delete') }}
            </button>
          </div>

          <!-- 子评论 -->
          <div v-if="comment.children && comment.children.length > 0" class="child-comments">
            <!-- 当子评论数量不超过3个或已展开时显示子评论 -->
            <div v-if="comment.children.length <= 3 || expandedComments[comment.id]">
              <div v-for="child in comment.children" :key="child.id" class="child-comment-item">
                <div class="comment-avatar">
                  <img :src="getAvatarUrl(child.avatar)" :alt="t('comments.anonymous')">
                  <!-- 根据会员等级显示VIP徽章 -->
                  <div v-if="isVipUser(child, '普通会员')" class="vip-badge vip-regular">
                    VIP
                  </div>
                  <div v-else-if="isVipUser(child, '超级会员')" class="vip-badge vip-super">
                    SVIP
                  </div>
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="commenter-name">{{ child.nickName || t('comments.anonymous') }}</span>
                    <span class="comment-time">{{ formatCommentTime(child.createTime) }}</span>
                  </div>
                  <p class="comment-text">
                    <span v-if="child.replyToNickName" class="reply-to-indicator">
                      {{ t('comments.replyTo', { name: child.replyToNickName || t('comments.anonymous') }) }}
                    </span>
                    {{ child.content }}
                  </p>
                  <div class="comment-actions">
                    <button class="action-button reply-button" @click="replyToComment(child)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path
                          d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z">
                        </path>
                      </svg>
                      {{ t('comments.reply') }}
                    </button>
                    <button v-if="isCommentOwner(child)" class="action-button delete-button" @click="confirmDeleteComment(child)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                      </svg>
                      {{ t('comments.delete') }}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 展开/收起按钮 -->
            <div v-if="comment.children.length > 3" class="child-comments-toggle">
              <button v-if="!expandedComments[comment.id]" class="toggle-button"
                @click="toggleChildComments(comment.id)">
                <span>{{ t('comments.showAllReplies', { count: comment.children.length }) }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <button v-else class="toggle-button" @click="toggleChildComments(comment.id)">
                <span>{{ t('comments.hideReplies') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="18 15 12 9 6 15"></polyline>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空评论状态 -->
    <div v-else class="empty-comments">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      </div>
      <p class="empty-text">{{ t('comments.noComments') }}</p>
    </div>

    <!-- 回复弹框 -->
    <div v-if="showReplyForm" class="reply-form-container">
      <div class="reply-form-overlay" @click="cancelReply"></div>
      <div class="reply-form">
        <div class="reply-form-header">
          <h4>{{ t('comments.replyToComment') }}</h4>
          <button class="close-button" @click="cancelReply">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <!-- 添加被回复者信息区域 -->
        <div class="reply-to-info">
          <div class="reply-to-avatar">
            <img :src="getAvatarUrl(replyTo.avatar)" :alt="t('comments.anonymous')">
            <!-- 根据会员等级显示VIP徽章 -->
            <div v-if="isVipUser(replyTo, '普通会员')" class="vip-badge vip-regular">
              VIP
            </div>
            <div v-else-if="isVipUser(replyTo, '超级会员')" class="vip-badge vip-super">
              SVIP
            </div>
          </div>
          <div class="reply-to-content">
            <div class="reply-to-name">{{ replyTo.nickName || t('comments.anonymous') }}</div>
            <div class="reply-to-text">{{ replyTo.content }}</div>
          </div>
        </div>

        <textarea v-model="replyText" class="reply-input" :placeholder="t('comments.writeReply')" ref="replyInput" maxlength="80"></textarea>
        <div class="reply-form-footer">
          <div class="char-count" v-if="replyText.length > 0">{{ t('comments.charLimit', { current: replyText.length, max: 80 }) }}</div>
          <div class="reply-buttons">
          <button class="cancel-button" @click="cancelReply">{{ t('comments.cancel') }}</button>
          <button class="submit-button" :disabled="!replyText.trim() || isSubmitting" @click="submitReply">
            <svg v-if="isSubmitting" class="loading-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            {{ isSubmitting ? t('comments.sending') : t('comments.reply') }}
          </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useUserStore } from '../store/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCommentList, postComment, removeComment } from '../api/comment';
import imageCache from '../utils/imageCache';
import { useI18n } from '../composables/useI18n';

const { t } = useI18n();

const props = defineProps({
  postId: {
    type: [String, Number],
    required: true
  }
});

// 获取用户信息
const userStore = useUserStore();
const isLoggedIn = computed(() => !!userStore.token);
const userId = computed(() => userStore.userInfo?.userId || '');
const baseUrl = import.meta.env.VITE_BASE_API || '';
const userAvatar = computed(() => {
  const avatar = userStore.userInfo?.avatar;
  if (!avatar) return '/image/default-avatar.png';
  if (avatar.startsWith('http')) return avatar;
  return `${baseUrl}${avatar}`;
});

// 评论状态
const comments = ref([]);
const totalComments = ref(0);
const commentText = ref('');
const isSubmitting = ref(false);
const showReplyForm = ref(false);
const replyTo = ref({});
const replyText = ref('');
const replyInput = ref(null);
const isLoading = ref(false);
const expandedComments = ref({});

// 获取评论列表
const fetchComments = async () => {
  if (!props.postId) {
    console.warn('帖子ID无效，无法获取评论列表');
    return;
  }

  // 防止重复加载
  if (isLoading.value) {
    console.log('正在加载评论，跳过重复请求');
    return;
  }

  try {
    isLoading.value = true;
    console.log('开始获取评论列表, 帖子ID:', props.postId);
    console.log('请求参数: targetId =', props.postId, ', targetType = article');

    // 直接传递参数调用API
    const response = await getCommentList(props.postId, 'article');

    if (response.code === 200) {
      // 从 response.rows 获取评论数据
      comments.value = response.rows || [];
      // 更新评论总数
      totalComments.value = response.total || comments.value.length;
      console.log('获取到评论列表:', comments.value.length, '条评论');
      
      // 输出评论数据结构，用于调试VIP标识
      if (comments.value.length > 0) {
        console.log('评论数据结构示例:', JSON.stringify(comments.value[0], null, 2));
      }
      
      // 处理评论数据，添加memberType字段
      comments.value = comments.value.map(comment => {
        // 根据实际情况，如果后端返回的字段名不是memberType，在这里转换
        // 例如：如果后端字段名是userType或vipLevel等
        if (!comment.memberType && comment.userType) {
          comment.memberType = comment.userType;
        }
        
        // 如果没有会员信息，根据avatar或其他字段判断是否为VIP
        if (!comment.memberType && (comment.isVip || comment.vipLevel > 0)) {
          comment.memberType = '普通会员';
        }
        
        // 为子评论也添加会员信息
        if (comment.children && comment.children.length > 0) {
          comment.children = comment.children.map(child => {
            if (!child.memberType && child.userType) {
              child.memberType = child.userType;
            }
            if (!child.memberType && (child.isVip || child.vipLevel > 0)) {
              child.memberType = '普通会员';
            }
            return child;
          });
        }
        
        return comment;
      });
    } else {
      console.error('获取评论失败:', response.msg);
      if (response.code !== 401) {
        ElMessage.warning('获取评论失败，请稍后再试');
      }
    }
  } catch (error) {
    console.error('获取评论出错:', error);
  } finally {
    isLoading.value = false;
  }
};

// 提交评论
const submitComment = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning(t('comments.loginFirst'));
    return;
  }

  if (!commentText.value.trim()) {
    ElMessage.warning(t('comments.emptyComment'));
    return;
  }

  try {
    isSubmitting.value = true;

    // 构建评论数据
    const commentData = {
      content: commentText.value.trim(),
      targetId: props.postId,
      targetType: 'article',
      userId: userId.value
    };

    console.log('提交评论:', commentData);
    const response = await postComment(commentData);

    if (response.code === 200) {
      ElMessage.success(t('comments.postSuccess'));
      commentText.value = '';
      fetchComments(); // 刷新评论列表
    } else {
      ElMessage.error(response.msg || t('comments.postFailed'));
    }
  } catch (error) {
    console.error('发表评论出错:', error);
    ElMessage.error(t('comments.postFailed'));
  } finally {
    isSubmitting.value = false;
  }
};

// 回复评论
const replyToComment = (comment) => {
  if (!isLoggedIn.value) {
    ElMessage.warning(t('comments.loginFirst'));
    return;
  }

  // 确保回复对象有正确的会员类型信息
  const processedComment = { ...comment };
  
  // 如果后端字段名不是memberType，这里进行转换
  if (!processedComment.memberType && processedComment.userType) {
    processedComment.memberType = processedComment.userType;
  }
  
  // 如果有其他会员相关字段，进行处理
  if (!processedComment.memberType && (processedComment.isVip || processedComment.vipLevel > 0)) {
    processedComment.memberType = '普通会员';
  }
  
  replyTo.value = processedComment;
  showReplyForm.value = true;
  replyText.value = '';

  console.log('正在回复评论，评论数据:', JSON.stringify(processedComment, null, 2));

  // 在下一个渲染周期后聚焦输入框
  nextTick(() => {
    if (replyInput.value) {
      replyInput.value.focus();
    }
  });
};

// 提交回复
const submitReply = async () => {
  if (!replyText.value.trim()) {
    ElMessage.warning('回复内容不能为空');
    return;
  }

  try {
    isSubmitting.value = true;

    // 构建回复数据
    const replyData = {
      content: replyText.value.trim(),
      targetId: props.postId,
      targetType: 'article',
      replyToNickName: replyTo.value.nickName || '匿名用户',
      userId: userId.value // 添加用户ID
    };

    // 添加父评论ID
    if (replyTo.value.id && replyTo.value.id !== 0) {
      replyData.parentId = replyTo.value.id;

      // 如果回复的是子评论，添加根评论ID
      if (replyTo.value.rootId) {
        replyData.rootId = replyTo.value.rootId;
      }
      // 如果回复的是父评论并且没有rootId，则以父评论ID作为rootId
      else if (replyTo.value.parentId === null && replyTo.value.id) {
        replyData.rootId = replyTo.value.id;
      }
    }

    console.log('提交回复:', replyData);
    const response = await postComment(replyData);

    if (response.code === 200) {
      ElMessage.success('回复发表成功');
      cancelReply();
      fetchComments(); // 刷新评论列表

      // 如果回复的是子评论，确保展开该评论的子评论列表
      if (replyData.rootId && replyData.rootId !== replyData.parentId) {
        expandedComments.value[replyData.rootId] = true;
      }
    } else {
      ElMessage.error(response.msg || '回复发表失败');
    }
  } catch (error) {
    console.error('发表回复出错:', error);
    ElMessage.error('回复发表失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 取消回复
const cancelReply = () => {
  showReplyForm.value = false;
  replyTo.value = {};
  replyText.value = '';
};

// 获取头像URL - 使用缓存服务
const getAvatarUrl = (avatar) => {
  return imageCache.getAvatarUrl(avatar, baseUrl);
};

// 格式化评论时间
const formatCommentTime = (timestamp) => {
  if (!timestamp) return '未知时间';

  const now = new Date();
  const commentTime = new Date(timestamp);
  const diffMs = now - commentTime;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  // 一分钟内
  if (diffSec < 60) {
    return '刚刚';
  }
  // 一小时内
  if (diffMin < 60) {
    return `${diffMin}分钟前`;
  }
  // 一天内
  if (diffHour < 24) {
    return `${diffHour}小时前`;
  }
  // 一周内
  if (diffDay < 7) {
    return `${diffDay}天前`;
  }

  // 超过一周显示具体日期
  const year = commentTime.getFullYear();
  const month = String(commentTime.getMonth() + 1).padStart(2, '0');
  const day = String(commentTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 展开/收起子评论
const toggleChildComments = (commentId) => {
  expandedComments.value[commentId] = !expandedComments.value[commentId];
};

// 检查当前用户是否是评论的所有者
const isCommentOwner = (comment) => {
  const currentUserId = userId.value;
  const commentUserId = comment.userId;
  
  console.log('检查评论所有者:', { 
    commentId: comment.id,
    commentUserId: commentUserId, 
    currentUserId: currentUserId,
    isMatch: String(commentUserId) === String(currentUserId)
  });
  
  // 将两个ID转换为字符串进行比较，避免类型不匹配问题
  return isLoggedIn.value && currentUserId && String(commentUserId) === String(currentUserId);
};

// 确认删除评论
const confirmDeleteComment = (comment) => {
  ElMessageBox.confirm(
    t('comments.deleteConfirm'),
    t('comments.title'),
    {
      confirmButtonText: t('comments.delete'),
      cancelButtonText: t('comments.cancel'),
    type: 'warning'
    }
  )
    .then(() => {
    deleteComment(comment);
    })
    .catch(() => {
      // 用户取消操作
  });
};

// 删除评论
const deleteComment = async (comment) => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录');
    return;
  }

  if (!isCommentOwner(comment)) {
    ElMessage.warning('只能删除自己的评论');
    return;
  }
  
  try {
    console.log('准备删除评论:', comment);
    
    // 确保commentId是有效值
    const commentId = comment.id;
    if (!commentId) {
      ElMessage.error('评论ID不能为空');
      return;
    }
    
    console.log('开始调用删除评论API, 参数:', { commentId });
    const response = await removeComment(commentId);
    console.log('删除评论响应:', response);
    
    if (response.code === 200) {
      ElMessage.success(response.msg || '评论删除成功');
      fetchComments(); // 刷新评论列表
    } else {
      console.error('删除失败, 状态码:', response.code, '错误信息:', response.msg);
      ElMessage.error(response.msg || '删除评论失败');
    }
  } catch (error) {
    console.error('删除评论出错:', error);
    if (error.response) {
      console.error('错误响应信息:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
      
      if (error.response.status === 400) {
        ElMessage.error('删除评论失败: 参数错误，请检查评论ID格式');
      } else if (error.response.status === 404) {
        ElMessage.error('删除评论失败: API接口不存在，请检查接口路径');
      } else {
        ElMessage.error(`删除评论失败: ${error.response.data?.msg || error.message}`);
      }
    } else {
      ElMessage.error('删除评论失败，请稍后重试');
    }
  }
};

// 判断用户是否为指定类型的VIP
const isVipUser = (user, type) => {
console.log('判断用户是否为指定类型的VIP:', { user, type });
  if (!user) return false;
  
  // 直接匹配memberType字段（后端返回的主要字段）
  if (user.memberType === type) return true;
  
  // 兼容可能的其他字段名
  if (user.userType === type) return true;
  if (user.vipType === type) return true;
  
  // 特定类型的判断
  if (type === '普通会员') {
    // 如果有任何表示用户是普通会员的标志
    return (
      user.isVip === true || 
      user.isVip === 1 || 
      user.vipLevel === 1 ||
      user.memberType === 'vip'
    );
  }
  
  if (type === '超级会员') {
    // 如果有任何表示用户是超级会员的标志
    return (
      user.isSuperVip === true || 
      user.isSuperVip === 1 || 
      user.vipLevel === 2 || 
      user.vipLevel === 'super' ||
      user.memberType === 'svip'
    );
  }
  
  return false;
};

onMounted(() => {
  console.log('PostComments组件挂载，接收到的postId:', props.postId);

  if (props.postId) {
    // 确保组件挂载后立即获取评论列表
    fetchComments();
  } else {
    console.warn('PostComments组件挂载时没有接收到有效的postId');
  }
});

// 修改 watch，移除 immediate 选项，因为 onMounted 已经加载了一次
watch(() => props.postId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    console.log('postId 发生变化，重新加载评论');
    fetchComments();
  }
});
</script>

<style scoped>
.post-comments {
  margin-top: 30px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.comments-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.comments-title {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

/* 评论表单 */
.comment-form {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: visible; /* 修改为visible，让VIP徽章显示在头像外部 */
  flex-shrink: 0;
  border: 1px solid #f0f0f0;
  position: relative; /* 添加相对定位，使VIP徽章可以正确定位 */
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%; /* 确保头像图片是圆形的 */
}

.comment-input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 90%;
}

.comment-input {
  width: 95%;
  min-height: 80px;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  resize: vertical;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  transition: all 0.3s;
}

.comment-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-footer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 12px;
  color: #999;
}

.submit-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.submit-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.submit-btn:disabled {
  background-color: #bae7ff;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 评论列表 */
.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comment-item {
  display: flex;
  gap: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  align-items: center;
}

.commenter-name {
  font-weight: 500;
  color: #333;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  margin: 0 0 12px 0;
  line-height: 1.6;
  color: #333;
  white-space: pre-line;
  width: 80%; /* 减少宽度20% */
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s;
}

.action-button:hover {
  color: #1890ff;
}

.reply-button svg,
.delete-button svg {
  width: 14px;
  height: 14px;
}

.delete-button {
  color: #ff4d4f;
}

.delete-button:hover {
  color: #ff7875;
}

/* 空评论状态 */
.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  color: #999;
}

.empty-icon {
  margin-bottom: 16px;
  color: #d9d9d9;
}

.empty-text {
  font-size: 14px;
}

/* 回复表单 */
.reply-form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.reply-form {
  position: relative;
  width: 90%;
  max-width: 650px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.reply-form-header h4 {
  font-size: 17px;
  color: #333;
  margin: 0;
  font-weight: 500;
}

.close-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  color: #666;
  background-color: #f0f0f0;
}

.reply-to-info {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  padding: 12px;
  border-radius: 8px;
  background-color: #fafafa;
}

.reply-to-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: visible; /* 确保VIP徽章可以显示在头像外部 */
  position: relative;
}

.reply-to-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.reply-to-content {
  flex: 1;
}

.reply-to-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.reply-to-text {
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #e0e0e0;
  max-height: 60px;
  width: 90%;
  margin-top: 4px;
  overflow-y: auto;
  white-space: pre-line;
  line-height: 1.5;
}

.reply-input {
  width: 90%;
  margin-left: 3%;
  min-height: 100px;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  resize: vertical;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-top: 5px;
  margin-bottom: 16px;
}

.reply-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.reply-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.cancel-button {
  background: #f0f0f0;
  color: #666;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.cancel-button:hover {
  background: #e0e0e0;
}

.submit-button {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.submit-button:hover:not(:disabled) {
  background: #40a9ff;
}

.submit-button:disabled {
  background: #bae7ff;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 子评论样式 */
.child-comments {
  margin-top: 16px;
  margin-left: 24px;
  border-left: 2px solid #f0f0f0;
  padding-left: 16px;
  position: relative;
}

.child-comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
}

.child-comment-item:last-child {
  margin-bottom: 0;
}

.child-comment-item .comment-avatar {
  width: 32px;
  height: 32px;
  overflow: visible; /* 修改为visible，让VIP徽章显示在头像外部 */
  position: relative; /* 添加相对定位，使VIP徽章可以正确定位 */
}

.child-comment-item .comment-content {
  flex: 1;
}

.child-comment-item .comment-text {
  font-size: 13px;
  margin-bottom: 8px;
  width: 80%; /* 减少宽度20% */
}

.reply-to-indicator {
  color: #666;
}

.reply-to-name {
  color: #1890ff;
  font-weight: 500;
}

.child-comment-item .comment-actions {
  margin-top: 4px;
}

.child-comments-toggle {
  margin-top: 12px;
  text-align: left;
  padding-left: 44px;
}

.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #1890ff;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.toggle-button:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.toggle-button svg {
  transition: transform 0.2s;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .post-comments {
    padding: 16px;
  }

  .comment-form {
    gap: 12px;
  }

  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  .reply-form {
    width: 95%;
    max-width: 650px;
    padding: 20px;
  }

  .child-comments {
    margin-left: 16px;
    padding-left: 12px;
  }

  .child-comments-toggle {
    padding-left: 32px;
  }
}

.reply-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.delete-button {
  color: #ff4d4f;
}

.delete-button:hover {
  color: #ff7875;
}

.delete-button svg {
  margin-right: 4px;
}

/* VIP徽章样式 */
.vip-badge {
  position: absolute;
  bottom: 0;
  right: -5px;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 20;
  line-height: 1;
  min-width: 18px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #fbbf24;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border-color: #fbbf24;
  animation: svip-glow 2s ease-in-out infinite alternate;
}

/* 子评论中的VIP徽章稍微调整位置 */
.child-comment-item .vip-badge {
  bottom: 0;
  right: -3px;
  transform: scale(0.9);
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(59, 130, 246, 0.5);
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(251, 191, 36, 0.5);
  }
}
</style>