﻿<script setup>
import {
  onMounted,
  ref,
  computed,
  nextTick,
  onUnmounted,
  watch,
  provide,
  onBeforeUnmount,
  reactive,
} from "vue";
// import { Search, Delete } from "@element-plus/icons-vue"; // 添加 Delete 图标
import { ElNotification, ElMessage } from "element-plus";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import "leaflet-measure/dist/leaflet-measure.css";
import "leaflet-measure";
import "../utils/MousePosition.js";
import {
  getHotStations,
  getHotStationsels,
  getHotStationszhongya,
  getHotStationszhongou,
  getHotStationsyunnan,
} from "../data/hotStations";
import "@/styles/WorldMap.css";
import Nav from "../components/Nav.vue"; // 导入Nav组件
import Right from "../components/rigth.vue"; // 导入Right组件
import { useI18n } from "../composables/useI18n";
import DistanceTool from "../components/DistanceTool.vue";
import { useUserStore } from "@/store/user";
import AvaterCart from "../components/avaterCart.vue"; // 确保导入AvaterCart组件
import VoucherGiftModal from "@/components/VoucherGiftModal.vue";
import { getMaprouter, listStation } from "@/api/maps.js";
import { getadvertisement } from "@/api/map.js";
import Earth3D from "@/components/Earth3D.vue";
import { useChatModalStore } from "../store/chatModal";
import ChatModal from "../components/chat_modal.vue";
import Login from "../components/login.vue";
import { gettonyonggfx } from "@/api/map";
import { getsendSms } from "@/api/user";
import { getxieyiList } from "@/api/login";

const { t, currentLocale } = useI18n();

const tdtKey = "a9140837d111b6d596b611e6fdedd838";

// 提供地图实例给子组件
provide("map", null); // 先初始化为 null

// 在地图加载完成后更新
const updateMapInstance = (mapInstance) => {
  provide("map", mapInstance);
};

let transportMethod = ref("铁路");
let stationType = ref("destination");
let destination = ref("莫斯科");
let resultsList = ref([]);

let map;
let sc = ref(false);
const ZOOM_LEVELS = {
  country: 4, // 国家级别
  province: 6, // 省级
  city: 7, // 市级
  district: 8, // 区县级
  town: 9, // 乡镇级
  interest: 10, // 具体地点
};

const searchQuery = ref("");
const rawResults = ref([]);
const isSearching = ref(false);
const hasError = ref(false);
const lastUpdate = ref(Date.now());

const searchResults = computed(() => {
  return rawResults.value.map((item, index) => {
    return {
      id: `${index}-${lastUpdate.value}`,
      lat: Number(item.lat || 0),
      lon: Number(item.lon || 0),
      display_name: item.display_name || "未知地点",
      level: getOSMLevel(item.type || "interest"),
    };
  });
});

// 天地图地名搜索API（建议用place接口而不是geocoder接口，geocoder主要用于坐标反查）
async function searchLocation() {
  if (!searchQuery.value.trim()) return;

  isSearching.value = true;
  hasError.value = false;
  rawResults.value = [];

  try {
    const url = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${searchQuery.value}","level":"12","mapBound":"60,0,150,60","queryType":"1","start":"0","count":"1"}&type=query&tk=${tdtKey}`;
    const response = await fetch(url);
    if (!response.ok) throw new Error(`搜索失败: ${response.status}`);
    const data = await response.json();

    let result = null;

    // 1. 优先使用 POI
    if (data.pois && Array.isArray(data.pois) && data.pois.length > 0) {
      const poi = data.pois[0];
      const [lon, lat] = poi.lonlat.split(" ");
      result = {
        lat: Number(lat),
        lon: Number(lon),
        display_name: poi.name,
        type: "interest",
      };
    }
    // 2. 如果没有 POI，使用行政区
    else if (data.area && data.area.lonlat) {
      const [lon, lat] = data.area.lonlat.split(",");
      result = {
        lat: Number(lat),
        lon: Number(lon),
        display_name: data.area.name,
        type: data.area.level >= 10 ? "city" : "province",
      };
    }

    if (!result) {
      throw new Error("未找到相关地点");
    }

    // 直接跳转到找到的位置
    goToLocation(result);
  } catch (error) {
    hasError.value = true;
    console.error("天地图搜索失败:", error);
  } finally {
    isSearching.value = false;
  }
}

function getOSMLevel(type) {
  switch (type) {
    case "country":
      return "country";
    case "state":
      return "province";
    case "city":
    case "town":
      return "city";
    case "suburb":
    case "village":
      return "district";
    default:
      return "interest";
  }
}

function goToLocation(result) {
  if (!map || !result) return;

  const lat = result.lat;
  const lon = result.lon;
  const zoom = ZOOM_LEVELS[result.level] || 9;

  // 清除之前的所有标记点
  clearAllMarkers();

  // 创建新的标记点
  const marker = L.marker([lat, lon])
    .addTo(map)
    .bindPopup(result.display_name)
    .openPopup();

  // 将新标记点添加到管理数组
  currentMarkers.value.push(marker);

  map.setView([lat, lon], zoom);
  searchQuery.value = "";
}

const destinationPoint = ref(""); // 将startPoint改为destinationPoint
const isPlanning = ref(false);

// 添加路线选择状态
const availableRoutes = ref([]);
const selectedRouteIndex = ref(null); // 默认为null，未选中任何路线

// 添加存储所有路线的变量
const allRouteLayers = ref([]);

// 配置铁路轨道样式
const lineStyle = {
  glow: "rgba(0, 0, 0, 0.3)",
  base: "#000000",
  dash: "8, 12",
  railWidth: 8,
};

// 定义固定的站点坐标
const FIXED_STATIONS = {
  北京站: [39.9042, 116.4074],
  南京站: [32.0603, 118.7969],
  西安: [34.3472, 108.9374],
  郑州站: [34.7472, 113.6515],
  武汉站: [30.6268, 114.2987],
};

// 天地图地名检索API（单点地理编码）
async function geocodeLocation(address) {
  try {
    // 使用 place 搜索接口优先返回 pois，否则尝试 area 字段
    const url = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${address}","level":"12","mapBound":"60,0,150,60","queryType":"1","start":"0","count":"1"}&type=query&tk=${tdtKey}`;
    const response = await fetch(url);
    const data = await response.json();
    // 1. 优先返回 pois
    if (
      data.pois &&
      Array.isArray(data.pois) &&
      data.pois.length > 0 &&
      data.pois[0].lonlat
    ) {
      const [lon, lat] = data.pois[0].lonlat.split(" ");
      return {
        lat: Number(lat),
        lng: Number(lon),
        name: data.pois[0].name || address,
      };
    }
    // 2. 若无 pois，尝试 area 字段
    if (data.area && data.area.lonlat) {
      const [lon, lat] = data.area.lonlat.split(",");
      return {
        lat: Number(lat),
        lng: Number(lon),
        name: data.area.name || address,
      };
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 计算两点间距离（米）
function calculateDistance(start, end) {
  if (!start || !end) return 0;

  const R = 6371e3; // 地球半径（米）
  const φ1 = (start.lat * Math.PI) / 180;
  const φ2 = (end.lat * Math.PI) / 180;
  const Δφ = ((end.lat - start.lat) * Math.PI) / 180;
  const Δλ = ((end.lng - end.lon || end.lng - start.lng) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// 修改 loadYandexAPI 函数
const loadYandexAPI = (() => {
  let loadPromise = null;
  return () => {
    if (!loadPromise) {
      loadPromise = new Promise((resolve, reject) => {
        const script = document.createElement("script");
        // script.src = 'https://api-maps.yandex.ru/2.1/?lang=none&load=package.full';
        script.async = true;

        script.onload = () => {
          window.ymaps.ready(() => resolve(window.ymaps));
        };
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }
    return loadPromise;
  };
})();

// 修改路线规划函数
async function getYandexDrivingRoute(start, end) {
  try {
    const ymaps = await loadYandexAPI();

    return new Promise((resolve, reject) => {
      // 确保坐标是数字
      const startPoint = [parseFloat(start.lng), parseFloat(start.lat)];
      const endPoint = [parseFloat(end.lng), parseFloat(end.lat)];

      // 创建路线规划请求
      ymaps
        .route([startPoint, endPoint], {
          mapStateAutoApply: false,
          avoidTrafficJams: true,
          multiRoute: true,
        })
        .then((route) => {
          try {
            const paths = route.getPaths();
            const routes = [];

            paths.each((path, index) => {
              const segments = path.getSegments();
              const coordinates = [];

              segments.forEach((segment) => {
                coordinates.push(...segment.getCoordinates());
              });

              routes.push({
                stations: [
                  { name: start.name, lat: start.lat, lng: start.lng, type: 1 },
                  ...coordinates.map((coord, i) => ({
                    name: `途经点${i + 1}`,
                    lat: coord[1],
                    lng: coord[0],
                    type: 0,
                  })),
                  { name: end.name, lat: end.lat, lng: end.lng, type: 1 },
                ],
                totalTime: `${Math.round(path.getJamsTime() / 60)}分钟`,
                distance: Math.round(path.getLength() / 1000),
                routeIndex: index,
              });
            });

            resolve(routes);
          } catch (error) {
            console.error("路线数据处理失败:", error);
            reject(new Error("路线数据处理失败"));
          }
        })
        .catch((error) => {
          console.error("路线规划API错误:", error);
          reject(new Error("路线规划请求失败"));
        });
    });
  } catch (error) {
    console.error("Yandex API加载失败:", error);
    // 返回直线路线作为备选
    return [
      {
        stations: [
          { name: start.name, lat: start.lat, lng: start.lng, type: 1 },
          { name: end.name, lat: end.lat, lng: end.lng, type: 1 },
        ],
        totalTime: "计算中...",
        distance: Math.round(calculateDistance(start, end) / 1000),
        routeIndex: 0,
      },
    ];
  }
}

// Add a search counter near the top of the component script where other reactive variables are defined
const searchCount = ref(0);
const maxSearchesWithoutLogin = 3; // Maximum number of searches before requiring login
// 添加查询状态和防抖变量
const canCancelQuery = ref(false);
const queryTimeout = ref(null);
// 最近一次查询时间，用于防止短时间内多次点击
const lastQueryTime = ref(0);
const MIN_QUERY_INTERVAL = 2000; // 最小查询间隔，单位毫秒

// 修改路线规划函数
async function planRoute() {
  // 如果正在查询中，提供取消选项
  if (isPlanning.value) {
    if (canCancelQuery.value) {
      // 取消当前查询
      isPlanning.value = false;
      canCancelQuery.value = false;
      
      // 清除超时定时器
      if (queryTimeout.value) {
        clearTimeout(queryTimeout.value);
        queryTimeout.value = null;
      }
      
      ElMessage({
        message: "已取消查询",
        type: "info",
        duration: 2000
      });
      
      return;
    }
    
    // 如果不能取消，提示正在查询中
    ElMessage({
      message: "正在查询路线，请稍候...",
      type: "warning",
      duration: 2000
    });
    
    return;
  }

  // 检查是否短时间内多次点击
  const currentTime = Date.now();
  if (currentTime - lastQueryTime.value < MIN_QUERY_INTERVAL) {
    ElMessage({
      message: "请勿频繁查询，请稍候再试",
      type: "warning",
      duration: 2000
    });
    return;
  }

  if (!destinationPoint.value) return;
  // 同步触发 3D 地图搜索
  if (earth3dRef.value && typeof earth3dRef.value.searchIn3D === "function") {
    earth3dRef.value.searchIn3D(destinationPoint.value);
  }
  if (transportMethod.value === "汽运" && !startPoint.value) {
    ElMessage({
      message: "请输入起始地点",
      type: "warning",
      duration: 3000,
    });
    return;
  }

  // 清除之前的路线
  clearAllMarkers();

  // 清除会员详情卡片数据
  memberInfoData.value = null;

  // 增加搜索计数
  if (!userStore.token) {
    searchCount.value++;
  }

  // 添加到搜索历史
  addToHistory(destinationPoint.value);

  // 更新最近一次查询时间
  lastQueryTime.value = currentTime;
  
  // 设置查询状态
  isPlanning.value = true;
  
  // 在适当延迟后允许取消查询
  queryTimeout.value = setTimeout(() => {
    canCancelQuery.value = true;
  }, 1000);

  try {
    // 检查是否达到未登录搜索次数限制
    // if (!userStore.token && searchCount.value > maxSearchesWithoutLogin) {
    //   // 如果超过限制，提示用户登录，不显示错误消息
    //   ElNotification({
    //     title: "登录提示",
    //     message: "您已多次查询路线信息，请登录继续使用更多功能",
    //     type: "info",
    //     duration: 10000,
    //     position: "top-right",
    //   });

    //   // 打开登录对话框
    //   openLogin();

    //   // 重置搜索状态
    //   isPlanning.value = false;
    //   canCancelQuery.value = false;
    //   return;
    // }

    // 根据是否有起始站来调用不同的查询
    let routes;
    const hasStartPoint = startPoint.value && startPoint.value.trim() !== "";

    if (hasStartPoint) {
      // 先尝试使用起始站和目的站查询
      routes = await getRouteToDestination(
        destinationPoint.value,
        startPoint.value
      );

      // 如果没有找到路线，自动仅使用目的站查询
      if (!routes || !routes.length) {
        ElNotification({
          title: "路线提醒",
          message: `未找到从 ${startPoint.value} 到 ${destinationPoint.value} 的直接路线，为您推荐替代路线！`,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
        routes = await getRouteToDestination(destinationPoint.value);
      }
    } else {
      routes = await getRouteToDestination(destinationPoint.value);
    }

    if (!routes || !routes.length) {
      // 如果没有找到路线，直接抛出错误
      throw new Error("未找到可用路线");
    }

    availableRoutes.value = routes;
    selectedRouteIndex.value = 0;

    // 清除之前的路线
    allRouteLayers.value.forEach((layers) => {
      layers.forEach((layer) => map.removeLayer(layer));
    });
    allRouteLayers.value = [];

    // 只显示前三条路线
    routes.slice(0, 3).forEach((route, index) => {
      const layers = displayRoute(
        {
          path: route.stations,
          stations: route.stations,
          trainInfo: {
            type: transportMethod.value === "汽运" ? "公路线路" : "铁路线路",
            trainNo: `方案${index + 1}`,
            distance: calculateTotalDistance(route.stations),
            totalTime: route.totalTime,
          },
        },
        index,
        true
      );
      allRouteLayers.value[index] = layers;
    });

    // 检查路线的起始站和终点站是否相同
    if (routes.length >= 1) {
      // 获取第一条路线的起始站和终点站
      const firstRoute = routes[0];
      const firstStartStation = firstRoute.stations[0];
      const firstEndStation =
        firstRoute.stations[firstRoute.stations.length - 1];

      // 检查所有路线是否都有相同的起始站和终点站
      let allSameStartStation = true;
      let allSameEndStation = true;

      // 只考虑前三条路线(如果有的话)
      const routesToCheck = routes.slice(0, Math.min(3, routes.length));

      // 如果只有一条路线，默认起始站和终点站相同
      if (routesToCheck.length === 1) {
        // 保持默认值 true
      }
      // 有多条路线时，逐一比较
      else {
        for (let i = 1; i < routesToCheck.length; i++) {
          const route = routesToCheck[i];
          const startStation = route.stations[0];
          const endStation = route.stations[route.stations.length - 1];

          // 检查起始站是否相同
          if (startStation.name !== firstStartStation.name) {
            allSameStartStation = false;
            console.log(
              `路线${i + 1}的起始站(${startStation.name})与第一条路线的起始站(${
                firstStartStation.name
              })不同`
            );
          }

          // 检查终点站是否相同
          if (endStation.name !== firstEndStation.name) {
            allSameEndStation = false;
            console.log(
              `路线${i + 1}的终点站(${endStation.name})与第一条路线的终点站(${
                firstEndStation.name
              })不同`
            );
          }
        }
      }

      // 如果所有路线的起始站和终点站都相同，直接显示第一条路线的站点会员信息
      if (allSameStartStation && allSameEndStation) {
        console.log(
          "所有路线的起始站和终点站都相同，自动显示第一条路线的站点会员信息"
        );

        // 确保延迟执行，等待地图和路线完全加载
        setTimeout(() => {
          try {
            // 设置起始站和终点站信息
            startStation.value = firstStartStation;
            endStation.value = firstEndStation;

            // 设置会员数据
            startMembers.value = {
              ordinary: firstRoute.startStationMembers?.ordinaryMembers || [],
              super: firstRoute.startStationMembers?.superMembers || [],
            };

            endMembers.value = {
              ordinary: firstRoute.endStationMembers?.ordinaryMembers || [],
              super: firstRoute.endStationMembers?.superMembers || [],
            };

            console.log("自动显示卡片 - 起始站:", startStation.value.name);
            console.log("自动显示卡片 - 终点站:", endStation.value.name);

            // 确保地图已经加载并可以获取点位置
            if (map) {
              // 更新起始站和终点站卡片位置
              const startPoint = map.latLngToContainerPoint([
                firstStartStation.lat,
                firstStartStation.lng,
              ]);

              const endPoint = map.latLngToContainerPoint([
                firstEndStation.lat,
                firstEndStation.lng,
              ]);

              startCardPos.value = {
                x: startPoint.x,
                y: startPoint.y + 30,
              };

              endCardPos.value = {
                x: endPoint.x,
                y: endPoint.y + 30,
              };

              // 设置地图视图以显示整个路线
              // 根据第一条路线的里程数计算缩放级别
              const firstRoute = availableRoutes.value[0];
              const mileage = firstRoute.totalMileage || calculateTotalDistance(firstRoute.stations);
              const zoomLevel = calculateZoomByMileage(mileage);

              const centerLat = (firstStartStation.lat + firstEndStation.lat) / 2;
              const centerLng = (firstStartStation.lng + firstEndStation.lng) / 2;

              console.log(`初始路线里程: ${mileage}km, 计算缩放级别: ${zoomLevel}`);
              map.setView([centerLat, centerLng], zoomLevel);
            }

            // 标记路线为选中状态
            selectedRouteIndex.value = 0;

            // 显示会员信息卡片
            showStartAvaterCart.value = true;
            showEndAvaterCart.value = true;
            showStartCard.value = true;
            showEndCard.value = true;

            // 强制更新卡片位置
            nextTick(() => {
              updateAvaterCartPositions();
              updateCardPositions();
            });
          } catch (error) {
            console.error("自动显示会员卡片失败:", error);
          }
        }, 500);
      }
      // 处理只有起始站相同的情况
      else if (allSameStartStation) {
        console.log("所有路线的起始站都相同，直接显示起始站会员卡片");
        startStation.value = firstStartStation;
        showStartAvaterCart.value = true;

        // 更新起始站卡片位置
        const point = map.latLngToContainerPoint([
          firstStartStation.lat,
          firstStartStation.lng,
        ]);
        startCardPos.value = {
          x: point.x,
          y: point.y + 30,
        };

        // 保存会员数据
        startMembers.value = {
          ordinary: firstRoute.startStationMembers?.ordinaryMembers || [],
          super: firstRoute.startStationMembers?.superMembers || [],
        };
      }
      // 处理只有终点站相同的情况
      else if (allSameEndStation) {
        console.log("所有路线的终点站都相同，直接显示终点站会员卡片");
        endStation.value = firstEndStation;
        showEndAvaterCart.value = true;

        // 更新终点站卡片位置
        const point = map.latLngToContainerPoint([
          firstEndStation.lat,
          firstEndStation.lng,
        ]);
        endCardPos.value = {
          x: point.x,
          y: point.y + 30,
        };

        // 保存会员数据
        endMembers.value = {
          ordinary: firstRoute.endStationMembers?.ordinaryMembers || [],
          super: firstRoute.endStationMembers?.superMembers || [],
        };
      }
    }

    // 为剩余路线预留位置
    for (let i = 3; i < routes.length; i++) {
      allRouteLayers.value[i] = [];
    }

    // 隐藏热门站点
    showHotStations.value = false;
    if (window.hotStationsLayer) {
      map.removeLayer(window.hotStationsLayer);
    }

    // 获取广告数据
    await fetchAdvertisements();
  } catch (error) {
    console.error("路线规划失败:", error);

    // 检查是否是需要登录的错误
    if (error.message === "NEED_LOGIN") {
      ElNotification({
        title: "登录提示",
        message: "您已多次查询路线信息，请登录继续使用更多功能",
        type: "info",
        duration: 10000,
        position: "top-right",
      });

      // 打开登录对话框
      openLogin();
    } else {
      // 其他错误显示错误消息
      ElNotification({
        title: "错误",
        message: error.message,
        type: "error",
        duration: 4000,
        position: "top-right",
      });
    }
  } finally {
    // 重置查询状态
    isPlanning.value = false;
    canCancelQuery.value = false;
    
    // 清除超时定时器
    if (queryTimeout.value) {
      clearTimeout(queryTimeout.value);
      queryTimeout.value = null;
    }
  }
}

// 添加路线点击处理函数
// const onRouteClick = (index) => {
//   console.log('点击路线:', index)

//   // 更新2D地图显示
//   selectRoute(index)

//   // 通知3D组件切换路线
//   if (is3DMode.value && earth3dRef.value && typeof earth3dRef.value.selectRoute === 'function') {
//     const selectedRoute = availableRoutes.value[index];
//     console.log('通知3D组件切换路线:', index, '路线ID:', selectedRoute?.id);
//     earth3dRef.value.selectRoute(index, selectedRoute?.id);
//   }
// }

// 添加路线点击处理函数
const onRouteClick = (index) => {
  console.log("点击路线:", index);

  // 更新2D地图显示
  selectRoute(index);

  // 通知3D组件切换路线
  if (
    is3DMode.value &&
    earth3dRef.value &&
    typeof earth3dRef.value.selectRoute === "function"
  ) {
    const selectedRoute = availableRoutes.value[index];
    console.log("通知3D组件切换路线:", index, "路线ID:", selectedRoute?.id);
    earth3dRef.value.selectRoute(index, selectedRoute?.id);
  }
};

// 修改选择路线函数
function selectRoute(index) {
  console.log("选择路线:", index);
  selectedRouteIndex.value = index;

  // 获取当前路线的起点和终点站
  const selectedRoute = availableRoutes.value[index];

  if (selectedRoute?.stations?.length > 0) {
    const startStation = selectedRoute.stations[0];
    const endStation =
      selectedRoute.stations[selectedRoute.stations.length - 1];
      
    // 根据当前语言选择合适的站点名称
    startPoint.value = currentLocale.value === "zh-CN" ? startStation.name : (startStation.nameEn || startStation.name);
    destinationPoint.value = currentLocale.value === "zh-CN" ? endStation.name : (endStation.nameEn || endStation.name);

    // 设置起点站卡片
    if (startStation && map) {
      const point = map.latLngToContainerPoint([
        startStation.lat,
        startStation.lng,
      ]);
      console.log(point);

      startCardPos.value = {
        x: point.x,
        y: point.y + 30, // 向下偏移30px
      };
    }

    // 设置终点站卡片
    if (endStation && map) {
      const point = map.latLngToContainerPoint([
        endStation.lat,
        endStation.lng,
      ]);
      endCardPos.value = {
        x: point.x,
        y: point.y + 30, // 向下偏移30px
      };
    }
    if (map) {
      const centerLat = (startStation.lat + endStation.lat) / 2;
      const centerLng = (startStation.lng + endStation.lng) / 2;

      // 根据路线总里程数计算合适的缩放级别
      const mileage = selectedRoute.totalMileage || calculateTotalDistance(selectedRoute.stations);
      const zoomLevel = calculateZoomByMileage(mileage);

      console.log(`路线里程: ${mileage}km, 计算缩放级别: ${zoomLevel}`);
      map.setView([centerLat, centerLng], zoomLevel);
    }
    // 显示卡片
    showStartCard.value = true;
    showEndCard.value = true;
    // 同时设置AvaterCart显示
    showStartAvaterCart.value = true;
    showEndAvaterCart.value = true;

    // 保存会员数据
    startMembers.value = {
      ordinary: selectedRoute.startStationMembers?.ordinaryMembers || [],
      super: selectedRoute.startStationMembers?.superMembers || [],
    };

    endMembers.value = {
      ordinary: selectedRoute.endStationMembers?.ordinaryMembers || [],
      super: selectedRoute.endStationMembers?.superMembers || [],
    };
  }

  // 如果选择的是第4条及以后的路线，且该路线还未绘制
  if (index >= 3 && allRouteLayers.value[index].length === 0) {
    const route = availableRoutes.value[index];
    const layers = displayRoute(
      {
        path: route.stations,
        stations: route.stations,
        trainInfo: {
          type: transportMethod.value === "汽运" ? "公路线路" : "铁路线路",
          trainNo: `方案${index + 1}`,
          distance: calculateTotalDistance(route.stations),
          totalTime: route.totalTime,
        },
      },
      index,
      true
    );
    allRouteLayers.value[index] = layers;
  }

  // 更新所有路线的显示状态
  allRouteLayers.value.forEach((layers, i) => {
    layers.forEach((layer) => {
      const element = layer.getElement?.();
      if (i === index) {
        if (element) {
          element.style.setProperty("display", "block");
          if (layer instanceof L.Marker) {
            element.style.setProperty("pointer-events", "auto");
          }
        }
        // 根据图层特征设置选中样式
        if (layer.options.color === "white") {
          // 铁轨枕木部分
          layer.setStyle?.({ opacity: 1 });
        } else {
          // 铁轨底层
          layer.setStyle?.({ opacity: 0.8 });
        }
      } else {
        if (element) {
          element.style.setProperty("display", "none");
          element.style.setProperty("pointer-events", "none");
        }
        layer.setStyle?.({
          opacity: 0,
        });
      }
    });
  });
}

// 修改获取路线函数返回多条路线
async function getRouteToDestination(destination, startPoint = null) {
  try {
    console.log("获取路线:", destination, startPoint);

    // 处理目的地，检查是否包含中英文格式 EnglishName(ChineseName)
    let endStationName = destination;
    const destMatch = destination.match(/^(.+?)\(([^)]+)\)$/);
    if (destMatch) {
      // 如果匹配到格式，提取英文部分
      endStationName = destMatch[1];
      console.log("使用英文名搜索目的地:", endStationName);
    }

    // 确定是否包含中文字符
    const isChinese = /[\u4e00-\u9fa5]/.test(endStationName);
    let params = isChinese
      ? { endStationNameZh: endStationName }
      : { endStationNameEn: endStationName };

    // 如果提供了起始站，添加到查询参数中
    if (startPoint) {
      let startStationName = startPoint;
      const startMatch = startPoint.match(/^(.+?)\(([^)]+)\)$/);
      if (startMatch) {
        // 如果匹配到格式，提取英文部分
        startStationName = startMatch[1];
        console.log("使用英文名搜索起始站:", startStationName);
      }

      const isStartChinese = /[\u4e00-\u9fa5]/.test(startStationName);
      if (isStartChinese) {
        params.startStationNameZh = startStationName;
      } else {
        params.startStationNameEn = startStationName;
      }
    }
    // params.isPage = false;
    const response = await getMaprouter(params);

    if (response.code !== 200 || !response.rows) {
      throw new Error("获取路线失败");
    }

    return response.rows.map((route) => {
      // 构建站点数组
      const stations = [
        {
          name: route.startStationNameZh,
          nameEn: route.startStationNameEn,
          displayName: route.startStationNameEn && route.startStationNameZh ? 
            `${route.startStationNameEn}(${route.startStationNameZh})` : 
            route.startStationNameZh,
          lat: route.startStationLatitude,
          lng: route.startStationLongitude,
          type: 1,
          members: route.startStationMembers, // 添加会员信息
        },
        // 中间站点
        ...(route.viaStations || []).map((station) => ({
          name: station.stationNameZh,
          nameEn: station.stationNameEn,
          displayName: station.stationNameEn && station.stationNameZh ? 
            `${station.stationNameEn}(${station.stationNameZh})` : 
            station.stationNameZh,
          lat: station.latitude,
          lng: station.longitude,
          type: station.isExitPort ? 1 : 0,
          members: null,
        })),
        // 终点站
        {
          name: route.endStationNameZh,
          nameEn: route.endStationNameEn,
          displayName: route.endStationNameEn && route.endStationNameZh ? 
            `${route.endStationNameEn}(${route.endStationNameZh})` : 
            route.endStationNameZh,
          lat: route.endStationLatitude,
          lng: route.endStationLongitude,
          type: 1,
          members: route.endStationMembers, // 添加会员信息
        },
      ];

      return {
        //筛选出isExitPort为1是该路线转关口岸
        stations,
        isExitPort: (() => {
          const exitPorts = route.viaStations.filter(
            (station) => station.isExitPort === 1
          );
          return exitPorts.length > 0 ? exitPorts[0].stationNameZh : '';
        })(),
        totalMileage: route.totalMileage,
        totalTime: `${route.totalDays}天`,
        trafficCondition: route.congestionLevel || "通畅",
        gpsStatus: route.cabinStatus || "充足",
        startStationMembers: route.startStationMembers,
        endStationMembers: route.endStationMembers,
      };
    });
  } catch (error) {
    console.error("获取路线失败:", error);

    // 检查是否为401未授权错误（用户需要登录）
    if (error.response && error.response.status === 401) {
      // 标记为需要登录的错误
      throw new Error("NEED_LOGIN");
    }

    // 其他错误，抛出原始错误
    throw error;
  }
}

// 计算总距离
function calculateTotalDistance(stations) {
  let total = 0;
  for (let i = 1; i < stations.length; i++) {
    total += calculateDistance(stations[i - 1], stations[i]);
  }
  return total / 1000; // 转换为公里
}

// 根据路线总里程数计算合适的缩放级别
function calculateZoomByMileage(mileage) {
  // 里程数越大，缩放级别越小（显示范围越大）
  // 里程数越小，缩放级别越大（显示范围越小）

  if (mileage >= 8000) {
    return 2; // 超长距离路线（如中欧班列）
  } else if (mileage >= 5000) {
    return 3; // 长距离路线
  } else if (mileage >= 3000) {
    return 4; // 中长距离路线
  } else if (mileage >= 1500) {
    return 5; // 中距离路线
  } else if (mileage >= 800) {
    return 6; // 中短距离路线
  } else if (mileage >= 400) {
    return 7; // 短距离路线
  } else {
    return 8; // 很短距离路线
  }
}

// 修改生成自然曲线的函数
function generateNaturalCurve(start, end) {
  const points = [];
  const count = 200; // 增加采样点数量以获得更平滑的曲线

  // 计算两点之间的距离
  const dx = end.lng - start.lng;
  const dy = end.lat - start.lat;
  const dist = Math.sqrt(dx * dx + dy * dy);

  // 计算曲线的控制点偏移量
  const offset = dist * 0.13; // 控制曲线的弯曲程度

  // 计算曲线的中点
  const midX = (start.lng + end.lng) / 2;
  const midY = (start.lat + end.lat) / 2;

  // 生成贝塞尔曲线的控制点 - 使用更小的随机偏移量，使得路线更一致
  // 使用起点和终点的坐标作为随机数生成的种子，确保同一路线每次生成的曲线相似
  const seed = (start.lat + end.lat + start.lng + end.lng) * 1000;
  const pseudoRandom = Math.sin(seed) * 0.5 + 0.5; // 生成0-1之间的伪随机数
  const direction = pseudoRandom > 0.5 ? 1 : -1;
  
  // 减小偏移量并使用伪随机数，使曲线变化更小
  const controlPoint = {
    lat: midY + (pseudoRandom * 0.5 + 0.3) * offset * direction,
    lng: midX + (pseudoRandom * 0.5 + 0.3) * offset * direction,
  };

  // 使用二次贝塞尔曲线生成平滑路径
  for (let i = 0; i <= count; i++) {
    const t = i / count;
    const point = {
      lat:
        Math.pow(1 - t, 2) * start.lat +
        2 * (1 - t) * t * controlPoint.lat +
        Math.pow(t, 2) * end.lat,
      lng:
        Math.pow(1 - t, 2) * start.lng +
        2 * (1 - t) * t * controlPoint.lng +
        Math.pow(t, 2) * end.lng,
    };
    points.push(point);
  }

  return points;
}

function generateCurvedPath(points) {
  if (points.length < 2) return points;

  const curvedPoints = [];

  // 直接处理每对相邻点
  for (let i = 0; i < points.length - 1; i++) {
    const current = points[i];
    const next = points[i + 1];

    // 生成当前段的曲线
    const curvePoints = generateNaturalCurve(current, next);

    // 添加所有点，除了最后一个(避免重复)
    if (i < points.length - 2) {
      curvePoints.pop();
    }

    curvedPoints.push(...curvePoints.map((point) => [point.lat, point.lng]));
  }

  return curvedPoints;
}

// 修改 displayRoute 函数
function displayRoute(route, routeIndex = 0, keepVisible = false) {
  if (!route || !route.path || !Array.isArray(route.path)) return [];

  const validPath = route.path.filter((point) => {
    return (
      point && typeof point.lat === "number" && typeof point.lng === "number"
    );
  });

  if (validPath.length < 2) return [];

  const curvedPath = generateCurvedPath(validPath);
  const layers = [];

  // 创建铁轨风格的路线图层
  // 铁轨底层 - 黑色轨道背景
  const railBase = L.polyline(curvedPath, {
    color: lineStyle.base,
    weight: lineStyle.railWidth,
    opacity: 0.8,
    lineCap: "butt", // 平直端点更像铁轨
    lineJoin: "miter", // 尖角连接点
    smoothFactor: 1,
    className: `route-${routeIndex}`,
    interactive: true, // 允许鼠标交互
  }).on("click", () => onRouteClick(routeIndex));

  // 铁轨上层 - 黑白相间的枕木效果
  const railTies = L.polyline(curvedPath, {
    color: "white",
    weight: lineStyle.railWidth - 2, // 稍微窄一点形成轨道边框效果
    opacity: 1,
    dashArray: lineStyle.dash,
    lineCap: "butt",
    lineJoin: "miter",
    smoothFactor: 1,
    className: `route-${routeIndex}`,
    interactive: true,
  }).on("click", () => onRouteClick(routeIndex));

  // 添加路线标签
  const midPoint = Math.floor(curvedPath.length / 2);
  const label = L.divIcon({
    className: "route-label",
    html: `<div class="route-number" style="      background: ${
      lineStyle.base
    };      color: white;      padding: 2px 6px;      border-radius: 10px;      font-size: 11px;      white-space: nowrap;      box-shadow: 0 1px 3px rgba(0,0,0,0.3);    ">线路 ${
      routeIndex + 1
    }</div>`,
    iconSize: [40, 20],
    iconAnchor: [20, 10],
  });

  const labelMarker = L.marker(
    [curvedPath[midPoint][0], curvedPath[midPoint][1]],
    {
      icon: label,
      className: `route-${routeIndex}`,
    }
  );

  // 为铁轨添加点击事件
  railBase.on("click", () => selectRoute(routeIndex));
  railTies.on("click", () => selectRoute(routeIndex));
  labelMarker.on("click", () => selectRoute(routeIndex));

  layers.push(railBase, railTies, labelMarker);

  // 修改站点标记创建部分
  if (route.stations) {
    route.stations.forEach((station, index) => {
      const isEndpoint = index === 0 || index === route.stations.length - 1;
      if (isEndpoint) {
        const coordinates = FIXED_STATIONS[station.name] || [
          station.lat,
          station.lng,
        ];
        const isStart = index === 0;
        const icon = L.divIcon({
          className: `station-marker-${routeIndex}-${index} ${
            isStart ? "start-station" : "end-station"
          }`,
          html: `<div class="station-icon station-icon-${
            isStart ? "start" : "end"
          }">
            <div class="station-icon-inner">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <circle cx="12" cy="12" r="10" fill="${
                  isStart ? "#4CAF50" : "#FF5722"
                }" stroke="white" stroke-width="2" />
                <text x="12" y="17" text-anchor="middle" fill="white" font-size="14" font-weight="bold">
                  ${isStart ? "S" : "E"}
                </text>
              </svg>
            </div>
            <div class="station-name-label">${currentLocale.value === "zh-CN" ? station.name : (station.nameEn || station.name)}</div>
          </div>`,
          iconSize: L.point(40, 40),
          iconAnchor: L.point(20, 20),
        });

        const marker = L.marker(coordinates, {
          icon: icon,
          zIndexOffset: 1000,
          interactive: true,
          bubblingMouseEvents: false,
          riseOnHover: true,
          routeIndex: routeIndex, // 添加路线索引
        });

        // 点击事件只处理选中路线的站点，且仅在2D模式下显示avaterCart
        marker.on("click", (e) => {
          if (selectedRouteIndex.value === e.target.options.routeIndex) {
            // 检查是否为3D模式
            if (is3DMode.value) return;

            const pixel = map.latLngToContainerPoint(e.target.getLatLng());
            avaterCartPosition.value = {
              x: pixel.x,
              y: pixel.y,
            };
            selectedStation.value = station;
            showAvaterCart.value = true;
          }
        });

        // 设置初始可见性
        if (routeIndex !== selectedRouteIndex.value) {
          const element = marker.getElement();
          if (element) {
            element.style.setProperty("pointer-events", "none");
            element.style.setProperty("display", "none");
          }
        }

        layers.push(marker);
      }
    });
  }

  if (keepVisible) {
    layers.forEach((layer) => {
      layer.addTo(map);
      if (layer instanceof L.Marker) {
        currentMarkers.value.push(layer);
      }
    });
  }

  return layers;
}

// 添加热门站点显示控制
const showHotStations = ref(true);

// 修改热门站点标记点创建逻辑
async function renderHotStations() {
  if (!map || !showHotStations.value) {
    console.log("renderHotStations: 地图未初始化或热门站点不显示");
    return;
  }

  try {
    // 直接获取热门站点数据，不需要用ref包装
    const stations = await getHotStations();
    console.log("获取到的热门站点:", stations);
    resultsList.value = stations.map((item) => {
      return {
        name: item.nameEn,
        description: item.name,
      };
    });
    const stationsels = await getHotStationsels();
    tabData.中俄 = stationsels.map((item) => {
      return {
        name: item.nameEn,
        description: item.name,
      };
    });
    const stationszhongya = await getHotStationszhongya();
    tabData.中亚 = stationszhongya.map((item) => {
      return {
        name: item.nameEn,
        description: item.name,
      };
    });
    const stationszhongou = await getHotStationszhongou();
    tabData.中欧 = stationszhongou.map((item) => {
      return {
        name: item.nameEn,
        description: item.name,
      };
    });
    const stationsyunnan = await getHotStationsyunnan();
    tabData.东南亚 = stationsyunnan.map((item) => {
      return {
        name: item.nameEn,
        description: item.name,
      };
    });
    if (window.hotStationsLayer) {
      map.removeLayer(window.hotStationsLayer);
    }

    const markers = [];
    // 确保stations是数组且有数据
    if (Array.isArray(stations) && stations.length > 0) {
      stations.forEach((station) => {
        // 确保站点数据有效
        if (
          station &&
          typeof station.lat === "number" &&
          typeof station.lng === "number"
        ) {
          const customIcon = L.divIcon({
            className: `hot-station-icon lang-${currentLocale.value}`,
            html: `
              <div class="map-container">
                <div class="marker-container">
                  <div class="marker-label">
                    <div class="label-content">
                      <div class="label-text">
                        <div class="primary-text" style="display: flex; align-items: center; font-size: 12px;">
                          <svg t="1746685747219" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3232" width="14" height="14" style="margin-right: 4px;">
                            <path d="M336 972.8c-60.8-128-28.8-201.6 19.2-268.8 51.2-76.8 64-150.4 64-150.4s41.6 51.2 25.6 134.4c70.4-80 83.2-208 73.6-256 160 112 230.4 358.4 137.6 537.6 492.8-281.6 121.6-700.8 57.6-745.6 22.4 48 25.6 128-19.2 166.4-73.6-281.6-256-336-256-336 22.4 144-76.8 300.8-172.8 419.2-3.2-57.6-6.4-96-38.4-153.6-6.4 105.6-86.4 188.8-108.8 294.4C89.6 758.4 140.8 860.8 336 972.8L336 972.8z" p-id="3233" fill="#d81e06"></path>
                          </svg>
                          <span>${currentLocale.value === "zh-CN" ? station.name : station.nameEn}</span>
                        </div>
                      </div>
                    </div>
                    <div class="label-arrow"></div>
                  </div>
                </div>
                <div class="marker-dot-container">
                  <div class="marker-dot"></div>
                  <div class="pulse-ring pulse-ring-1"></div>
                  <div class="pulse-ring pulse-ring-2"></div>
                  <div class="pulse-ring pulse-ring-3"></div>
                </div>
              </div>
            `,
            iconSize: L.point(currentLocale.value === "en" ? 160 : 100, 60),
            iconAnchor: L.point(currentLocale.value === "en" ? 80 : 50, 60),
          });

          const marker = L.marker([station.lat, station.lng], {
            icon: customIcon,
            zIndexOffset: 1000,
            interactive: true,
            bubblingMouseEvents: false,
          });

          marker.on("click", () => {
            destinationPoint.value = currentLocale.value === "zh-CN" ? station.name : (station.nameEn || station.name);
            planRoute();
          });

          markers.push(marker);
        }
      });
    }

    // 创建新的图层组并添加到地图
    window.hotStationsLayer = L.featureGroup(markers).addTo(map);
    console.log("renderHotStations: 热门站点渲染完成");
  } catch (error) {
    console.error("渲染热门站点失败:", error);
  }
}

// 添加地图事件控制函数
function disableMapEvents() {
  if (map) {
    map.dragging.disable();
    map.touchZoom.disable();
    map.doubleClickZoom.disable();
    map.scrollWheelZoom.disable();
    map.boxZoom.disable();
    map.keyboard.disable();
  }
}

function enableMapEvents() {
  if (map) {
    map.dragging.enable();
    map.touchZoom.enable();
    map.doubleClickZoom.enable();
    map.scrollWheelZoom.enable();
    map.boxZoom.enable();
    map.keyboard.enable();
  }
}

// 添加地图控制处理函数
const handleMapControl = (enabled) => {
  if (!map) return;

  if (enabled) {
    // 恢复地图控制
    map.dragging.enable();
    map.touchZoom.enable();
    map.doubleClickZoom.enable();
    map.scrollWheelZoom.enable();
    map.boxZoom.enable();
    map.keyboard.enable();
    map.tap?.enable();
  } else {
    // 禁用地图控制
    map.dragging.disable();
    map.touchZoom.disable();
    map.doubleClickZoom.disable();
    map.scrollWheelZoom.disable();
    map.boxZoom.disable();
    map.keyboard.disable();
    map.tap?.disable();
  }
};

// 添加setTimeout包装函数
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// 修改点击事件处理，点击外部隐藏下拉框
const handleClickOutside = (event) => {
  // 处理目的地和起始站下拉框逻辑
  // 获取所有目的地和起始站相关元素
  const destinationSuggestions = document.querySelectorAll(
    ".destination-suggestions"
  );
  const startSuggestions = document.querySelectorAll(".start-suggestions");
  const destinationInputs = document.querySelectorAll(
    "input[v-model='destinationPoint']"
  );
  const startInputs = document.querySelectorAll("input[v-model='startPoint']");

  // 处理运输方式下拉框
  let clickedOutsideTransport = true;
  const transportDropdowns = document.querySelectorAll(".select-dropdown");
  const transportSelects = document.querySelectorAll(".select-container");

  // 检查是否点击了运输方式下拉框或按钮
  transportSelects.forEach((select) => {
    if (select.contains(event.target)) {
      // 如果点击的是运输方式选择区域，不做处理（因为点击事件会在内部处理）
      clickedOutsideTransport = false;
    }
  });

  // 检查是否点击了运输方式下拉选项
  transportDropdowns.forEach((dropdown) => {
    if (dropdown.contains(event.target)) {
      clickedOutsideTransport = false;
    }
  });

  // 如果点击在运输方式下拉框外部，关闭它
  if (clickedOutsideTransport && isTransportDropdownOpen.value) {
    isTransportDropdownOpen.value = false;
  }

  // 处理目的地下拉菜单
  let clickedOutsideDestination = true;

  // 检查是否点击了目的地输入框
  destinationInputs.forEach((input) => {
    if (input.contains(event.target)) {
      clickedOutsideDestination = false;
      // 当点击目的地输入框时，确保隐藏起始站下拉菜单
      showStartSuggestions.value = false;
    }
  });

  // 检查是否点击了目的地下拉框
  destinationSuggestions.forEach((list) => {
    if (list.contains(event.target)) {
      clickedOutsideDestination = false;
    }
  });

  // 如果点击在目的地输入框和下拉框外部，隐藏目的地下拉框
  if (clickedOutsideDestination) {
    showSuggestions.value = false;
  }

  // 处理起始站下拉菜单
  let clickedOutsideStart = true;

  // 检查是否点击了起始站输入框
  startInputs.forEach((input) => {
    if (input.contains(event.target)) {
      clickedOutsideStart = false;
      // 当点击起始站输入框时，确保隐藏目的地下拉菜单
      showSuggestions.value = false;
    }
  });

  // 检查是否点击了起始站下拉框
  startSuggestions.forEach((list) => {
    if (list.contains(event.target)) {
      clickedOutsideStart = false;
    }
  });

  // 如果点击在起始站输入框和下拉框外部，隐藏起始站下拉框
  if (clickedOutsideStart) {
    showStartSuggestions.value = false;
  }
};

// 添加全局点击事件监听
onMounted(async () => {
  // 获取协议内容
  await fetchAgreements();

  document.addEventListener("click", handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
});

// 修改模板blur事件处理
const handleSearchBlur = () => {
  // 为避免与点击事件冲突，仅当不是点击选项时才隐藏
  // 使用短延迟以确保先处理点击事件
  delay(100).then(() => {
    // 隐藏目的地搜索结果
    showSuggestions.value = false;
  });
};

// 处理起始站输入框失焦事件
const handleStartSearchBlur = () => {
  // 使用短延迟以确保先处理点击事件
  delay(100).then(() => {
    // 隐藏起始站搜索结果
    showStartSuggestions.value = false;
  });
};

// 添加控制3D模式的状态
const is3DMode = ref(false);

// 添加Nav组件的3D切换事件处理器
const handle3DToggle = () => {
  console.log("切换3D/2D模式", !is3DMode.value ? "3D" : "2D");

  // 清空路线和输入框
  clearRoutes();

  // 如果从3D切换到2D，提前做些准备工作
  if (is3DMode.value) {
    // 先切换状态再处理DOM
    is3DMode.value = false;

    // 延迟执行确保DOM已更新
    nextTick(() => {
      console.log("从3D切换回2D，开始恢复地图显示");

      // 如果有选择的路线，重新启用AvaterCart显示
      if (
        selectedRouteIndex.value !== null &&
        availableRoutes.value.length > 0
      ) {
        setTimeout(() => {
          showStartCard.value = true;
          showEndCard.value = true;
          updateAvaterCartPositions();
        }, 500);
      }

      // 确保地图容器元素存在
      if (!mapContainer.value) {
        console.error("地图容器不存在");
        return;
      }

      // 确保地图容器可见
      const mapContainerEl = mapContainer.value;
      if (mapContainerEl.style.display === "none") {
        mapContainerEl.style.display = "block";
      }

      // 如果地图已存在，只需刷新大小并重新创建图层
      if (map) {
        try {
          console.log("刷新现有地图大小");
          // 先重置地图大小
          map.invalidateSize({ reset: true, animate: false, pan: false });

          // 延迟一段时间后重新创建图层，确保DOM完全更新
          setTimeout(() => {
            recreateMapLayers();
            // 再次刷新地图大小，确保渲染正确
            map.invalidateSize({ reset: true, animate: false, pan: false });
            // 如果开启了热门站点显示，重新渲染热门站点
            if (showHotStations.value) {
              renderHotStations();
            }
          }, 300);
        } catch (err) {
          console.error("刷新地图失败:", err);
          // 如果刷新失败，尝试完全重新初始化地图
          setTimeout(() => {
            initMap();
          }, 500);
        }
      } else {
        // 如果地图不存在，重新初始化
        console.log("地图不存在，重新初始化");
        setTimeout(() => {
          initMap();
        }, 500);
      }
    });
  } else {
    // 从2D切换到3D
    is3DMode.value = true;

    // 隐藏任何当前显示的avaterCart
    showStartCard.value = false;
    showEndCard.value = false;
    showAvaterCart.value = false;

    // 确保地图容器不可见，避免在3D模式下仍渲染
    nextTick(() => {
      if (mapContainer.value) {
        mapContainer.value.style.display = "none";
      }
    });
  }
};

// 添加地图初始化函数，从onMounted中抽离出来
const initMap = () => {
  // 确保容器元素存在
  if (!mapContainer.value) {
    console.error("地图容器不存在，无法初始化地图");
    return;
  }

  // 检查地图是否已经初始化过
  if (map) {
    console.log("地图已经初始化，不再重新创建");
    map.invalidateSize({ reset: true, animate: false });
    recreateMapLayers(); // 只重新创建图层
    return;
  }

  console.log("初始化2D地图");

  // 创建地图实例
  try {
    map = L.map(mapContainer.value, {
      center: [35.8617, 104.1954],
      zoom: 4,
      maxZoom: 18,
      minZoom: 2,
      attributionControl: true,
      zoomControl: false,
      worldCopyJump: true,
      crs: L.CRS.EPSG3857,
      fadeAnimation: false,
      markerZoomAnimation: false,
      preferCanvas: true,
      // 限制地图移动范围，防止移出视图
      maxBounds: [
        [-225, -180], // 西南角
        [85, 220], // 东北角 - 放宽右边界限制
      ],
      maxBoundsViscosity: 1.0, // 完全限制在边界内
    });

    // 创建全局保存的图层引用
    window.mapLayers = {
      yandex: null,
      tdt: null,
    };

    // 添加图层
    recreateMapLayers();

    // 添加控件
    L.control
      .scale({
        imperial: false,
        metric: true,
        position: "bottomright",
      })
      .addTo(map);

    L.control
      .mousePosition({
        position: "bottomleft",
        separator: " | ",
        emptyString: "未知",
        lngFirst: true,
        numDigits: 5,
      })
      .addTo(map);

    // 添加自定义位置的缩放控件
    L.control
      .zoom({
        position: "topright",
        zoomInText: "+",
        zoomOutText: "-",
        zoomInTitle: "放大",
        zoomOutTitle: "缩小",
      })
      .addTo(map);

    // 添加事件监听
    map.on("zoomend moveend", () => {
      // 延迟执行以确保地图状态已更新
      setTimeout(() => {
        if (window.hotStationsLayer) {
          window.hotStationsLayer.eachLayer((layer) => {
            if (layer instanceof L.Marker) {
              layer.setLatLng(layer.getLatLng());
            }
          });
        }
      }, 100);
    });

    // 在地图初始化后添加缩放监听
    map.on("zoomend", () => {
      const currentZoom = map.getZoom();
      const markerLabels = document.querySelectorAll(".marker-label");
      markerLabels.forEach((label) => {
        label.style.display =
          currentZoom <= 4 || currentZoom >= 10 ? "none" : "block";
      });
    });

    // 添加地图移动和缩放事件监听
    map.on("movestart zoomstart", () => {
      // 只隐藏临时的供应商信息卡片，保持固定站点的信息卡显示
      showAvaterCart.value = false;
    });

    // 添加地图移动和缩放结束事件监听，更新固定站点信息卡的位置
    map.on("moveend zoomend", () => {
      // 更新所有会员卡片位置 - 无论是手动选择的还是自动显示的
      updateAvaterCartPositions();
      updateCardPositions();
    });

    // 渲染热门站点
    renderHotStations();

    console.log("2D地图初始化完成");
  } catch (error) {
    console.error("地图初始化失败:", error);
  }
};

onMounted(() => {
  // 如果不是3D模式，初始化2D地图
  if (!is3DMode.value) {
    // 延迟初始化地图，确保DOM已加载
    setTimeout(() => {
      initMap();
    }, 100);
  }

  // 获取广告数据
  fetchAdvertisements();

  // 从localStorage加载搜索历史
  loadHistoryFromLocalStorage();

  startCarousel();
});
// 添加对DOM元素的引用
const mapContainer = ref(null);

// 检测是否为移动设备
const isMobileDevice = ref(false);

const checkMobile = () => {
  isMobileDevice.value =
    window.innerWidth <= 768 ||
    (window.innerHeight <= 500 && window.innerOrientation !== undefined);

  // 检测是否移动设备
  if (isMobileDevice.value) {
    // 使用CSS类来强制缩放为60%
    document.documentElement.classList.add("mobile-scaled-60");
    document.body.classList.add("mobile-scaled-60");
  } else {
    // 恢复正常大小
    document.documentElement.classList.remove("mobile-scaled-60");
    document.body.classList.remove("mobile-scaled-60");
  }

  console.log(
    "设备检测:",
    isMobileDevice.value ? "移动设备(60%)" : "桌面设备(100%)"
  );
};

onMounted(() => {
  // 初始检测设备类型
  checkMobile();

  // 监听窗口大小变化
  window.addEventListener("resize", checkMobile);

  // 使用DOM元素引用而不是字符串ID
  map = L.map(mapContainer.value, {
    center: [35.8617, 104.1954],
    zoom: isMobileDevice.value ? 2 : 2, // 移动设备默认缩小一级
    maxZoom: 18,
    minZoom: 2,
    attributionControl: true,
    zoomControl: false, // 禁用默认缩放控件
    worldCopyJump: true,
    crs: L.CRS.EPSG3857,
    fadeAnimation: false, // 禁用淡入淡出动画
    markerZoomAnimation: false, // 禁用标记缩放动画
    preferCanvas: true, // 使用 Canvas 渲染以提高性能
    // 限制地图移动范围，防止移出视图
    maxBounds: [
      [-85, -180], // 西南角
      [85, 250], // 东北角 - 放宽右边界限制
    ],
    maxBoundsViscosity: 1.0, // 完全限制在边界内
  });

  // 创建全局保存的图层引用
  window.mapLayers = {
    yandex: null,
    tdt: null,
  };

  // 使用Yandex Maps图层; 使用英语
  window.mapLayers.yandex = L.tileLayer(
    "https://core-renderer-tiles.maps.yandex.net/tiles?l=map&x={x}&y={y}&z={z}&scale=2&lang=en_US&pl=true&size=512",
    {
      subdomains: ["01", "02", "03", "04"],
      maxZoom: 19,
      attribution: "© Yandex",
      tileSize: 256,
      // lang: currentLocale.value === "zh-CN" ? "" : "en_US", // 根据语言切换标注
      className: "yandex-labels", // 添加自定义类名用于样式控制
    }
  ).addTo(map);

  // 天地图矢量注记图层
  window.mapLayers.tdt = L.tileLayer(
    "https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=" +
      tdtKey,
    {
      subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      maxZoom: 18,
      className: "tdt-labels", // 添加自定义类用于样式控制
      opacity: currentLocale.value === "zh-CN" ? 1 : 0, // 根据语言控制显示隐藏
    }
  ).addTo(map);

  // 监听语言变化
  watch(
    () => currentLocale.value,
    (newLang) => {
      // 更新 Yandex 图层语言
      window.mapLayers.yandex.setUrl(
        window.mapLayers.yandex._url.replace(
          /lang=\w+/,
          `lang=${newLang === "zh-CN" ? "" : "en_US"}`
        )
      );

      // 控制天地图标注显示隐藏
      window.mapLayers.tdt.setOpacity(newLang === "zh-CN" ? 1 : 0);

      // 重新渲染热门站点
      if (showHotStations.value) {
        renderHotStations();
      }

      // 更新当前显示的起点和终点名称
      if (selectedRouteIndex.value !== null && availableRoutes.value.length > 0) {
        const selectedRoute = availableRoutes.value[selectedRouteIndex.value];
        if (selectedRoute?.stations?.length > 0) {
          const startStation = selectedRoute.stations[0];
          const endStation = selectedRoute.stations[selectedRoute.stations.length - 1];

          // 根据新语言更新起点和终点名称
          startPoint.value = newLang === "zh-CN" ? startStation.name : (startStation.nameEn || startStation.name);
          destinationPoint.value = newLang === "zh-CN" ? endStation.name : (endStation.nameEn || endStation.name);
        }
      }

      // 重新渲染路线标记以更新站点名称标签
      if (availableRoutes.value.length > 0) {
        // 重新绘制所有已显示的路线以更新站点名称
        allRouteLayers.value.forEach((layers, routeIndex) => {
          if (layers && layers.length > 0) {
            // 清除该路线的图层
            layers.forEach((layer) => {
              if (map.hasLayer(layer)) {
                map.removeLayer(layer);
              }
            });

            // 重新绘制该路线以更新标记名称
            const route = availableRoutes.value[routeIndex];
            if (route && route.stations) {
              const newLayers = displayRoute(
                {
                  path: route.stations,
                  stations: route.stations,
                  trainInfo: {
                    type: transportMethod.value === "汽运" ? "公路线路" : "铁路线路",
                    trainNo: `方案${routeIndex + 1}`,
                    distance: calculateTotalDistance(route.stations),
                    totalTime: route.totalTime || "计算中...",
                  },
                },
                routeIndex,
                true
              );

              allRouteLayers.value[routeIndex] = newLayers;
            }
          }
        });
      }
    }
  );

  // 添加自定义样式
  const style = document.createElement("style");
  style.textContent = `
    .yandex-labels {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #0066ff !important;
    }
    .tdt-labels {
      font-size: 124px !important;
      font-weight: 500 !important;
      color: #ff4d4f !important;
    }
  `;
  document.head.appendChild(style);

  // 更新图层控制
  const baseLayers = {
    "Yandex Maps": window.mapLayers.yandex,
  };
  const overlays = {
    中文标注: window.mapLayers.tdt,
  };
  L.control.layers(baseLayers, overlays).addTo(map);

  L.control
    .scale({
      imperial: false,
      metric: true,
      position: "bottomright",
    })
    .addTo(map);

  L.control
    .mousePosition({
      position: "bottomleft",
      separator: " | ",
      emptyString: "未知",
      lngFirst: true,
      numDigits: 5,
    })
    .addTo(map);

  // 替换图层控制器的移除代码
  if (map.layersControl) {
    map.removeControl(map.layersControl);
  }

  // 添加自定义位置的缩放控件
  L.control
    .zoom({
      position: "topright",
      zoomInText: "+",
      zoomOutText: "-",
      zoomInTitle: "放大",
      zoomOutTitle: "缩小",
    })
    .addTo(map);

  // 添加热门站点图层控制
  overlays["热门站点"] = {
    layer: null,
    shown: true,
    toggle: () => {
      showHotStations.value = !showHotStations.value;
      if (showHotStations.value) {
        renderHotStations();
      } else {
        map.eachLayer((layer) => {
          if (layer instanceof L.Marker) {
            map.removeLayer(layer);
          }
        });
      }
    },
  };

  // 监听地图缩放
  map.on("zoomend moveend", () => {
    // 延迟执行以确保地图状态已更新
    setTimeout(() => {
      if (window.hotStationsLayer) {
        window.hotStationsLayer.eachLayer((layer) => {
          if (layer instanceof L.Marker) {
            layer.setLatLng(layer.getLatLng());
          }
        });
      }
    }, 100);
  });

  // 在 onMounted 中的地图初始化后添加缩放监听
  map.on("zoomend", () => {
    const currentZoom = map.getZoom();
    const markerLabels = document.querySelectorAll(".marker-label");
    markerLabels.forEach((label) => {
      label.style.display =
        currentZoom <= 4 || currentZoom >= 10 ? "none" : "block";
    });
  });

  // 添加地图移动和缩放事件监听
  map.on("movestart zoomstart", () => {
    // 只隐藏临时的供应商信息卡片，保持固定站点的信息卡显示
    showAvaterCart.value = false;
  });

  renderHotStations();
});

// 添加标签相关状态和映射
const tabMapping = {
  hot: "热门",
  history: "历史",
  cnRu: "中俄",
  cnAs: "中亚",
  cnEu: "中欧",
  cnSa: "东南亚",
};

const currentTab = ref("热门");
const searchHistory = ref([]); // 存储搜索历史
const tabData = {
  中俄: [],
  中亚: [],
  中欧: [],
  东南亚: [],
};

// 添加搜索历史记录函数
const addToHistory = (point) => {
  // Avoid duplicate entries
  const exists = searchHistory.value.some((item) => item.name === point);
  if (!exists) {
    // Try to find description from other data sources
    let description = "";

    // Look in resultsList (hot stations)
    const hotItem = resultsList.value.find((item) => item.name === point);
    if (hotItem && hotItem.description) {
      description = hotItem.description;
    }

    // If not found, check other tab data
    if (!description) {
      for (const key in tabData) {
        const tabItem = tabData[key].find((item) => item.name === point);
        if (tabItem && tabItem.description) {
          description = tabItem.description;
          break;
        }
      }
    }

    // Add new item to the beginning of history array
    searchHistory.value.unshift({
      name: point,
      description: description,
      timestamp: new Date().toLocaleString(),
    });

    // Limit history to 10 items
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10);
    }

    // Save to localStorage
    saveHistoryToLocalStorage();
  }
};

// 保存搜索历史到 localStorage
const saveHistoryToLocalStorage = () => {
  try {
    localStorage.setItem(
      "mapSearchHistory",
      JSON.stringify(searchHistory.value)
    );
  } catch (error) {
    console.error("Failed to save search history to localStorage:", error);
  }
};

// 从 localStorage 加载搜索历史
const loadHistoryFromLocalStorage = () => {
  try {
    const savedHistory = localStorage.getItem("mapSearchHistory");
    if (savedHistory) {
      searchHistory.value = JSON.parse(savedHistory);
    }
  } catch (error) {
    console.error("Failed to load search history from localStorage:", error);
  }
};

// 在组件卸载时清理
onUnmounted(() => {
  clearAllMarkers();
  // 清理地图事件监听
  if (map) {
    map.off("click");
    map.remove();
  }

  // 移除窗口事件监听器
  window.removeEventListener("resize", checkMobile);

  // 移除全局点击事件监听
  document.removeEventListener("click", handleClickOutside);

  // 移除移动设备缩放样式
  document.documentElement.classList.remove("mobile-scaled-60");
  document.body.classList.remove("mobile-scaled-60");
});

// 修改 computed 属性
const currentList = computed(() => {
  let list = [];
  switch (currentTab.value) {
    case "热门":
      list = resultsList.value;
      break;
    case "历史":
      list = searchHistory.value;
      break;
    case "中俄":
    case "中亚":
    case "中欧":
    case "东南亚":
      list = tabData[currentTab.value];
      break;
    default:
      list = [];
  }

  // 按首字母排序（除了历史标签，保持时间顺序）
  if (currentTab.value !== "历史") {
    list = [...list].sort((a, b) => {
      const firstLetterA = a.name.charAt(0).toUpperCase();
      const firstLetterB = b.name.charAt(0).toUpperCase();
      return firstLetterA.localeCompare(firstLetterB);
    });
  }

  // 返回所有数据，不再限制显示数量
  return list;
});

// 修改标签点击事件的处理方法
const handleTabClick = (tab) => {
  currentTab.value = tabMapping[tab];
  // 如果之前有路线显示，清除它们
  if (availableRoutes.value.length > 0) {
    clearRoutes();
  }
};

// 添加控制显示所有路线的状态
const showAllRoutes = ref(true);
let expanded = ref(true);
// 添加切换显示所有路线的方法
const toggleAllRoutes = () => {
  expanded.value = !expanded.value;
  showAllRoutes.value = !showAllRoutes.value;
};

// 添加运输方式选项
const transportMethods = [
  { label: `Rail(铁路)`, value: "铁路", disabled: false },
  { label: `Road(公路运输)(开发中)`, value: "汽运", disabled: true },
];
const isTransportDropdownOpen = ref(false);

// 添加切换运输方式的方法
const selectTransportMethod = (method) => {
  if (!method.disabled) {
    transportMethod.value = method.value;
    isTransportDropdownOpen.value = false;
  }
};

// 添加初始站相关数据
const startPoint = ref("");

// 只有选中路线时才显示初始站输入框
const showStartPoint = computed(() => {
  return selectedRouteIndex.value !== null && availableRoutes.value.length > 0;
});

// 监听选中路线变化，自动填充初始站
watch(
  [selectedRouteIndex, availableRoutes],
  ([idx, routes]) => {
    if (
      idx !== null &&
      routes &&
      routes[idx] &&
      routes[idx].stations &&
      routes[idx].stations.length > 0
    ) {
      startPoint.value = currentLocale.value === "zh-CN" ? routes[idx].stations[0].name : (routes[idx].stations[0].nameEn || routes[idx].stations[0].name);
    }
  },
  { immediate: true }
);

// 添加标记点管理
const currentMarkers = ref([]);

// 修改 clearAllMarkers 函数
const clearAllMarkers = () => {
  // 清除当前所有标记点
  currentMarkers.value.forEach((marker) => {
    if (map.hasLayer(marker)) {
      map.removeLayer(marker);
    }
  });
  currentMarkers.value = [];

  // 清除所有路线图层
  allRouteLayers.value.forEach((layers) => {
    layers.forEach((layer) => {
      if (map.hasLayer(layer)) {
        map.removeLayer(layer);
      }
    });
  });
  allRouteLayers.value = [];
};

// 修改清空路线方法
const clearRoutes = () => {
  startPoint.value = "";
  destinationPoint.value = "";
  clearAllMarkers();
  availableRoutes.value = [];
  if (earth3dRef.value && typeof earth3dRef.value.reset3D === "function") {
    earth3dRef.value.reset3D();
  }

  // 隐藏路线起终点供应商信息
  showStartAvaterCart.value = false;
  showEndAvaterCart.value = false;
  startStation.value = null;
  endStation.value = null;

  // 清空会员数据
  startMembers.value = { ordinary: [], super: [] };
  endMembers.value = { ordinary: [], super: [] };
  memberInfoData.value = null;

  // 显示热门站点
  showHotStations.value = true;
  renderHotStations();

  // 重置展开状态，确保containerHeight恢复600px
  expanded.value = true;
  showAllRoutes.value = true;

  // 重置路线索引
  selectedRouteIndex.value = null;
};

// 添加侧边栏显示状态
const showSidebar = ref(true);

// 添加切换侧边栏方法
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value;
};

// 添加控制avaterCart显示的状态
const showAvaterCart = ref(false);
const avaterCartPosition = ref({ x: 0, y: 0 });
const selectedStation = ref(null);

// 添加起点和终点站的供应商信息显示状态
const showStartAvaterCart = ref(false);
const showEndAvaterCart = ref(false);
const startStation = ref(null);
const endStation = ref(null);

// 添加会员信息卡片的状态
const memberInfoData = ref(null);
const memberInfoPosition = ref({
  position: "absolute",
  left: "0px",
  top: "0px",
  zIndex: "1000",
});

// 修改站点卡片位置计算逻辑
const cardOffsetLat = 0.005; // 定义卡片向下偏移的纬度距离
const cardOffsetLng = 0; // 定义卡片水平偏移的经度距离

const getCardPosition = (latlng) => {
  if (!latlng) return null;
  return {
    lat: latlng.lat + cardOffsetLat,
    lng: latlng.lng + cardOffsetLng,
  };
};

// 添加计算属性控制container高度
const containerHeight = computed(() => {
  // 在移动设备上始终使用自适应高度
  if (isMobileDevice.value) {
    return "auto"; // 移动设备一律使用auto高度
  }

  // 桌面设备使用固定高度
  if (availableRoutes.value.length === 0) return "600px";
  return expanded.value && showAllRoutes.value ? "650px" : "500px"; // 收起时高度为500px
});

// 添加拥堵情况和GPS舱位状态
const trafficCondition = ref("通畅"); // 添加拥堵情况，默认值为"通畅"
const gpsStatus = ref("充足"); // 添加GPS舱位状态，默认值为"充足"

const userStore = useUserStore();

const stationSuggestions = ref([]);
const showSuggestions = ref(false);

// 添加模糊查询方法
const handleStationInput = async (query) => {
  // 无论如何，先隐藏起始站搜索结果
  showStartSuggestions.value = false;

  if (!query) {
    stationSuggestions.value = [];
    showSuggestions.value = false;
    return;
  }
  try {
    await delay(300);
    const isChinese = /[\u4e00-\u9fa5]/.test(query);
    const params = isChinese
      ? { stationNameZh: query }
      : { stationNameEn: query };
    const res = await listStation(params);
    if (res.code === 200 && Array.isArray(res.rows)) {
      // 将结果转换为所需格式
      const stations = res.rows.map((item) => ({
        nameZh: item.stationNameZh,
        nameEn: item.stationNameEn,
      }));

      // 对结果进行排序
      stationSuggestions.value = sortStationsByQuery(
        stations,
        query,
        isChinese
      );
      showSuggestions.value = true;

      // 确保起始站搜索结果不显示
      showStartSuggestions.value = false;
    } else {
      stationSuggestions.value = [];
      showSuggestions.value = false;
    }
  } catch (e) {
    stationSuggestions.value = [];
    showSuggestions.value = false;
  }
};

// 按查询字符串排序站点
const sortStationsByQuery = (stations, query, isChinese) => {
  const lowerQuery = query.toLowerCase();

  return stations.sort((a, b) => {
    const nameA = isChinese ? a.nameZh : a.nameEn;
    const nameB = isChinese ? b.nameZh : b.nameEn;

    // 获取拼音首字母（如果是中文）或者直接取英文首字母
    const startWithQueryA = nameA.toLowerCase().startsWith(lowerQuery);
    const startWithQueryB = nameB.toLowerCase().startsWith(lowerQuery);

    // 首先按是否以查询字符串开头排序
    if (startWithQueryA && !startWithQueryB) return -1;
    if (!startWithQueryA && startWithQueryB) return 1;

    // 如果都是以查询字符串开头或都不是，按字母顺序排序
    return nameA.localeCompare(nameB);
  });
};

// 格式化站点名称，同时显示英文和中文名称
const formatStationName = (nameZh, nameEn) => {
  // 根据当前语言选择显示格式
  if (currentLocale.value === "zh-CN") {
    // 中文环境：优先显示中文，如果没有中文则显示英文
    return nameZh || nameEn || "";
  } else {
    // 英文环境：优先显示英文，如果没有英文则显示中文
    return nameEn || nameZh || "";
  }
};

// 选择建议
const selectStationSuggestion = (station) => {
  // 使用格式化函数显示站点名称
  destinationPoint.value = formatStationName(station.nameZh, station.nameEn);
  showSuggestions.value = false;
};

// 添加起始站建议相关状态
const startStationSuggestions = ref([]); // 添加起始站建议列表
const showStartSuggestions = ref(false); // 添加起始站建议显示状态

// 添加处理起始站输入的方法
const handleStartStationInput = async (query) => {
  // 无论如何，先隐藏目的地搜索结果
  showSuggestions.value = false;

  if (!query) {
    startStationSuggestions.value = [];
    showStartSuggestions.value = false;
    return;
  }
  try {
    await delay(300);
    const isChinese = /[\u4e00-\u9fa5]/.test(query);
    const params = isChinese
      ? { stationNameZh: query }
      : { stationNameEn: query };
    const res = await listStation(params);
    if (res.code === 200 && Array.isArray(res.rows)) {
      // 将结果转换为所需格式
      const stations = res.rows.map((item) => ({
        nameZh: item.stationNameZh,
        nameEn: item.stationNameEn,
      }));

      // 对结果进行排序
      startStationSuggestions.value = sortStationsByQuery(
        stations,
        query,
        isChinese
      );
      showStartSuggestions.value = true;

      // 确保目的地搜索结果不显示
      showSuggestions.value = false;
    } else {
      startStationSuggestions.value = [];
      showStartSuggestions.value = false;
    }
  } catch (e) {
    startStationSuggestions.value = [];
    showStartSuggestions.value = false;
  }
};

// 添加选择起始站建议的方法
const selectStartStationSuggestion = (station) => {
  // 使用格式化函数显示站点名称
  startPoint.value = formatStationName(station.nameZh, station.nameEn);
  showStartSuggestions.value = false;
};

// 在Vue组件选项中，靠近引入语句后添加provide共享
// 提供is3DMode状态给Earth3D组件
provide("is3DMode", is3DMode);
provide("currentLocale", currentLocale);

// 提供地图控制函数给Earth3D组件
provide("mapControl", {
  disable: () => {
    if (map) {
      map.dragging.disable();
      map.touchZoom.disable();
      map.doubleClickZoom.disable();
      map.scrollWheelZoom.disable();
      map.boxZoom.disable();
      map.keyboard.disable();
      map.tap?.disable();
    }
  },
  enable: () => {
    if (map) {
      map.dragging.enable();
      map.touchZoom.enable();
      map.doubleClickZoom.enable();
      map.scrollWheelZoom.enable();
      map.boxZoom.enable();
      map.keyboard.enable();
      map.tap?.enable();
    }
  },
  setControl: (enabled) => handleMapControl(enabled),
});

// 在script部分添加对Earth3D组件的引用并修改切换逻辑
const earth3dRef = ref(null);

// 添加新函数用于重新创建地图图层
const recreateMapLayers = () => {
  if (!map) return;

  console.log("开始重新创建地图图层");

  try {
    // 移除所有现有图层
    map.eachLayer((layer) => {
      if (layer instanceof L.TileLayer) {
        console.log("移除图层:", layer._url);
        map.removeLayer(layer);
      }
    });

    // 重新创建Yandex图层
    window.mapLayers.yandex = L.tileLayer(
      "https://core-renderer-tiles.maps.yandex.net/tiles?l=map&x={x}&y={y}&z={z}&scale=2&lang={lang}&pl=true&size=512",
      {
        subdomains: ["01", "02", "03", "04"],
        maxZoom: 19,
        attribution: "© Yandex",
        tileSize: 256,
        lang: currentLocale.value === "en_US" ? "" : "en_US", // 根据语言切换标注
        className: "yandex-labels", // 添加自定义类名用于样式控制
      }
    ).addTo(map);

    // 重新创建天地图矢量注记图层
    window.mapLayers.tdt = L.tileLayer(
      "https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=" +
        tdtKey,
      {
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        maxZoom: 18,
        className: "tdt-labels", // 添加自定义类用于样式控制
        opacity: currentLocale.value === "zh-CN" ? 1 : 0, // 根据语言控制显示隐藏
      }
    ).addTo(map);

    console.log("地图图层重建完成");

    // 触发一次地图重绘
    map.fire("moveend");

    // 如果有选中的路线，重新绘制
    if (selectedRouteIndex.value !== null && availableRoutes.value.length > 0) {
      // 只清除并重新绘制当前选中的路线
      const selectedIndex = selectedRouteIndex.value;
      const selectedLayers = allRouteLayers.value[selectedIndex];

      if (selectedLayers) {
        // 清除当前选中路线的图层
        selectedLayers.forEach((layer) => {
          if (map.hasLayer(layer)) {
            map.removeLayer(layer);
          }
        });

        // 重新绘制当前选中的路线
        const route = availableRoutes.value[selectedIndex];
        if (route && route.stations) {
          const layers = displayRoute(
            {
              path: route.stations,
              stations: route.stations,
              trainInfo: {
                type: transportMethod.value === "汽运" ? "公路线路" : "铁路线路",
                trainNo: `方案${selectedIndex + 1}`,
                distance: calculateTotalDistance(route.stations),
                totalTime: route.totalTime || "计算中...",
              },
            },
            selectedIndex,
            true
          );

          allRouteLayers.value[selectedIndex] = layers;
        }
      }
    }

    return true;
  } catch (error) {
    console.error("重建地图图层失败:", error);
    return false;
  }
};

// 添加卡片位置和显示状态
const startCardPos = ref({ x: 0, y: 0 });
const endCardPos = ref({ x: 0, y: 0 });
const showStartCard = ref(false);
const showEndCard = ref(false);
const startMembers = ref({ ordinary: [], super: [] });
const endMembers = ref({ ordinary: [], super: [] });

// 添加更新卡片位置的函数
function updateCardPositions() {
  if (!map) return;

  // 如果有选中的路线
  if (selectedRouteIndex.value !== null && availableRoutes.value.length > 0) {
    const route = availableRoutes.value[selectedRouteIndex.value];
    if (!route?.stations?.length) return;

    const routeStartStation = route.stations[0];
    const routeEndStation = route.stations[route.stations.length - 1];

    if (showStartCard.value && routeStartStation) {
      const point = map.latLngToContainerPoint([
        routeStartStation.lat,
        routeStartStation.lng,
      ]);
      startCardPos.value = {
        x: point.x,
        y: point.y + 30,
      };
    }

    if (showEndCard.value && routeEndStation) {
      const point = map.latLngToContainerPoint([
        routeEndStation.lat,
        routeEndStation.lng,
      ]);
      endCardPos.value = {
        x: point.x,
        y: point.y + 30,
      };
    }
  }
  // 如果没有选中路线但有会员卡片显示（自动显示的情况）
  else {
    if (showStartAvaterCart.value && startStation.value) {
      const point = map.latLngToContainerPoint([
        startStation.value.lat,
        startStation.value.lng,
      ]);
      startCardPos.value = {
        x: point.x,
        y: point.y + 30,
      };
    }

    if (showEndAvaterCart.value && endStation.value) {
      const point = map.latLngToContainerPoint([
        endStation.value.lat,
        endStation.value.lng,
      ]);
      endCardPos.value = {
        x: point.x,
        y: point.y + 30,
      };
    }
  }
}

// 在地图移动或缩放时更新卡片位置
onMounted(() => {
  if (map) {
    map.on("moveend zoomend", updateCardPositions);
  }
});

onUnmounted(() => {
  if (map) {
    map.off("moveend zoomend", updateCardPositions);
  }
});

// 添加屏幕方向状态和移动端横屏检测
const isLandscape = ref(window.innerWidth > window.innerHeight);
const showRotateHint = ref(false);
const isMobile = ref(window.innerWidth < 768);
const isMobileLandscape = ref(false);

// 增强的屏幕方向检测
onMounted(() => {
  const checkOrientation = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    isLandscape.value = width > height;
    isMobile.value = width < 768;
    isMobileLandscape.value = isMobile.value && isLandscape.value;

    // 只在移动设备且竖屏时显示旋转提示
    showRotateHint.value = isMobile.value && !isLandscape.value;

    // 移动端横屏时的特殊处理
    if (isMobileLandscape.value) {
      // 确保地图重新计算尺寸
      if (map) {
        setTimeout(() => {
          map.invalidateSize();
        }, 300);
      }
    }
  };

  checkOrientation();

  window.addEventListener("resize", checkOrientation);
  window.addEventListener("orientationchange", () => {
    // 延迟执行以确保屏幕旋转完成
    setTimeout(checkOrientation, 500);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", checkOrientation);
    window.removeEventListener("orientationchange", checkOrientation);
  });
});

// 添加更新AvaterCart位置的函数
function updateAvaterCartPositions() {
  // 如果地图未初始化或处于3D模式，不更新位置
  if (!map || is3DMode.value) return;

  // 优先使用选中的起始站卡片位置
  if (showStartAvaterCart.value) {
    // 首先检查是否有自定义的startStation
    if (startStation.value) {
      try {
        const point = map.latLngToContainerPoint([
          startStation.value.lat,
          startStation.value.lng,
        ]);
        startCardPos.value = {
          x: point.x,
          y: point.y + 30,
        };
        console.log("更新起始站卡片位置:", startCardPos.value);
      } catch (error) {
        console.error("更新起始站卡片位置出错:", error);
      }
    }
    // 如果没有自定义的，但有选中的路线
    else if (
      selectedRouteIndex.value !== null &&
      availableRoutes.value.length > 0
    ) {
      const route = availableRoutes.value[selectedRouteIndex.value];
      if (route?.stations?.length) {
        try {
          const routeStartStation = route.stations[0];
          const point = map.latLngToContainerPoint([
            routeStartStation.lat,
            routeStartStation.lng,
          ]);
          startCardPos.value = {
            x: point.x,
            y: point.y + 30,
          };
          console.log("更新路线起始站卡片位置:", startCardPos.value);
        } catch (error) {
          console.error("更新路线起始站卡片位置出错:", error);
        }
      }
    }
  }

  // 优先使用选中的终点站卡片位置
  if (showEndAvaterCart.value) {
    // 首先检查是否有自定义的endStation
    if (endStation.value) {
      try {
        const point = map.latLngToContainerPoint([
          endStation.value.lat,
          endStation.value.lng,
        ]);
        endCardPos.value = {
          x: point.x,
          y: point.y + 30,
        };
        console.log("更新终点站卡片位置:", endCardPos.value);
      } catch (error) {
        console.error("更新终点站卡片位置出错:", error);
      }
    }
    // 如果没有自定义的，但有选中的路线
    else if (
      selectedRouteIndex.value !== null &&
      availableRoutes.value.length > 0
    ) {
      const route = availableRoutes.value[selectedRouteIndex.value];
      if (route?.stations?.length) {
        try {
          const routeEndStation = route.stations[route.stations.length - 1];
          const point = map.latLngToContainerPoint([
            routeEndStation.lat,
            routeEndStation.lng,
          ]);
          endCardPos.value = {
            x: point.x,
            y: point.y + 30,
          };
          console.log("更新路线终点站卡片位置:", endCardPos.value);
        } catch (error) {
          console.error("更新路线终点站卡片位置出错:", error);
        }
      }
    }
  }
}

// 添加聊天窗口相关状态
const chatModalStore = useChatModalStore();
const chatModalRef = ref(null);

const handleClose = () => {
  chatModalStore.close();
  // 重新启用地图控制，确保关闭聊天窗口后地图可以正常操作
  handleMapControl(true);
  enableMapEvents();
};

// 登录弹窗相关状态与方法
const showLogin = ref(false);

const openLogin = () => {
  showLogin.value = true;
  // 禁用地图控制，防止弹窗打开时地图交互
  handleMapControl(false);

  // 触发登录弹窗显示事件，通知其他组件隐藏
  window.dispatchEvent(new CustomEvent('login-modal-show'));

  // 在下一个Tick后，检查是否需要显示手机号绑定弹窗
  nextTick(() => {
    if (loginRef.value && userStore.pendingWechatOpenid) {
      console.log("检测到待绑定的微信openid，初始化手机号绑定弹窗");
      loginRef.value.showBindPhoneModalWithOpenid(
        userStore.pendingWechatOpenid
      );
    }
  });
};

const closeLogin = () => {
  showLogin.value = false;
  // 重置用户存储中的登录弹窗状态
  userStore.showLoginDialog = false;
  // 重新启用地图控制
  handleMapControl(true);
  enableMapEvents();

  // 触发登录弹窗隐藏事件
  window.dispatchEvent(new CustomEvent('login-modal-hide'));
};

const handleLoginSuccess = () => {
  showLogin.value = false;
  // 重置用户存储中的登录弹窗状态
  userStore.showLoginDialog = false;
  // 清除可能存在的待绑定微信openid
  if (userStore.pendingWechatOpenid) {
    userStore.clearPendingWechatOpenid();
  }

  // 触发登录弹窗隐藏事件
  window.dispatchEvent(new CustomEvent('login-modal-hide'));

  // 重置搜索计数
  searchCount.value = 0;
  // 重新启用地图控制
  handleMapControl(true);
  enableMapEvents();
};

// 新增：监听 supplier 变化，自动初始化聊天
watch(
  () => [chatModalStore.visible, chatModalStore.currentSupplier],
  async ([visible, supplier]) => {
    if (visible && supplier && chatModalRef.value?.initializeChat) {
      await chatModalRef.value.initializeChat(supplier);
    }
  }
);

// 业务简介弹窗相关状态
const showBusinessIntroDialog = ref(false);
const businessIntroSupplier = ref(null);
const businessIntroContent = ref("");
const showBusinessCard = ref(false);
const businessCardImage = ref("");

// 添加emit定义，用于控制地图事件
const emit = defineEmits(["map-control"]);

// 打开业务简介弹窗
const showBusinessIntro = (supplier) => {
  console.log(supplier, "看看有什么");
  businessIntroSupplier.value = supplier;

  // 设置业务简介内容（根据会员类型提供不同的默认内容）
  if (supplier.memberType === "超级会员") {
    businessIntroContent.value = supplier.introduction;
  } else {
    businessIntroContent.value = supplier.introduction;
  }
  // 显示弹窗
  showBusinessIntroDialog.value = true;

  // 禁用页面滚动
  document.body.style.overflow = "hidden";
};

// 关闭业务简介弹窗
const closeBusinessIntroDialog = () => {
  showBusinessIntroDialog.value = false;
  showBusinessCard.value = false;

  // 恢复页面滚动
  document.body.style.overflow = "auto";
};

// 查看名片大图
const viewBusinessCard = () => {
  // 设置名片图片来源 - 只在有businessCard时才显示
  if (businessIntroSupplier.value?.businessCard) {
    businessCardImage.value = businessIntroSupplier.value.businessCard;

    // 显示名片大图
    showBusinessCard.value = true;

    console.log("显示名片大图:", businessCardImage.value);
  } else {
    console.log("用户暂未上传名片");
  }
};

// 关闭名片大图
const closeBusinessCard = () => {
  showBusinessCard.value = false;
};

// 处理显示会员信息
const handleShowMemberInfo = (data) => {
  // 保存会员信息
  memberInfoData.value = data;

  // 如果数据为null，则隐藏会员信息卡片
  if (!data) {
    return;
  }

  // 确保data和position都存在再计算卡片位置
  if (data.position) {
    const position = data.position;
    memberInfoPosition.value = {
      position: "fixed",
      left: `${position.left}px`,
      top: `${position.top}px`,
      transform: "translate(-50%, -110%)", // 水平居中，垂直方向放在头像上方
      zIndex: "1000",
    };

    // 添加一次性点击事件监听器，点击其他区域关闭会员信息卡片
    setTimeout(() => {
      const closeInfoCard = (e) => {
        // 检查点击是否在会员信息卡片外部
        const infoCard = document.querySelector(".member-info-popup");
        if (
          infoCard &&
          !infoCard.contains(e.target) &&
          !e.target.closest(".icon-wrapper")
        ) {
          memberInfoData.value = null;
          document.removeEventListener("click", closeInfoCard);
        }
      };

      document.addEventListener("click", closeInfoCard);
    }, 100);
  }
};

// 处理会员信息中的QQ聊天
const openQQChat = (qq) => {
  if (!qq) {
    alert("未提供QQ号码");
    return;
  }

  const url = `http://qm.qq.com/q/uin=${qq}&web_src=localhost&menu=yes`;
  window.open(url, "_blank");
};

// 处理会员信息中的联系按钮
const handleContactFromMemberInfo = (supplier) => {
  if (!userStore.token) {
    memberInfoData.value = null; // 关闭会员信息卡片
    openLogin();
    return;
  }

  if (supplier) {
    chatModalStore.open(supplier, true); // 传递第二个参数表示从会员卡片打开聊天
  }
  memberInfoData.value = null; // 关闭会员信息卡片
};

// 从业务简介弹窗中联系供应商
const handleContactFromIntro = () => {
  // 检查用户是否已登录
  if (!userStore.token) {
    // 用户未登录，关闭业务简介弹窗并显示登录对话框
    closeBusinessIntroDialog();
    userStore.showLoginDialog = true;
    return;
  }

  if (businessIntroSupplier.value) {
    // 先禁用地图控制
    emit("map-control", false);

    // 使用 chatModalStore 打开聊天弹窗并传递联系人
    chatModalStore.open(businessIntroSupplier.value, true); // 第二个参数表示从会员卡片打开

    // 最后关闭业务简介弹窗，避免关闭弹窗时触发问题
    nextTick(() => {
      closeBusinessIntroDialog();
    });
  }
};

// 添加跑马灯文字内容
const marqueeText = ref("");
const getgettonyonggfx = async () => {
  const res = await gettonyonggfx("公告");
  marqueeText.value = res.data.info;
};

// 添加轮播图数据 - 使用广告API数据
const carouselItems = ref([]);

// 获取并处理广告数据
const fetchAdvertisements = async () => {
  try {
    const response = await getadvertisement();
    if (response.code === 200 && Array.isArray(response.data)) {
      // 过滤有效的广告（状态为0表示启用）并按显示顺序排序
      const validAds = response.data
        .filter((ad) => ad.status === 0)
        .sort((a, b) => a.displayOrder - b.displayOrder)
        .map((ad) => ({
          id: ad.id,
          image: import.meta.env.VITE_BASE_API + ad.imageUrl,
          url: ad.linkUrl,
          userId: ad.userId,
          nickName: ad.nickName,
          title: `广告 #${ad.id}`,
          startDate: new Date(ad.startDate),
          endDate: new Date(ad.endDate),
        }));

      console.log(`获取到 ${validAds.length} 个有效广告`);

      // 如果有有效广告，使用它们
      if (validAds.length > 0) {
        carouselItems.value = validAds;
      } else {
        // 如果没有有效广告，设置默认图片
        carouselItems.value = [
          {
            id: 0,
            image: "/carousel/default.jpg",
            url: "#",
            title: "暂无广告",
          },
        ];
      }

      // 重置轮播索引
      activeIndex.value = 0;

      // 完全停止并重启轮播
      stopCarousel();
      setTimeout(() => {
        startCarousel();
      }, 100);
    }
  } catch (error) {
    console.error("获取广告数据失败:", error);
    // 设置默认图片
    carouselItems.value = [
      {
        id: 0,
        image: "/carousel/default.jpg",
        url: "#",
        title: "加载失败",
      },
    ];
  }
  showCarousel.value = true;
};

const activeIndex = ref(0);
const timer = ref(null);

// 自动轮播 - 简化为顺序播放
const startCarousel = () => {
  // 清除之前的定时器
  if (timer.value) {
    clearInterval(timer.value);
  }

  timer.value = setInterval(() => {
    // 简单地顺序切换到下一个广告
    activeIndex.value = (activeIndex.value + 1) % carouselItems.value.length;
    // console.log(`轮播切换到第 ${activeIndex.value + 1}/${carouselItems.value.length} 个广告`);
  }, 5000);
};

// 停止轮播
const stopCarousel = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
    console.log("轮播已停止");
  }
};

// 手动切换
const setActive = (index) => {
  // 停止当前的轮播
  stopCarousel();

  // 设置新的索引
  activeIndex.value = index;
  console.log(`手动切换到第 ${index + 1}/${carouselItems.value.length} 个广告`);

  // 重新开始轮播
  startCarousel();
};

// 点击跳转
const handleClick = (item) => {
  if (!item) return;
  
  // 检查是否有userId，如果有，则打开聊天框
  if (item.userId) {
    console.log(`打开与用户 ${item.userId} (${item.nickName || '未知用户'}) 的聊天窗口`);
    
    // 使用chatModalStore打开聊天窗口
    const supplierInfo = {
      id: item.userId,
      name: item.nickName || `用户${item.userId}`,
      avatar: '', // 可能需要根据实际情况设置头像
    };
    
    chatModalStore.open(supplierInfo);
    return;
  }
  
  // 如果没有userId但有链接，则打开链接
  if (item.url && item.url !== "#") {
    // 确保URL是完整的URL，如果不是则添加https://
    let finalUrl = item.url;
    if (!finalUrl.startsWith("http://") && !finalUrl.startsWith("https://")) {
      finalUrl = "https://" + finalUrl;
    }

    // 直接打开链接
    window.open(finalUrl, "_blank");
  }
};

// 轮播图显示控制
const showCarousel = ref(true);
const closeCarousel = () => {
  showCarousel.value = false;
};

onMounted(() => {
  startCarousel();
  getgettonyonggfx();

  // 添加对自定义事件的监听，用于处理微信登录回调
  window.addEventListener("show-login-modal", (event) => {
    console.log("收到show-login-modal事件:", event.detail);
    // 强制打开登录弹窗
    showLogin.value = true;
    handleMapControl(false);

    // 延迟一下，确保登录弹窗已完全打开
    setTimeout(() => {
      if (loginRef.value && userStore.pendingWechatOpenid) {
        console.log("WorldMap: 显示手机号绑定弹窗");
        loginRef.value.showBindPhoneModalWithOpenid(
          userStore.pendingWechatOpenid
        );
      }
    }, 300);
  });

  // 添加对新事件的监听，专门用于直接显示手机号绑定弹窗（跳过登录弹窗）
  window.addEventListener("show-bind-phone-modal", (event) => {
    console.log("收到show-bind-phone-modal事件:", event.detail);
    const { openid } = event.detail || {};

    // 如果有openid，则保存到store中
    if (openid) {
      userStore.setPendingWechatOpenid(openid);
    }

    // 直接显示绑定手机号弹窗
    console.log(
      "WorldMap: 直接显示手机号绑定弹窗，openid:",
      openid || userStore.pendingWechatOpenid || "未提供"
    );
    openBindPhone();
  });

  // 监听userStore中的showLoginDialog状态变化
  watch(
    () => userStore.showLoginDialog,
    (newVal) => {
      if (newVal) {
        console.log("检测到showLoginDialog变为true");
        openLogin();
        // 避免重复触发
        userStore.showLoginDialog = false;
      }
    }
  );

  // 监听userStore中的showBindPhoneModal状态变化
  watch(
    () => userStore.showBindPhoneModal,
    (newVal) => {
      console.log("检测到showBindPhoneModal变化:", newVal);
      if (newVal) {
        // 直接显示绑定手机号弹窗，完全独立于登录弹窗
        console.log(
          "直接显示绑定手机号弹窗，openid:",
          userStore.pendingWechatOpenid || "未提供"
        );
        openBindPhone();
      }
    }
  );
});

onUnmounted(() => {
  stopCarousel();
});

// 添加这个函数在组件的setup部分，用于管理移动设备上的下拉框位置
const positionMobileSuggestions = () => {
  // 只在移动设备上执行
  if (!isMobileDevice.value) return;

  nextTick(() => {
    // 获取当前活跃的输入框
    const activeInput = document.activeElement;
    if (!activeInput) return;

    // 判断是哪个输入框获得了焦点
    const isDestination =
      activeInput.getAttribute("v-model") === "destinationPoint";
    const isStart = activeInput.getAttribute("v-model") === "startPoint";

    if (!isDestination && !isStart) return;

    // 获取输入框的位置
    const rect = activeInput.getBoundingClientRect();

    // 获取对应的下拉框
    const suggestionsClass = isDestination
      ? ".destination-suggestions"
      : ".start-suggestions";
    const suggestionsList = document.querySelector(suggestionsClass);

    if (!suggestionsList) return;

    // 设置下拉框的位置 - 放在输入框下方但避免被其他内容遮挡
    const topPosition = Math.min(rect.bottom + 5, window.innerHeight * 0.3);

    // 应用样式
    suggestionsList.style.top = `${topPosition}px`;
    suggestionsList.style.left = `${rect.left}px`;
    suggestionsList.style.width = `${rect.width}px`;
    suggestionsList.style.maxHeight = `${window.innerHeight * 0.4}px`;
    suggestionsList.style.zIndex = "10000";
  });
};

// 监听窗口大小变化和滚动，重新定位下拉框
onMounted(() => {
  window.addEventListener("resize", positionMobileSuggestions);
  window.addEventListener("scroll", positionMobileSuggestions);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", positionMobileSuggestions);
  window.removeEventListener("scroll", positionMobileSuggestions);
});

// 修改输入框聚焦事件处理
const handleInputFocus = (isDestination = true) => {
  // 重新定位下拉框
  positionMobileSuggestions();

  // 确保对应的建议列表显示，其他隐藏
  if (isDestination) {
    if (stationSuggestions.value.length > 0) {
      showSuggestions.value = true;
    }
    showStartSuggestions.value = false;
  } else {
    if (startStationSuggestions.value.length > 0) {
      showStartSuggestions.value = true;
    }
    showSuggestions.value = false;
  }
};

// 登录弹窗组件
const loginRef = ref(null);

// 微信绑定手机号相关
const showBindPhone = ref(false);
const bindCooldown = ref(0);
const bindLoading = ref(false);
const showBindPassword = ref(false);
const showAgreementModal = ref(false);
const agreementType = ref("user");
const agreements = ref([]);
const userAgreementUrl = ref("");
const privacyAgreementUrl = ref("");

// 绑定手机号表单
const bindPhoneForm = reactive({
  phone: "",
  verificationCode: "",
  password: "",
  agreement: true,
});

// 微信绑定手机号相关方法
const openBindPhone = () => {
  showBindPhone.value = true;
  handleMapControl(false);
};

const closeBindPhone = () => {
  showBindPhone.value = false;
  handleMapControl(true);
  // 重置store状态
  userStore.clearPendingWechatOpenid();
  userStore.setShowBindPhoneModal(false);
};

// 发送绑定验证码
const sendBindVerificationCode = async () => {
  if (!bindPhoneForm.phone) {
    ElMessage.error(t("bindPhone.phoneRequired"));
    return;
  }

  // 验证手机号格式
  const phonePattern = /^1[3-9]\d{9}$/;
  if (!phonePattern.test(bindPhoneForm.phone)) {
    ElMessage.error(t("bindPhone.phoneFormat"));
    return;
  }

  try {
    const response = await getsendSms(bindPhoneForm.phone);

    if (response.code === 200) {
      userStore.setuuidAction(response.uuid);
      bindCooldown.value = 60;
      const timer = setInterval(() => {
        bindCooldown.value--;
        if (bindCooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      ElMessage.success(t("login.codeSent"));
    } else {
      ElMessage.error(response.msg || t("login.codeSendFailed"));
    }
  } catch (error) {
    ElMessage.error(t("login.codeSendError"));
    console.error(t("login.codeSendErrorLog"), error);
  }
};

// 确认绑定
const confirmBind = async () => {
  if (
    !bindPhoneForm.phone ||
    !bindPhoneForm.verificationCode ||
    !bindPhoneForm.password
  ) {
    ElMessage.error(t("register.completeInfo"));
    return;
  }

  // 验证手机号格式
  const phonePattern = /^1[3-9]\d{9}$/;
  if (!phonePattern.test(bindPhoneForm.phone)) {
    ElMessage.error(t("bindPhone.phoneFormat"));
    return;
  }

  if (!bindPhoneForm.agreement) {
    ElMessage.error(t("bindPhone.agreementRequired"));
    return;
  }

  bindLoading.value = true;

  try {
    // 准备请求参数
    const requestParams = {
      phoneNumber: bindPhoneForm.phone,
      password: bindPhoneForm.password,
      code: bindPhoneForm.verificationCode,
      uuid: userStore.uuid,
    };

    // 如果有openid，则添加绑定微信的参数
    if (userStore.pendingWechatOpenid) {
      requestParams.bindWechat = true;
      requestParams.wechatOpenid = userStore.pendingWechatOpenid;
    } else {
      ElMessage.error(t("bindPhone.failure"));
      bindLoading.value = false;
      return;
    }

    console.log("绑定请求参数:", requestParams);
    const { register } = await import("@/api/user");
    const response = await register(requestParams);

    if (response.code === 200) {
      // 直接使用注册接口返回的token进行登录处理
      await userStore.loginAction(response);

      ElMessage.success(t("bindPhone.success"));

      // 清理状态
      userStore.clearPendingWechatOpenid();
      userStore.setShowBindPhoneModal(false);

      // 关闭弹窗
      closeBindPhone();
    } else {
      ElMessage.error(response.msg || t("bindPhone.failure"));
    }
  } catch (error) {
    console.error("绑定手机号错误:", error);
    ElMessage.error(t("bindPhone.failure"));
  } finally {
    bindLoading.value = false;
  }
};

// 显示协议弹窗
// 获取协议列表
const fetchAgreements = async () => {
  try {
    const res = await getxieyiList();
    if (res.code === 200 && res.data && res.data.length > 0) {
      agreements.value = res.data;

      // 查找用户协议和隐私协议
      const userAgreement = agreements.value.find(
        (item) => item.name === "用户协议"
      );
      const privacyAgreement = agreements.value.find(
        (item) => item.name === "隐私协议"
      );

      if (userAgreement) {
        userAgreementUrl.value = userAgreement.url;
      }

      if (privacyAgreement) {
        privacyAgreementUrl.value = privacyAgreement.url;
      }
    }
  } catch (error) {
    console.error("获取协议列表失败:", error);
  }
};

const baseUrl = import.meta.env.VITE_BASE_API;

const showAgreement = (type) => {
  agreementType.value = type;
  showAgreementModal.value = true;
};

// 同意协议
const agreeToTerms = () => {
  bindPhoneForm.agreement = true;
  showAgreementModal.value = false;
};

// 微信绑定相关逻辑已移至路由和事件监听器中

// 监听store中的showBindPhoneModal状态
watch(
  () => userStore.showBindPhoneModal,
  (newVal) => {
    if (newVal) {
      console.log("检测到showBindPhoneModal变化:", newVal);
      if (newVal) {
        // 直接显示绑定手机号弹窗，完全独立于登录弹窗
        console.log(
          "直接显示绑定手机号弹窗，openid:",
          userStore.pendingWechatOpenid || "未提供"
        );
        openBindPhone();
      }
    }
  }
);

// 监听聊天窗口打开事件，关闭会员信息卡片
const handleChatModalOpened = () => {
  // 隐藏会员信息卡片
  memberInfoData.value = null;
};

// 在组件挂载时添加事件监听
onMounted(() => {
  // 添加聊天窗口打开事件监听
  window.addEventListener("chat-modal-opened", handleChatModalOpened);
});

// 组件卸载前移除事件监听器
onBeforeUnmount(() => {
  // 移除聊天窗口打开事件监听
  window.removeEventListener("chat-modal-opened", handleChatModalOpened);
});
</script>

<template>
  <div id="map" :class="{ 'mobile-landscape': isMobileLandscape }">
    <!-- 添加横屏提示 -->
    <div v-if="showRotateHint" class="rotate-hint">
      <div class="rotate-hint-content">
        <div class="rotate-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="48"
            height="48"
          >
            <path
              fill="currentColor"
              d="M7.34 6.41L.86 12.9l6.49 6.48L8.76 18 3.66 12.9l5.1-5.1z"
            />
            <path
              fill="currentColor"
              d="M15.66 6.41l6.48 6.48-6.48 6.48L14.24 18l5.1-5.1-5.1-5.1z"
            />
          </svg>
        </div>
        <div class="rotate-text">{{ t("worldMap.rotateHint.title") }}</div>
        <div class="rotate-subtext">
          {{ t("worldMap.rotateHint.subtitle") }}
        </div>
      </div>
    </div>

    <!-- Nav组件始终显示 -->
    <div
      class="nav-container"
      :class="{ 'nav-mobile-landscape': isMobileLandscape }"
    >
      <Nav
        @map-control="handleMapControl"
        @toggle-3d="handle3DToggle"
        @login-click="openLogin"
      />
    </div>

    <!-- 条件渲染3D地图组件 - 只在3D模式下显示 -->
    <div
      v-if="is3DMode"
      class="earth3d-container"
      :class="{ 'earth3d-mobile-landscape': isMobileLandscape }"
    >
      <Earth3D ref="earth3dRef" class="earth3d" />
    </div>

    <!-- 2D地图相关组件，只在非3D模式下显示 -->
    <div
      v-show="!is3DMode"
      ref="mapContainer"
      class="leaflet-container"
      :class="{ 'leaflet-mobile-landscape': isMobileLandscape }"
    ></div>

    <!-- 测距工具始终显示 -->
    <div
      class="map-tools"
      :class="{ 'tools-mobile-landscape': isMobileLandscape }"
    >
      <DistanceTool :map="map" />
    </div>

    <!-- RouteInfoContainer组件始终显示 -->
    <div class="route-info-container">
      <!-- 添加 transform 过渡效果 -->
      <div
        class="container"
        :class="{
          'sidebar-hidden': !showSidebar,
          'container-mobile-landscape': isMobileLandscape,
          'mobile-container': isMobileDevice,
          'mobile-view': isMobileDevice /* 添加新样式类用于专门控制移动视图 */,
          'show-dropdown': showSuggestions || showStartSuggestions || isTransportDropdownOpen /* 当有下拉框显示时添加类 */,
        }"
        :style="{
          height: isMobileDevice ? 'auto' : containerHeight,
          overflow: (showSuggestions || showStartSuggestions || isTransportDropdownOpen)
            ? 'visible'
            : 'hidden' /* 只在显示下拉框时设置为visible，否则隐藏滚动条 */,
        }"
        @mouseenter="disableMapEvents"
        @mouseleave="enableMapEvents"
      >
        <!-- 固定部分 -->
        <div
          class="fixed-header"
          :class="{ 'header-mobile-landscape': isMobileLandscape }"
        >
          <div class="header">
            <div
              class="logo-container"
              :class="{ 'logo-mobile-landscape': isMobileLandscape }"
            >
              <img src="/image/newMaptitlelogo.png" alt="New Map" class="logo-image" />
            </div>
          </div>

          <div
            class="search-form"
            :class="{ 'form-mobile-landscape': isMobileLandscape }"
          >
            <div
              class="form-group"
              :class="{ 'group-mobile-landscape': isMobileLandscape }"
            >
              <div
                class="select-container"
                @click="isTransportDropdownOpen = !isTransportDropdownOpen"
              >
                <div class="select-value">{{ transportMethod === "铁路" ? "Rail(铁路)" : "Road(公路运输)" }}</div>
                <div
                  class="select-arrow"
                  :class="{ open: isTransportDropdownOpen }"
                >
                  ▼
                </div>
                <div v-if="isTransportDropdownOpen" class="select-dropdown">
                  <div
                    v-for="method in transportMethods"
                    :key="method.value"
                    class="select-option"
                    :class="{
                      selected: method.value === transportMethod,
                      disabled: method.disabled,
                    }"
                    @click="selectTransportMethod(method)"
                  >
                    {{ method.label }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 如果是汽运则显示初始站 -->
            <div
              class="form-group"
              v-if="showStartPoint"
              :class="{ 'group-mobile-landscape': isMobileLandscape }"
            >
              <div class="radio-group">
                <label class="radio-label">
                  <input
                    type="radio"
                    v-model="stationType"
                    value="start"
                    class="radio-input"
                  />

                  <span>{{ t("worldMap.stationType.start") }}</span>
                </label>
              </div>
              <div class="select-container">
                <input
                  v-model="startPoint"
                  @input="handleStartStationInput(startPoint)"
                  @focus="
                    handleInputFocus(false);
                    handleStartStationInput(startPoint);
                  "
                  @blur="handleSearchBlur"
                  type="text"
                  :placeholder="t('worldMap.placeholders.start')"
                  class="search-input"
                  :class="{ 'input-mobile-landscape': isMobileLandscape }"
                  autocomplete="off"
                />
                <div
                  v-if="showStartSuggestions && startStationSuggestions.length"
                  class="station-suggestions-list start-suggestions"
                  :class="{ 'suggestions-mobile-landscape': isMobileLandscape }"
                  :style="
                    isMobileDevice ? { top: window.innerHeight / 2 + 'px' } : {}
                  "
                >
                  <div class="suggestions-header">
                    {{ t("worldMap.searchResults") }}
                  </div>
                  <div
                    v-for="(station, idx) in startStationSuggestions"
                    :key="station.nameZh + station.nameEn + idx"
                    class="station-suggestion-item"
                    @mousedown.prevent="selectStartStationSuggestion(station)"
                  >
                    <div class="suggestion-content">
                      <div class="suggestion-name">{{ station.nameZh }}</div>
                      <div v-if="station.nameEn" class="suggestion-en-name">
                        ({{ station.nameEn }})
                      </div>
                    </div>
                    <div class="suggestion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M9 18l6-6-6-6" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="form-group"
              :class="{ 'group-mobile-landscape': isMobileLandscape }"
            >
              <div class="radio-group">
                <label class="radio-label">
                  <input
                    type="radio"
                    v-model="stationType"
                    value="destination"
                    class="radio-input"
                  />

                  <span>{{ t("worldMap.stationType.destination") }}</span>
                </label>
              </div>
              <div class="select-container">
                <input
                  v-model="destinationPoint"
                  @input="handleStationInput(destinationPoint)"
                  @focus="
                    handleInputFocus(true);
                    handleStationInput(destinationPoint);
                  "
                  @blur="handleSearchBlur"
                  @keyup.enter="planRoute"
                  type="text"
                  :placeholder="t('worldMap.placeholders.destination')"
                  class="search-input"
                  :class="{ 'input-mobile-landscape': isMobileLandscape }"
                  autocomplete="off"
                />
                <div
                  v-if="showSuggestions && stationSuggestions.length"
                  class="station-suggestions-list destination-suggestions"
                  :class="{ 'suggestions-mobile-landscape': isMobileLandscape }"
                  :style="
                    isMobileDevice ? { top: window.innerHeight / 2 + 'px' } : {}
                  "
                >
                  <div class="suggestions-header">
                    {{ t("worldMap.searchResults") }}
                  </div>
                  <div
                    v-for="(station, idx) in stationSuggestions"
                    :key="station.nameZh + station.nameEn + idx"
                    class="station-suggestion-item"
                    @mousedown.prevent="selectStationSuggestion(station)"
                  >
                    <div class="suggestion-content">
                      <div class="suggestion-name">{{ station.nameZh }}</div>
                      <div v-if="station.nameEn" class="suggestion-en-name">
                        ({{ station.nameEn }})
                      </div>
                    </div>
                    <div class="suggestion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M9 18l6-6-6-6" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="button-group"
              :class="{ 'buttons-mobile-landscape': isMobileLandscape }"
            >
              <button
                class="search-button"
                :class="{ 
                  'button-mobile-landscape': isMobileLandscape,
                  'cancel-search': isPlanning && canCancelQuery
                }"
                @click="planRoute"
              >
                <el-icon class="search-icon">
                  <template v-if="isPlanning && canCancelQuery">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
                      <path fill="currentColor" d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/>
                    </svg>
                  </template>
                  <Search v-else />
                </el-icon>
                {{
                  isPlanning ? 
                    (canCancelQuery ? "取消查询" : "查询中...") : 
                    t("worldMap.inquire")
                }}
              </button>
              <button
                class="clear-button"
                :class="{ 'button-mobile-landscape': isMobileLandscape }"
                @click="clearRoutes"
                :title="t('worldMap.clear')"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </button>
            </div>
          </div>

          <div
            class="tabs"
            :class="{ 'tabs-mobile-landscape': isMobileLandscape }"
          >
            <div
              v-for="tab in ['hot', 'history', 'cnRu', 'cnAs', 'cnEu', 'cnSa']"
              :key="tab"
              :class="[
                'tab',
                { active: currentTab === tabMapping[tab] },
                { 'tab-mobile-landscape': isMobileLandscape },
              ]"
              @click="handleTabClick(tab)"
            >
              {{ t(`worldMap.tabs.${tab}`) }}
            </div>
          </div>
        </div>

        <!-- 可滚动部分 -->
        <div
          class="scrollable-content"
          :class="{
            'content-mobile-landscape': isMobileLandscape,
            'pc-scrollable': !isMobileDevice /* PC端专用滚动类 */,
            'mobile-visible': isMobileDevice /* 移动端专用显示类 */,
          }"
        >
          <div v-if="availableRoutes.length > 0" class="route-options">
            <div
              class="route-options-title"
              :class="{ 'title-mobile-landscape': isMobileLandscape }"
            >
              {{ t("worldMap.routeOptions.title") }}
              <span class="route-count">{{
                t("worldMap.routeOptions.count", {
                  count: availableRoutes.length,
                })
              }}</span>
              <span class="toggle-routes" @click="toggleAllRoutes">
                {{
                  expanded
                    ? t("worldMap.routeOptions.collapse")
                    : t("worldMap.routeOptions.expand")
                }}
                <svg
                  class="dropdown-arrow"
                  :class="{ 'is-expanded': expanded }"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </span>
            </div>

            <div
              class="route-options-list"
              :class="{ 'list-mobile-landscape': isMobileLandscape }"
            >
              <!-- 当展开全部路线时，不显示单独的选中路线 -->
              <template v-if="!showAllRoutes">
                <div
                  class="listcard active"
                  :class="{ 'card-mobile-landscape': isMobileLandscape }"
                  :key="selectedRouteIndex"
                >
                  <div class="headercard">
                    <div
                      class="route-name"
                      style="color: #fca61b; font-weight: 900; font-size: 14px"
                    >
                      {{ availableRoutes[selectedRouteIndex].stations[0].name }}
                      -
                      {{
                        availableRoutes[selectedRouteIndex].stations[
                          availableRoutes[selectedRouteIndex].stations.length -
                            1
                        ].name
                      }}
                    </div>
                    <div class="tag">路线 {{ selectedRouteIndex + 1 }}</div>
                  </div>

                  <div class="info-grid">
                    <div class="info-row">
                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.borderPort") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ availableRoutes[selectedRouteIndex].isExitPort }}
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.duration") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ availableRoutes[selectedRouteIndex].totalTime }}
                        </div>
                      </div>
                    </div>

                    <div class="info-row aligned-info">
                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.mileage") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{
                            availableRoutes[selectedRouteIndex].totalMileage
                          }}km
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.trafficCondition") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{
                            availableRoutes[selectedRouteIndex].trafficCondition
                          }}
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.availability") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ availableRoutes[selectedRouteIndex].gpsStatus }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 展示所有路线 -->
              <template v-if="showAllRoutes">
                <div
                  class="listcard"
                  v-for="(route, index) in availableRoutes"
                  :key="index"
                  :class="{ active: index === selectedRouteIndex }"
                  @click="onRouteClick(index)"
                >
                  <div class="headercard">
                    <div
                      class="route-name"
                      :class="{ qweactive: index === selectedRouteIndex }"
                    >
                      {{ route.stations[0].name }} -
                      {{ route.stations[route.stations.length - 1].name }}
                    </div>
                    <div class="tag">路线 {{ index + 1 }}</div>
                  </div>

                  <div class="info-grid">
                    <div class="info-row">
                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.borderPort") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ route.isExitPort }}
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.duration") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ route.totalTime }}
                        </div>
                      </div>
                    </div>

                    <div class="info-row aligned-info">
                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.mileage") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ route.totalMileage }}km
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.trafficCondition") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ route.trafficCondition }}
                        </div>
                      </div>

                      <div class="info-item">
                        <div class="info-label">
                          {{ t("worldMap.routeInfo.availability") }}:
                        </div>
                        <div class="info-value" style="font-weight: 900">
                          {{ route.gpsStatus }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <div
            v-else
            class="results-list"
            :class="{ 'results-mobile-landscape': isMobileLandscape }"
          >
            <div class="results-list-scroll">
              <div
                v-for="(item, index) in currentList"
                :key="index"
                class="result-item"
                :class="{ 'item-mobile-landscape': isMobileLandscape }"
                @click="
                  destinationPoint = formatStationName(item.nameZh, item.nameEn) || item.name;
                  planRoute();
                "
              >
                {{ formatStationName(item.nameZh, item.nameEn) || item.name }}
                <span class="result-description" v-if="item.description !== ''">
                  ( {{ item.description }} )
                  <span v-if="currentTab === '历史'" class="history-time">
                    {{ item.timestamp }}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加控制按钮组 -->
      <div
        class="control-buttons"
        :class="{
          'buttons-shifted': !showSidebar,
          'controls-mobile-landscape': isMobileLandscape,
          'mobile-controls': isMobileDevice,
        }"
      >
        <button
          class="control-btn toggle-btn"
          :class="{ 'btn-mobile-landscape': isMobileLandscape }"
          @click="toggleSidebar"
          :title="showSidebar ? '收起侧边栏' : '展开侧边栏'"
        >
          <span>{{ showSidebar ? "◀" : "▶" }}</span>
        </button>

        <!-- 测试微信绑定按钮 -->
        <!-- <button
          v-if="import.meta.env.DEV"
          class="control-btn test-btn"
          @click="testWechatBind"
          title="测试微信绑定"
          style="margin-top: 10px;"
        >
          <span>测试</span>
        </button> -->
      </div>
    </div>

    <!-- <div
      class="div-image"
      :class="{ 'image-mobile-landscape': isMobileLandscape }"
      :style="{ display: !showAllRoutes ? 'block' : 'none' }"
      :style="{ display: !showAllRoutes ? 'block' : 'none' }"
    >
      <div class="image-container">
        <img src="../../public/image.png" alt="" class="adaptive-image" />
      </div>
    </div> -->

    <!-- 添加右侧菜单组件 -->
    <Right
      class="right-menu"
      :class="{ 'menu-mobile-landscape': isMobileLandscape }"
      @mouseenter="disableMapEvents"
      @mouseleave="enableMapEvents"
    />

    <!-- 站点信息卡片 -->
    <div
      v-if="showStartCard && availableRoutes.length > 0 && !is3DMode"
      :style="{
        position: 'absolute',
        left: startCardPos.x - 100 + 'px',
        top: startCardPos.y + 5 + 'px',
        zIndex: 30,
        transform: isMobileLandscape
          ? 'translate(-50%, -100%) scale(0.6)'
          : 'translate(-50%, -100%) scale(0.7)',
        transition: 'all 0.1s ease-out',
        transformOrigin: 'center bottom',
      }"
      class="avater-cart-container"
      :class="{ 'cart-mobile-landscape': isMobileLandscape }"
      @mouseenter="disableMapEvents"
      @mouseleave="enableMapEvents"
    >
      <!-- AvaterCart组件只在2D模式下显示，3D模式下隐藏 -->
      <AvaterCart
        :ordinary-members="startMembers.ordinary"
        :super-members="startMembers.super"
        :station="selectedStation"
        @close="showAvaterCart = false"
        @show-business-intro="showBusinessIntro"
        @show-member-info="handleShowMemberInfo"
      />
    </div>

    <div
      v-if="showEndCard && availableRoutes.length > 0 && !is3DMode"
      :style="{
        position: 'absolute',
        left: endCardPos.x - 100 + 'px',
        top: endCardPos.y + 6 + 'px',
        zIndex: 30,
        transform: isMobileLandscape
          ? 'translate(-50%, -100%) scale(0.6)'
          : 'translate(-50%, -100%) scale(0.7)',
        transition: 'all 0.1s ease-out',
        transformOrigin: 'center bottom',
      }"
      class="avater-cart-container"
      :class="{ 'cart-mobile-landscape': isMobileLandscape }"
      @mouseenter="disableMapEvents"
      @mouseleave="enableMapEvents"
    >
      <!-- AvaterCart组件只在2D模式下显示，3D模式下隐藏 -->
      <AvaterCart
        :ordinary-members="endMembers.ordinary"
        :super-members="endMembers.super"
        :station="selectedStation"
        @close="showAvaterCart = false"
        @show-business-intro="showBusinessIntro"
        @show-member-info="handleShowMemberInfo"
      />
    </div>

    <!-- 登录弹窗组件 -->
    <Login
      ref="loginRef"
      v-if="showLogin"
      :is-visible="showLogin"
      @close="closeLogin"
      @login-success="handleLoginSuccess"
    />

    <!-- 微信绑定手机号弹窗 -->
    <div v-if="showBindPhone" class="wechat-bind-overlay">
      <div class="wechat-bind-container">
        <div class="wechat-bind-content">
          <div class="wechat-bind-header">
            <h3>{{ t("bindPhone.title") }}</h3>
            <button class="close-button" @click="closeBindPhone">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div class="wechat-bind-body">
            <p class="wechat-bind-desc">{{ t("bindPhone.description") }}</p>

            <div class="form-group">
              <label for="bind-phone">{{ t("bindPhone.phone") }}</label>
              <input
                type="tel"
                id="bind-phone"
                v-model="bindPhoneForm.phone"
                :placeholder="t('bindPhone.phonePlaceholder')"
                class="form-input"
                autocomplete="tel"
              />
            </div>

            <div class="form-group">
              <label for="bind-code">{{
                t("bindPhone.verificationCode")
              }}</label>
              <div class="verification-code-container">
                <input
                  type="text"
                  id="bind-code"
                  v-model="bindPhoneForm.verificationCode"
                  :placeholder="t('bindPhone.codePlaceholder')"
                  class="form-input"
                  inputmode="numeric"
                  maxlength="6"
                />
                <button
                  type="button"
                  class="send-code-button"
                  @click="sendBindVerificationCode"
                  :disabled="bindCooldown > 0"
                >
                  {{
                    bindCooldown > 0
                      ? `${bindCooldown}${t("bindPhone.seconds")}`
                      : t("bindPhone.getCode")
                  }}
                </button>
              </div>
            </div>

            <div class="form-group">
              <label for="bind-password">{{ t("bindPhone.password") }}</label>
              <div class="password-input-container">
                <input
                  :type="showBindPassword ? 'text' : 'password'"
                  id="bind-password"
                  v-model="bindPhoneForm.password"
                  :placeholder="t('bindPhone.passwordPlaceholder')"
                  class="form-input"
                />
                <button
                  type="button"
                  class="toggle-password"
                  @click="showBindPassword = !showBindPassword"
                >
                  <svg
                    v-if="!showBindPassword"
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"
                    ></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                    <path
                      d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
                    ></path>
                    <path
                      d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
                    ></path>
                    <line x1="2" x2="22" y1="2" y2="22"></line>
                  </svg>
                </button>
              </div>
            </div>

            <div class="agreement-section">
              <div class="agreement-checkbox">
                <input
                  type="checkbox"
                  id="wechat-bind-agreement"
                  v-model="bindPhoneForm.agreement"
                />
                <label for="wechat-bind-agreement">
                  {{ t("bindPhone.agreementPrefix") }}
                  <a href="#" @click.prevent="showAgreement('user')">{{
                    t("agreement.userAgreement")
                  }}</a>
                  {{ t("agreement.and") }}
                  <a href="#" @click.prevent="showAgreement('privacy')">{{
                    t("agreement.privacyPolicy")
                  }}</a>
                </label>
              </div>
            </div>
          </div>

          <div class="wechat-bind-footer">
            <button class="secondary-button" @click="closeBindPhone">
              {{ t("bindPhone.cancel") }}
            </button>
            <button
              class="primary-button"
              @click="confirmBind"
              :disabled="bindLoading"
            >
              <span v-if="!bindLoading">{{ t("bindPhone.confirm") }}</span>
              <span v-else class="loading-spinner"></span>
            </button>
          </div>
        </div>
      </div>

      <!-- 协议弹窗 -->
      <div v-if="showAgreementModal" class="agreement-modal">
        <div class="agreement-modal-content">
          <div class="agreement-modal-header">
            <h3>
              {{
                agreementType === "user"
                  ? t("agreement.userAgreementTitle")
                  : t("agreement.privacyAgreementTitle")
              }}
            </h3>
            <button @click="showAgreementModal = false">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
          <div class="agreement-modal-body">
            <div v-if="agreementType === 'user'">
              <h4>{{ t("agreement.userAgreementTitle") }}</h4>
              <p v-if="!userAgreementUrl">
                {{ t("agreement.userAgreementContent") }}
              </p>
              <p v-else>
                {{ t("loading") }}...
                <a :href="baseUrl + userAgreementUrl" target="_blank">{{
                  t("clickToView")
                }}</a>
              </p>
            </div>
            <div v-else>
              <h4>{{ t("agreement.privacyAgreementTitle") }}</h4>
              <p v-if="!privacyAgreementUrl">
                {{ t("agreement.privacyAgreementContent") }}
              </p>
              <p v-else>
                {{ t("loading") }}...
                <a :href="baseUrl + privacyAgreementUrl" target="_blank">{{
                  t("clickToView")
                }}</a>
              </p>
            </div>
          </div>
          <div class="agreement-modal-footer">
            <button class="primary-button" @click="agreeToTerms">
              {{ t("agree") }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <VoucherGiftModal
      v-if="userStore.showVoucherModal"
      :voucherData="userStore.currentVoucher"
    />

    <!-- 聊天窗口组件 -->
    <ChatModal
      ref="chatModalRef"
      :is-visible="chatModalStore.visible"
      @close="handleClose"
    />

    <!-- 会员详细信息卡片 - 独立显示 -->
    <div
      v-if="memberInfoData"
      class="member-info-popup"
      :style="memberInfoPosition"
    >
      <div class="member-info-card">
        <div class="member-header">
          <div class="avatar-container">
            <img
              class="member-avatar"
              :src="
                memberInfoData.supplier.avatar || 'https://picsum.photos/50/50'
              "
              alt="头像"
            />
            <div class="member-type-badge" :class="memberInfoData.supplier.memberType === '超级会员' ? 'vip-badge' : 'normal-badge'">VIP</div>
          </div>
          <div class="member-info">
            <div class="member-name-row">
              <span class="member-name">{{
                memberInfoData.supplier.name
              }}</span>
              <span class="member-title">{{
                memberInfoData.supplier.title || ""
              }}</span>
            </div>
            <div class="company-name">
              {{ memberInfoData.supplier.company }}
            </div>
          </div>
          <button
            class="private-msg-btn"
            @click.stop="handleContactFromMemberInfo(memberInfoData.supplier)"
          >
            私信
          </button>
        </div>

        <div class="member-contact-info">
          <div class="contact-info-row">
            <span class="contact-icon">QQ</span>
            <span class="contact-value">{{
              memberInfoData.supplier.qq || "暂未填写"
            }}</span>
            <span class="contact-icon id-icon">📞</span>
            <span class="contact-value">{{
              memberInfoData.supplier.phone || "暂未填写"
            }}</span>
          </div>
        </div>

        <div class="member-actions">
          <button
            class="action-btn business-intro-btn"
            @click.stop="showBusinessIntro(memberInfoData.supplier)"
          >
            业务简介
          </button>
        </div>
      </div>
      <!-- 小三角形 -->
      <div class="member-info-arrow"></div>
    </div>

    <!-- 业务简介弹窗 -->
    <div
      v-if="showBusinessIntroDialog"
      class="business-intro-dialog-overlay"
      @click="closeBusinessIntroDialog"
    >
      <div class="business-intro-dialog" @click.stop>
        <div class="business-intro-header">
          <h3>{{ t("worldMap.businessIntro.title") }}</h3>
          <button
            class="business-intro-close"
            @click="closeBusinessIntroDialog"
          >
            ×
          </button>
        </div>

        <div class="business-intro-body">
          <div class="intro-content">
            <div class="intro-header" v-if="businessIntroSupplier">
              <div class="intro-avatar">
                <img
                  :src="businessIntroSupplier.avatar"
                  :alt="businessIntroSupplier.name"
                />
                <div
                  v-if="businessIntroSupplier.businessCard !== null"
                  class="business-card-thumbnail"
                  @click="viewBusinessCard"
                >
                  <img
                    :src="businessIntroSupplier.businessCard"
                    alt="名片"
                  />
                  <div class="card-overlay">
                    点击查看名片
                  </div>
                </div>
                <div
                  v-else
                  class="business-card-thumbnail empty-card"
                >
                  <span class="card-icon">📇</span>
                  <div class="empty-card-text">
                    用户暂时未上传名片
                  </div>
                </div>
              </div>
              <div class="intro-user-info">
                <div class="intro-name">{{ businessIntroSupplier.name }}</div>
                <div class="intro-company">
                  {{ businessIntroSupplier.company }}
                </div>
                <div
                  class="intro-badge"
                  :class="{
                    'vip-badge':
                      businessIntroSupplier.memberType === '超级会员',
                    'normal-badge':
                      businessIntroSupplier.memberType !== '超级会员',
                  }"
                >
                 VIP
                </div>
              </div>
            </div>

            <div class="intro-text">{{ businessIntroContent }}</div>
          </div>
        </div>

        <div class="business-intro-footer">
          <button
            class="business-intro-btn cancel-btn"
            @click="closeBusinessIntroDialog"
          >
            {{ t("worldMap.businessIntro.close") }}
          </button>
          <button
            class="business-intro-btn confirm-btn"
            @click="handleContactFromIntro"
          >
            {{ t("worldMap.businessIntro.contact") }}
          </button>
        </div>

        <!-- 名片大图弹窗 -->
        <div
          v-if="showBusinessCard"
          class="business-card-modal"
          @click="closeBusinessCard"
        >
          <div class="business-card-modal-content" @click.stop>
            <div class="business-card-watermark">
              <img
                :src="businessCardImage"
                alt="名片大图"
                class="business-card-full-img"
              />
              <div class="watermark-text">www.yydt888.com</div>
             
            </div>
            <button class="business-card-close-btn" @click="closeBusinessCard">
              ×
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 跑马灯文字组件 -->
    <div v-if="marqueeText" class="marquee-container">
      <div class="marquee-content">
        {{ marqueeText }}
      </div>
    </div>

    <!-- 轮播图组件 -->
    <div
      v-if="showCarousel && carouselItems.length"
      class="div-image"
      :class="{ 'carousel-mobile-landscape': isMobileLandscape }"
      :style="{ display: !showAllRoutes ? 'block' : 'none' }"
      @mouseenter="stopCarousel"
      @mouseleave="startCarousel"
    >
      <!-- 关闭按钮 -->
      <span class="carousel-close-btn" @click.stop="closeCarousel">×</span>
      <div class="carousel-items">
        <div
          v-for="(item, index) in carouselItems"
          :key="item.id"
          class="carousel-item"
          :class="{ active: index === activeIndex }"
          @click="handleClick(item)"
        >
          <img :src="item.image" :alt="item.title" />
        </div>
      </div>

      <div class="carousel-dots">
        <span
          v-for="(item, index) in carouselItems"
          :key="item.id"
          class="dot"
          :class="{ active: index === activeIndex }"
          @click="setActive(index)"
        ></span>
      </div>
    </div>
  </div>
</template>

<style>
/* 基础样式保持不变 */
.listcard {
  width: calc(100% - 16px);
  background-color: #ffffff;
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.listcard:hover,
.listcard.active {
  background-color: #ecf3ff;
  border: 1px solid #548efe;
}

.headercard {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.route-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  overflow: visible;
  white-space: normal;
  word-break: break-word;
  line-height: 1.3;
}

.tag {
  background-color: #548efe;
  color: white;
  padding: 1px 6px;
  border-radius: 2px;
  font-size: 10px;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  gap: 4px;
}

.aligned-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  margin-top: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 11px;
  line-height: 1.2;
  width: auto;
}

.info-label {
  color: #666;
  margin-right: 2px;
  white-space: nowrap;
}

.info-value {
  color: #333;
  font-weight: 500;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80px;
}

.route-options-list {
  padding: 6px;
  width: calc(100% - 12px);
}

.info-item[style*="font-weight: 900"] {
  color: #548efe;
}

.info-item[style*="font-weight: 900"] .info-value {
  color: #548efe;
}

/* 自定义缩放控件样式 */
.leaflet-control-zoom {
  display: block !important;
  margin-top: 20px !important;
  margin-right: 30px !important;
  border: none !important;
}

.leaflet-control-zoom a {
  width: 30px !important;
  height: 30px !important;
  line-height: 30px !important;
  color: #666 !important;
  background-color: white !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.leaflet-control-zoom-in {
  border-radius: 4px !important;
  margin-bottom: 4px !important;
}

.leaflet-control-zoom-out {
  border-radius: 4px !important;
}

.leaflet-control-layers {
  display: none !important;
}

.map-tools {
  position: fixed;
  top: 80px;
  right: 30px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.hot-station-icon.lang-en .marker-label {
  width: 110px;
}

.hot-station-icon.lang-en .label-content {
  width: 130px;
}

.hot-station-icon.lang-zh-CN .marker-label {
  width: 65px;
  }

.hot-station-icon.lang-zh-CN .label-content {
  width: 110px;
}

.station-suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: none;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  z-index: 1010; /* Increased z-index to ensure it's above other elements */
  max-height: 300px; /* Increased height to show more results */
  overflow-y: auto;
  margin-top: 4px;
  padding: 6px 0;
  animation: fadeIn 0.2s ease;
  width: 100%; /* Ensure full width */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.station-suggestion-item {
  padding: 10px 14px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  position: relative;
  border-left: 3px solid transparent;
}

.station-suggestion-item:hover {
  background: #f0f5ff;
  color: #4070f4;
  border-left: 3px solid #4070f4;
}

.station-suggestion-item span {
  opacity: 0.7;
  font-size: 12px;
  margin-left: 4px;
}

.suggestions-header {
  padding: 8px 14px;
  font-size: 12px;
  color: #666;
  background: #f8fafc;
  font-weight: 600;
  border-bottom: 1px solid #f1f5f9;
  margin-bottom: 4px;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.suggestion-name {
  font-weight: 500;
}

.suggestion-en-name {
  font-size: 11px;
  color: #64748b;
  margin-top: 2px;
}

.suggestion-icon {
  color: #cbd5e1;
  opacity: 0;
  transition: all 0.2s;
}

.station-suggestion-item:hover .suggestion-icon {
  opacity: 1;
  color: #4070f4;
}

/* 滚动条样式 */
.station-suggestions-list::-webkit-scrollbar {
  width: 6px;
}

.station-suggestions-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.station-suggestions-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.station-suggestions-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
}

.logo-image {
  height: 38px;
  width: auto;
  object-fit: contain;
}



.logo-placeholder,
.logo-image-yyimg {
  display: none;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.search-icon {
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding: 0 12px;
}

.search-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 36px;
  background: #548efe;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0 16px;
}

.search-button:hover {
  background: #4071d6;
}

.clear-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: none;
  border-radius: 4px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-button:hover {
  background: #e8e8e8;
  color: #ff4d4f;
}

.station-card-marker {
  background: transparent;
}

.avater-cart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: scale(0.8);
  transform-origin: center bottom;
  pointer-events: auto;
}

.card-header {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  border-bottom: 2px solid;
}

.start-station-card .card-header {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border-color: #4caf50;
}

.end-station-card .card-header {
  background: rgba(255, 87, 34, 0.1);
  color: #ff5722;
  border-color: #ff5722;
}

#map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.earth3d-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 8;
}

.earth3d {
  width: 100%;
  height: 100%;
}

.leaflet-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
}

.DistanceTool {
  z-index: 1000;
}

.container {
  transition: transform 0.3s ease;
}

.sidebar-hidden {
  transform: translateX(-100%);
  margin-left: -20vw;
  /* 确保完全移出视图 */
}

/* 控制按钮相关样式 - 删除重复样式，整合到这里 */
.control-buttons {
  position: absolute;
  left: 20vw;
  /* 与容器宽度一致 */
  top: 10px;
  transform: translateX(-15px);
  /* 向左偏移，确保按钮位于容器边缘 */
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: all 0.3s ease;
  z-index: 1002;
  /* 确保按钮始终在最上层 */
}

/* 隐藏状态的按钮位置 */
.buttons-shifted {
  left: 10px !important;
  transform: translateX(0);
}

/* 修复sidebar-hidden类 */
.sidebar-hidden {
  transform: translateX(
    -305px
  ) !important; /* 确保完全移出视图，固定宽度+边距 */
}

.clear-btn {
  background: #ff4d4f;
  color: white;
}

.clear-btn:hover {
  background: #ff7875;
}

/* 横屏提示样式 */
.rotate-hint {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
}

.rotate-hint-content {
  text-align: center;
  color: white;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.rotate-icon {
  animation: rotate 2s infinite ease-in-out;
  margin-bottom: 20px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.rotate-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.rotate-subtext {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

@keyframes rotate {
  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }

  25% {
    transform: rotate(90deg) scale(1.1);
  }

  50% {
    transform: rotate(90deg) scale(1);
  }

  75% {
    transform: rotate(90deg) scale(0.9);
  }
}

/* 移动端横屏等比例缩放样式 */
.mobile-landscape {
  transform-origin: top left;
}

/* 移动端横屏时所有组件等比例缩放 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .mobile-landscape {
    transform: scale(0.7);
    /* 统一使用scale进行缩放 */
    height: 100vh;
    min-width: 180px;
    overflow: hidden;
  }

  /* 调整控制按钮位置 */
  .controls-mobile-landscape {
    left: calc(20vw * 0.7);
    /* 考虑容器缩放后的宽度 */
    top: 10px;
  }

  .controls-mobile-landscape.buttons-shifted {
    left: 10px !important;
  }

  /* 导航栏移动端横屏适配 */
  .nav-mobile-landscape {
    transform: scale(0.8);
    transform-origin: top left;
    height: 50px;
  }

  /* 地图容器移动端横屏适配 */
  .leaflet-mobile-landscape {
    transform: scale(0.85);
    transform-origin: top left;
    width: 117.65%;
    height: 117.65%;
  }

  .earth3d-mobile-landscape {
    transform: scale(0.85);
    transform-origin: top left;
    width: 117.65%;
    height: 117.65%;
  }

  /* 工具栏移动端横屏适配 */
  .tools-mobile-landscape {
    transform: scale(0.8);
    transform-origin: top right;
    top: 50px;
    right: 15px;
  }

  /* 路线信息容器移动端横屏适配 */
  .container-mobile-landscape {
    transform: scale(0.8);
    transform-origin: top left;
    width: 125%;
    height: 125%;
    max-height: 125%;
  }

  /* 固定头部移动端横屏压缩 */
  .header-mobile-landscape {
    transform: scale(0.9);
    transform-origin: top left;
    padding: 4px 0;
  }

  .logo-mobile-landscape {
    transform: scale(0.85);
    transform-origin: left center;
    padding: 4px 8px;
    gap: 8px;
  }

  /* 搜索表单移动端横屏优化 */
  .form-mobile-landscape {
    transform: scale(0.9);
    transform-origin: top left;
    padding: 0 8px;
  }

  .group-mobile-landscape {
    margin-bottom: 6px;
  }

  .input-mobile-landscape {
    transform: scale(0.9);
    height: 28px;
    font-size: 12px;
    padding: 0 6px;
  }

  /* 按钮组移动端横屏优化 */
  .buttons-mobile-landscape {
    transform: scale(0.85);
    transform-origin: left center;
    margin-top: 6px;
    padding: 0 8px;
    gap: 4px;
  }

  .button-mobile-landscape {
    height: 28px;
    font-size: 11px;
    padding: 0 10px;
  }

  /* 标签页移动端横屏优化 */
  .tabs-mobile-landscape {
    transform: scale(0.85);
    transform-origin: left center;
    padding: 3px 6px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    flex-wrap: nowrap;
  }

  .tabs-mobile-landscape::-webkit-scrollbar {
    display: none;
  }

  .tab-mobile-landscape {
    padding: 5px 8px;
    font-size: 10px;
    white-space: nowrap;
    flex: 0 0 auto;
    min-width: fit-content;
  }

  /* 可滚动内容移动端横屏优化 */
  .content-mobile-landscape {
    transform: scale(0.9);
    transform-origin: top left;
    max-height: calc(100vh - 180px);
    overflow: hidden;
  }

  /* 路线选项移动端横屏优化 */
  .title-mobile-landscape {
    transform: scale(0.85);
    transform-origin: left center;
    font-size: 11px;
    padding: 5px 6px;
  }

  .list-mobile-landscape {
    transform: scale(0.9);
    transform-origin: top left;
    padding: 3px 4px;
  }

  /* 路线卡片移动端横屏优化 */
  .card-mobile-landscape {
    transform: scale(0.85);
    transform-origin: top left;
    width: 240px;
    padding: 5px;
    margin-bottom: 3px;
  }

  /* 结果列表移动端横屏优化 */
  .results-mobile-landscape {
    transform: scale(0.9);
    transform-origin: top left;
    padding: 3px 6px;
  }

  .results-mobile-landscape .results-list-scroll {
    max-height: 180px;
  }

  .item-mobile-landscape {
    transform: scale(0.9);
    padding: 6px;
    font-size: 11px;
  }

  /* 控制按钮移动端横屏适配 */
  .controls-mobile-landscape {
    transform: scale(0.8);
    transform-origin: left center;
    left: calc(20vw - 12px);
    /* 调整为右上角位置 */
    top: 8px;
    /* 固定在顶部 */
  }

  .controls-mobile-landscape.buttons-shifted {
    left: 10px !important;
    top: 8px;
    /* 保持在顶部 */
  }

  .btn-mobile-landscape {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  /* 右侧菜单移动端横屏适配 */
  .menu-mobile-landscape {
    transform: scale(0.75);
    transform-origin: top right;
    right: 8px;
  }

  /* 站点信息卡片移动端横屏适配 */
  .cart-mobile-landscape {
    transform: scale(0.7) !important;
    max-width: 180px;
  }

  /* 图片容器移动端横屏适配 */
  .image-mobile-landscape {
    transform: scale(0.7);
    transform-origin: bottom right;
    bottom: 8px;
    right: 8px;
  }

  /* 建议列表移动端横屏适配 */
  .suggestions-mobile-landscape {
    transform: scale(0.9);
    max-height: 250px; /* Increased from 120px to show more results */
    font-size: 11px;
    z-index: 1020; /* Ensure it's above other elements */
  }

  /* Leaflet控件移动端横屏适配 */
  .leaflet-control-zoom {
    transform: scale(0.8) !important;
    margin-top: 5px !important;
    margin-right: 5px !important;
  }

  .leaflet-control-scale {
    transform: scale(0.8) !important;
    font-size: 9px !important;
  }

  .leaflet-control-mouseposition {
    transform: scale(0.8) !important;
    font-size: 9px !important;
  }

  /* 热门站点标记移动端横屏适配 */
  .hot-station-icon {
    transform: scale(0.7) !important;
  }

  .hot-station-icon .label-content {
    font-size: 9px;
  }

  .hot-station-icon .primary-text {
    font-size: 8px;
  }

  .hot-station-icon .secondary-text {
    font-size: 7px;
  }

  /* 滚动条样式已禁用 - 不再需要滚动功能 */

  /* 确保容器不会超出屏幕 */
  .container-mobile-landscape {
    max-width: 280px;
    overflow: hidden;
  }

  /* 优化文字显示 */
  .container-mobile-landscape * {
    font-size: inherit;
    line-height: 1.1;
  }

  /* 确保按钮可点击 */
  .button-mobile-landscape,
  .btn-mobile-landscape {
    min-height: 28px;
    touch-action: manipulation;
  }

  /* 路线选项标题样式调整 */
  .route-options-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 11px;
    padding: 4px 6px;
    font-weight: 600;
  }

  .route-count {
    font-size: 9px;
    color: #666;
    margin-left: 8px;
  }

  .toggle-routes {
    font-size: 9px;
    color: #548efe;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .dropdown-arrow {
    transition: transform 0.2s ease;
    width: 12px;
    height: 12px;
  }

  .dropdown-arrow.is-expanded {
    transform: rotate(180deg);
  }

  /* 标签页样式优化 */
  .tabs {
    display: flex;
    padding: 4px 8px;
    gap: 2px;
    border-bottom: 1px solid #eee;
    flex-wrap: nowrap;
    width: 100%;
    box-sizing: border-box;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .tabs::-webkit-scrollbar {
    display: none;
  }

  .tab {
    padding: 6px 10px;
    font-size: 11px;
    color: #666;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
    white-space: nowrap;
    text-align: center;
    flex: 0 0 auto; /* Change from flex: 1 to prevent stretching */
    min-width: fit-content; /* Ensure tab is at least as wide as its content */
  }

  /* 修复移动端横屏模式 */
  @media screen and (max-width: 768px) and (orientation: landscape) {
    /* 调整容器样式 */
    .container {
      transform: none; /* 移除缩放 */
      width: 300px; /* 恢复原始宽度 */
      height: 100vh;
      max-height: 85vh;
      overflow: hidden; /* 外围容器不显示滚动条 */
    }

    .container-mobile-landscape {
      transform: none; /* 移除缩放 */
      width: 300px;
      height: 100vh;
      max-height: 85vh;
      overflow: hidden; /* 外围容器不显示滚动条 */
    }

    /* 调整控制按钮位置 */
    .control-buttons {
      left: 300px; /* 与容器宽度一致 */
      top: 10px;
    }

    .controls-mobile-landscape {
      left: 300px;
      top: 10px;
      transform: translateX(-15px);
    }

    .controls-mobile-landscape.buttons-shifted {
      left: 10px !important;
      transform: translateX(0);
    }

    /* 优化横屏模式下的内部元素 */
    .search-form {
      padding: 5px 10px;
    }

    .form-group {
      margin-bottom: 5px;
    }

    .button-group {
      margin-top: 5px;
      gap: 5px;
    }

    .search-button,
    .clear-button {
      height: 30px;
      padding: 0 10px;
    }

    .tabs {
      padding: 3px 5px;
      gap: 2px;
    }

    .tab {
      padding: 4px 6px;
      font-size: 11px;
    }

    .scrollable-content {
      max-height: calc(100vh - 180px);
    }
  }

  /* 隐藏状态的按钮位置 */
  .buttons-shifted {
    left: 10px !important;
    /* 使用!important确保优先级 */
    transform: translateX(0);
    /* 重置偏移 */
  }

  /* 修复sidebar-hidden类 */
  .sidebar-hidden {
    transform: translateX(-105%) !important;
    /* 确保完全移出视图 */
  }

  /* 修复路线选项和列表卡片样式 */
  .route-options-list,
  .listcard {
    width: 90%;
    box-sizing: border-box;
  }

  /* 滚动条样式已禁用 - 不再需要滚动功能 */
}

/* 搜索结果下拉框特别处理 */
@media screen and (max-width: 768px) {
  .station-suggestions-list {
    position: fixed !important; /* 使用固定定位 */
    z-index: 9999 !important; /* 设置最高层级 */
    max-height: 40vh !important; /* 减小高度，避免遮挡 */
    width: 92% !important; /* 与父容器宽度接近但略小 */
    top: auto !important; /* 不要使用top定位 */
    left: 4% !important; /* 左侧留出空间 */
    border: 1px solid #e2e8f0;
    transform: none !important; /* 防止任何可能的变形 */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important; /* 增强阴影 */
  }

  /* 确保表单组和选择容器不会限制下拉框 */
  .form-group,
  .select-container {
    overflow: visible !important;
    position: relative;
  }

  /* 确保容器不会限制下拉框的显示 - 仅在有下拉框显示时应用 */
  .container.show-dropdown,
  .container-mobile-landscape.show-dropdown {
    overflow: visible !important;
  }

  /* 调整滚动内容区域，避免影响下拉框 */
  .scrollable-content {
    overflow: hidden !important;
  }

  /* 调整搜索结果样式 */
  .suggestion-content {
    padding: 0 !important;
  }

  .suggestion-name {
    font-size: 13px !important;
  }

  .suggestion-en-name {
    font-size: 11px !important;
  }

  /* 确保背景和阴影 */
  .station-suggestions-list {
    background: white !important;
  }
  
  /* 确保在小屏幕上标签不换行 */
  .tabs {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding: 3px 6px;
  }
  
  .tab {
    flex: 0 0 auto;
    min-width: fit-content;
    padding: 4px 8px;
    font-size: 10px;
  }
}

/* 跑马灯容器样式 */
.marquee-container {
  position: fixed;
  top: 7vh;
  left: 50%;
  transform: translateX(-50%);
  width: 350px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  z-index: 50;
  background: transparent;
  /*鼠标悬停为白色*/
  cursor: pointer;
  /* 防止文本选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 添加左右渐变遮罩 */
.marquee-container::before,
.marquee-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50px;
  z-index: 2;
  pointer-events: none;
}

.marquee-container::before {
  left: 0;
  background: linear-gradient(to right, transparent, transparent);
}

.marquee-container::after {
  right: 0;
  background: linear-gradient(to left, transparent, transparent);
}

/* 移动端内部元素优化 - 竖屏 */
@media screen and (max-width: 767px) and (orientation: portrait) {
  /* 优化内部元素显示 */
  .logo-container {
    padding: 6px 8px;
  }

  .logo-image {
    height: 28px;
  }



  .search-form {
    padding: 0 10px;
  }

  .search-input {
    height: 36px;
    font-size: 14px;
    padding: 0 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.3s;
    width: 100%;
    box-sizing: border-box;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .search-input:focus {
    border-color: #4070f4;
    box-shadow: 0 0 0 3px rgba(64, 112, 244, 0.15);
    outline: none;
  }

  .select-container {
    position: relative;
    width: 100%;
    overflow: visible !important; /* Make sure dropdowns aren't cut off */
  }

  .button-group {
    margin-top: 10px;
  }

  .search-button,
  .clear-button {
    height: 32px;
  }

  .tab {
    padding: 5px 8px;
    font-size: 10px;
  }

  .result-item {
    padding: 10px 12px;
    font-size: 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    cursor: pointer;
  }

  .result-item:hover {
    background-color: #f5f7fa;
  }

  .result-item:last-child {
    border-bottom: none;
  }

  .result-description {
    color: #666;
    font-size: 11px;
    margin-left: 4px;
  }

  .history-time {
    color: #999;
    font-size: 10px;
    margin-left: 4px;
  }
}

/* 超小屏幕设备横屏优化 */
@media screen and (min-width: 320px) and (max-width: 479px) and (orientation: landscape) {
  .container {
    transform: none;
    width: 250px; /* 更窄一点，适应非常小的横屏 */
    height: 100vh;
    max-height: 95vh;
    overflow: hidden; /* 外围容器不显示滚动条 */
  }

  .control-buttons {
    left: 250px;
    top: 15px;
  }

  /* 优化内部元素 */
  .header {
    padding: 3px 0;
  }

  .logo-container {
    padding: 3px 5px;
    gap: 6px;
  }

  .logo-image {
    height: 22px;
  }



  .search-form {
    padding: 2px 5px;
  }

  .form-group {
    margin-bottom: 3px;
    overflow: visible !important; /* Make sure dropdowns aren't cut off */
  }

  .label {
    font-size: 11px;
  }

  .search-input {
    height: 26px;
    font-size: 11px;
    padding: 0 6px;
  }

  .button-group {
    margin-top: 2px;
    gap: 3px;
    padding: 0 5px;
  }

  .search-button,
  .clear-button {
    height: 26px;
    font-size: 11px;
    padding: 0 8px;
  }

  .tabs {
    padding: 2px;
    gap: 1px;
  }

  .tab {
    padding: 3px 2px;
    font-size: 9px;
  }

  .scrollable-content {
    max-height: calc(100vh - 130px);
  }

  .results-list,
  .route-options-list {
    padding: 3px;
  }

  .result-item {
    padding: 5px;
    font-size: 10px;
    margin-bottom: 2px;
  }

  .listcard {
    padding: 4px;
    margin-bottom: 3px;
  }

  .route-name,
  .tag {
    font-size: 9px;
  }

  .info-item {
    font-size: 9px;
  }
}

.marquee-content {
  position: absolute;
  top: 0;
  left: 100%;
  white-space: nowrap;
  padding: 0 20px;
  font-size: 14px;
  line-height: 40px;
  color: #272626;
  font-weight: bold;
  animation: marquee 12s linear infinite;
  /* 防止文本选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 跑马灯动画 */
@keyframes marquee {
  0% {
    left: 100%;
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    left: -100%;
    opacity: 0;
  }
}

/* 轮播图样式增强 */
.carousel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  display: none;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.carousel-item.active {
  opacity: 1;
  display: block;
  z-index: 2;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.carousel-item:hover img {
  transform: scale(1.03);
}

.carousel-dots {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 8px;
  z-index: 4;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: white;
  transform: scale(1.2);
}

.div-image {
  position: fixed;
  bottom: 4px;
  right: 20px;
  width: 305px;
  height: 130px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

/* Mobile landscape mode */
.carousel-mobile-landscape {
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 120px;
}

/* Mobile portrait mode */
@media screen and (max-width: 768px) {
  .div-image {
    width: 350px;
    height: 120px;
    bottom: 65px;
    right: 10px;
  }
}

@media screen and (max-width: 480px) {
  .div-image {
    width: 300px;
    height: 100px;
    bottom: 55px;
    right: 10px;
  }
}

/* 关闭按钮样式 */
.carousel-close-btn {
  position: absolute;
  top: 6px;
  right: 10px;
  z-index: 10;
  font-size: 20px;
  color: #fff;
  background: rgba(0, 0, 0, 0.35);
  border-radius: 50%;
  width: 26px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  cursor: pointer;
  transition: background 0.2s;
  user-select: none;
}
.carousel-close-btn:hover {
  background: rgba(0, 0, 0, 0.6);
  color: #ff4d4f;
}

/* 会员信息卡片样式 */
.member-info-popup {
  position: fixed;
  z-index: 1000;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));
  pointer-events: auto;
}

.member-info-popup .member-info-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  border: none;
  width: 280px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.member-info-popup .member-header {
  background-color: #ffffff;
  padding: 12px 16px 8px;
  border-bottom: none;
  position: relative;
  display: flex;
  align-items: flex-start;
}

.member-info-popup .avatar-container {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.member-info-popup .member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #f0f0f0;
}

.member-info-popup .member-info {
  flex: 1;
  min-width: 0;
}

.member-info-popup .member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.member-info-popup .member-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-info-popup .member-title {
  font-weight: normal;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.member-info-popup .member-badge-container {
  position: absolute;
  bottom: -2px;
  right: -2px;
}

.member-info-popup .private-msg-btn {
  position: absolute;
  top: 12px;
  right: 16px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.member-info-popup .private-msg-btn:hover {
  background-color: #000;
}

.member-info-popup .member-badge {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
  font-weight: 500;
  display: inline-block;
}

.member-info-popup .vip-badge {
  background-color: #1a73e8;
}

.member-info-popup .normal-badge {
  background-color: #3498db;
}

.member-info-popup .company-name {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.member-info-popup .member-contact-info {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.member-info-popup .contact-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
  flex-wrap: wrap;
}

.member-info-popup .contact-info-row:last-child {
  margin-bottom: 0;
}

.member-info-popup .contact-icon {
  color: #666;
  width: 20px;
  flex-shrink: 0;
  font-size: 12px;
  margin-right: 4px;
}

.member-info-popup .phone-icon {
  color: #333;
}

.member-info-popup .id-icon {
  margin-left: 10px;
}

.member-info-popup .contact-value {
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  margin-right: 6px;
}

.member-info-popup .contact-btn {
  background: none;
  border: 1px solid #3498db;
  color: #3498db;
  font-size: 12px;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.member-info-popup .qq-btn {
  color: #3498db;
}

.member-info-popup .contact-btn:disabled {
  color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.member-info-popup .contact-btn:not(:disabled):hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.member-info-popup .member-actions {
  display: flex;
  padding: 4px 16px 12px;
  border-top: none;
  justify-content: center;
}

.member-info-popup .action-btn {
  padding: 6px 16px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  width: 100%;
  text-align: center;
}

.member-info-popup .business-intro-btn {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
}

.member-info-popup .business-intro-btn:hover {
  background-color: #e8e8e8;
}

.member-info-popup .member-info-arrow {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #f9f9f9;
}

/* 业务简介弹窗样式 */
.business-intro-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.2s ease;
}

.business-intro-dialog {
  width: 90%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative; /* Ensure position context for absolute elements */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.business-intro-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.business-intro-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.business-intro-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.business-intro-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.business-intro-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  max-height: 40vh; /* Limit the height and make scrollable */
}

.business-intro-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #eee;
}

.business-intro-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.confirm-btn:hover {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.intro-content {
  padding: 0;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.intro-avatar {
  margin-right: 15px;
  position: relative;
}

.intro-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.intro-user-info {
  flex: 1;
  position: relative;
}

.intro-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.intro-company {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
}

.intro-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.vip-badge {
  background-color: #f39c12;
}

.normal-badge {
  background-color: #95a5a6;
}

.intro-text {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap; /* Changed from pre-line to pre-wrap for better word wrapping */
  word-wrap: break-word; /* Ensures long words are broken */
  overflow-wrap: break-word; /* Modern version of word-wrap */
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  min-height: 120px;
  max-width: 100%; /* Prevent horizontal overflow */
  overflow-x: hidden; /* Hide horizontal overflow */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 名片区域样式 */
/* 名片和个人信息布局 */
.intro-header-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.intro-user-details {
  flex: 1;
  padding-right: 15px;
}

.intro-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.intro-position {
  font-size: 16px;
  color: #555;
  margin-bottom: 6px;
}

.intro-company {
  font-size: 16px;
  color: #555;
  margin-bottom: 10px;
}

/* 名片区域 */
.business-card-section {
  width: 40%;
  max-width: 280px;
}

.card-title-box {
  text-align: center;
  margin-bottom: 6px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.business-card-thumbnail {
  position: absolute;
  left: 320px;
  top: -10px;
  width: 120px;
  height: 80px;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1000;
  
  /* Responsive adjustments */
  @media screen and (max-width: 480px) {
    width: 80px;
    height: 60px;
    right: 10px;
    top: -5px;
  }
}

.business-card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: white;
  border-radius: 0;
}

.business-card-thumbnail .card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 3px 0;
  text-align: center;
}

.card-hint {
  font-size: 16px;
  color: #333;
  padding: 0 10px;
  line-height: 1.4;
}

/* 名片大图弹窗样式 */
.business-card-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease;
}

.business-card-modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  animation: scaleIn 0.3s ease;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
}

.business-card-watermark {
  position: relative;
  display: block;
  text-align: center;
}

.business-card-full-img {
  max-width: 80%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  background-color: white;
  padding: 10px;
  margin: 0 auto;
  display: block;
}

.watermark-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-30deg);
  font-size: 40px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.3);
  white-space: nowrap;
  pointer-events: none;
  user-select: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.business-card-close-btn {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  border: none;
  font-size: 20px;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
}

.business-card-close-btn:hover {
  background-color: #f5f5f5;
  transform: scale(1.1);
}

/* 修改左侧容器宽度为屏幕宽度的20% */
.container {
  width: 20vw;
  min-width: 240px;
  /* 设置最小宽度以确保内容可用性 */
  transition: transform 0.3s ease;
}

/* 修改列表卡片宽度为容器的90% */
.listcard {
  width: 100%;
  max-width: 93%;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

/* 修改路线选项列表宽度为容器的90% */
.route-options-list {
  padding: 6px;
}

/* 调整内部内容布局以适应新宽度 */
.info-grid,
.headercard,
.route-name,
.info-row {
  width: 100%;
  flex-wrap: wrap;
}

.info-value {
  max-width: none;
  width: auto;
}

.route-name {
  max-width: 60%;
}

/* 移动端适配调整 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .container-mobile-landscape {
    max-width: none;
    width: 20vw;
    min-width: 200px;
    /* 移动端略小的最小宽度 */
    overflow: hidden;
  }

  /* 调整控制按钮位置 */
  .controls-mobile-landscape {
    transform: scale(0.8);
    transform-origin: left center;
    left: 20vw;
    /* 与容器宽度一致 */
    top: 12%;
  }
}

/* 确保在非移动端设备上正常显示 */
@media screen and (min-width: 769px) {
  .container {
    width: 300px;
  }
}

/* 移动端适配调整 */
@media screen and (max-width: 768px) {
  .tab {
    font-size: 9px;
    /* 在小屏幕上使用更小的固定字体 */
    padding: 4px 2px;
  }

  /* 确保标签容器在小屏幕上正确显示 */
  .tabs {
    padding: 2px 4px;
    gap: 1px;
  }
}

/* 修改左侧容器为固定宽度 */
.container {
  width: 300px; /* 固定宽度，不使用响应式 */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  bottom: -10px;
  box-sizing: border-box;
  overflow: hidden;
  /* 移除所有缩放相关样式 */
}

/* iPhone XR 特殊处理 (375 x 812) */
@media screen and (max-width: 414px) and (max-height: 896px) and (orientation: portrait) {
  .container {
    transform: scale(0.65);
    transform-origin: top left;
    width: 462px; /* 300/0.65 */
    height: 154vh; /* 补偿缩放效果 */
    bottom: -10px;
  }

  .control-buttons {
    left: 462px; /* 与容器宽度一致 */
  }

  /* 优化内部元素 */
  .search-form {
    padding: 0 8px;
  }

  .form-group {
    margin-bottom: 8px;
  }

  .tabs {
    padding: 2px 4px;
  }
}

/* iPhone XR 横屏特殊处理 */
@media screen and (max-width: 896px) and (max-height: 414px) and (orientation: landscape) {
  .container {
    transform: none;
    width: 300px;
    height: 100vh;
    max-height: 85vh;
    overflow: hidden; /* 外围容器不显示滚动条 */
    bottom: 0;
  }

  .control-buttons {
    left: 300px;
  }

  /* 优化内部元素 */
  .search-form {
    padding: 4px 8px;
  }

  .form-group {
    margin-bottom: 4px;
  }

  .button-group {
    margin-top: 4px;
  }

  .tabs {
    padding: 2px;
  }

  .tab {
    padding: 4px;
    font-size: 10px;
  }

  .scrollable-content {
    max-height: calc(100vh - 150px);
  }
}

/* 固定控制按钮位置 */
.control-buttons {
  position: absolute;
  left: 290px; /* 与容器宽度一致 */
  top: 15px;
  transform: translateX(-15px); /* 向左偏移，确保按钮位于容器边缘 */
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: all 0.3s ease;
  z-index: 1002; /* 确保按钮始终在最上层 */
}

/* 移动端适配 - 控制按钮位置 */
@media screen and (max-width: 767px) {
  .control-buttons {
    left: 400px; /* 与移动端容器宽度一致 */
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .control-buttons {
    left: 353px; /* 与平板容器宽度一致 */
  }
}

/* 禁用内容区域滚动 */
.scrollable-content {
  flex: 1;
  overflow: hidden;
  padding-right: 2px;
}

/* 标签活跃状态和悬停状态 */
.tab.active {
  background: #548efe;
  color: white;
}

.tab:hover:not(.active) {
  background: #f5f5f5;
}

/* 在移动设备上隐藏缩放控件 */
@media screen and (max-width: 768px) {
  .leaflet-control-zoom {
    display: none !important;
  }

  .map-tools {
    display: none !important;
  }

  /* 调整右侧内容的位置 */
  .right-menu {
    right: 10px;
    transform: scale(0.85);
    transform-origin: top right;
  }
}

/* 针对平板和小屏幕设备也隐藏缩放控件 */
@media screen and (max-width: 1024px) and (orientation: portrait) {
  .leaflet-control-zoom {
    display: none !important;
  }

  .map-tools {
    display: none !important;
  }

  /* 调整右侧内容的位置 */
  .right-menu {
    right: 15px;
    transform: scale(0.9);
    transform-origin: top right;
  }
}

/* 在横屏模式下的移动设备上也隐藏缩放控件 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
  .leaflet-control-zoom {
    display: none !important;
  }

  .map-tools {
    display: none !important;
  }

  /* 调整右侧内容的位置 */
  .right-menu {
    right: 15px;
    transform: scale(0.85);
    transform-origin: top right;
  }

  /* 确保导航栏不会与其他元素重叠 */
  .nav-container {
    top: 15px;
    right: 15px;
  }
}

/* 超小屏幕设备特殊处理 */
@media screen and (max-width: 480px) {
  .nav-container {
    top: 10px;
    right: 10px;
  }

  .right-menu {
    right: 5px;
    transform: scale(0.75);
  }
}

/* 添加Nav容器样式 */
.nav-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 99;
  width: 30vw;
  /* 设置为视口宽度的30% */
  display: flex;
  justify-content: flex-end;
}

/* 确保Nav组件内的选择器容器占满宽度 */
:deep(.selector-container) {
  width: 100%;
  max-width: 100%;
  justify-content: space-between;
}

/* 添加结果列表滚动样式 */
.results-list {
  position: relative;
  width: 100%;
  height: 100%;
}

.results-list-scroll {
  overflow: hidden;
  /* 禁用滚动功能 */
}

/* 移动设备上的滚动优化 */
@media screen and (max-width: 768px) {
  .results-list-scroll {
    max-height: 250px;
  }
}

/* 响应式调整 - 只缩放Nav组件而不改变其他元素 */
@media screen and (max-width: 1480px) {
  .nav-container {
    transform: scale(0.95);
    transform-origin: top right;
  }
}

@media screen and (max-width: 1280px) {
  .nav-container {
    transform: scale(0.9);
    transform-origin: top right;
  }
}

@media screen and (max-width: 1080px) {
  .nav-container {
    transform: scale(0.85);
    transform-origin: top right;
    width: 40vw;
    /* 在较小屏幕上增加到40% */
  }
}

@media screen and (max-width: 960px) {
  .nav-container {
    transform: scale(0.8);
    transform-origin: top right;
    width: 40vw;
    /* 保持40% */
  }
}

@media screen and (max-width: 860px) {
  .nav-container {
    transform: scale(0.75);
    transform-origin: top right;
    width: 40vw;
    /* 保持40% */
  }
}

@media screen and (max-width: 768px) {
  .nav-container {
    transform: scale(0.7);
    transform-origin: top right;
    width: 40vw;
    /* 保持40% */
  }
}

/* 移动设备特殊处理 */
@media screen and (max-width: 480px) {
  .nav-container {
    width: 40vw;
    /* 在小屏幕上保持40%宽度 */
    transform: scale(0.65);
    transform-origin: top right;
  }
}

/* iPhone XR 特殊处理 (375 x 812) */
@media screen and (max-width: 414px) and (max-height: 896px) {
  .nav-container {
    width: 40vw;
    /* 在iPhone XR上保持40%宽度 */
    transform: scale(0.6);
    transform-origin: top right;
  }
}

/* 横屏模式下的导航栏样式 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
  .nav-mobile-landscape {
    width: 40vw;
    /* 在横屏模式下保持40% */
    transform: scale(0.7);
    transform-origin: top right;
  }
}

@media screen and (max-width: 1024px) and (orientation: landscape) {
  .floating-menu[data-v-f9e48044] {
    bottom: -10px;
    right: 15px;
  }
}

/* 修改iPhone等小屏幕设备下的右侧菜单位置 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .floating-menu {
    bottom: -10px !important;
    right: 15px;
  }
}

/* 添加更精确的媒体查询以适应不同设备 */
@media screen and (max-width: 480px) {
  .floating-menu {
    bottom: -10px !important;
    right: 10px;
  }
}

/* iPhone XR 特殊处理 */
@media screen and (max-width: 414px) and (max-height: 896px) {
  .floating-menu {
    bottom: -10px !important;
    right: 8px;
  }
}

/* 跑马灯动画已移至上方定义 */

/* 移动端横屏下的跑马灯优化 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .marquee-container {
    width: 250px;
    height: 30px;
    top: 10vh;
  }

  .marquee-content {
    font-size: 12px;
    line-height: 30px;
  }
}

/* 轮播图样式增强 */

/* 重置CSS变量，解决层级冲突 */
:root {
  --initial-station-zindex: 1002;
  --destination-zindex: 1001;
}

/* 确保起始站下拉菜单在目的站下拉菜单之上 */
.start-suggestions {
  z-index: var(--initial-station-zindex) !important;
}

.destination-suggestions {
  z-index: var(--destination-zindex) !important;
}

/* 确保表单组的层级关系 */
.form-group:has(input[v-model="startPoint"]) {
  position: relative;
  z-index: var(--initial-station-zindex);
}

.form-group:has(input[v-model="destinationPoint"]) {
  position: relative;
  z-index: var(--destination-zindex);
}

/* 激活状态提高层级 */
.form-group:has(input[v-model="startPoint"]:focus) {
  z-index: 1010 !important;
}

.form-group:has(input[v-model="destinationPoint"]:focus) {
  z-index: 1010 !important;
}

.business-card-thumbnail.empty-card {
  background-color: #7c4dff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-icon {
  font-size: 24px;
  color: white;
  margin-bottom: 6px;
}

.empty-card-text {
  font-size: 12px;
  color: white;
  text-align: center;
}
</style>

<style scoped>
/* 添加运输方式标题样式 */
.transport-header {
  font-size: 18px;
  font-weight: bold;
  padding: 10px;
  text-align: left;
  color: #333;
}

/* 微信绑定手机号弹窗 */
.wechat-bind-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

/* 协议弹窗样式 */
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.agreement-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.agreement-modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.agreement-modal-header button {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.agreement-modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.agreement-modal-body h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: 0;
  margin-bottom: 16px;
}

.agreement-modal-body p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
}

.agreement-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.wechat-bind-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 400px;
}

.wechat-bind-content {
  display: flex;
  flex-direction: column;
}

.wechat-bind-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.wechat-bind-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f5f5f5;
  color: #666;
}

.wechat-bind-body {
  padding: 20px;
}

.wechat-bind-desc {
  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
}

.form-group {
  margin-bottom: 14px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 9px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  height: 40px;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.verification-code-container {
  display: flex;
  gap: 10px;
}

.send-code-button {
  white-space: nowrap;
  padding: 0 12px;
  height: 40px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-code-button:hover {
  background-color: #2563eb;
}

.send-code-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.password-input-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.agreement-section {
  margin-bottom: 16px;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.agreement-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}

.agreement-checkbox label {
  font-size: 13px;
  color: #333;
}

.agreement-checkbox a {
  color: #3b82f6;
  text-decoration: none;
}

.agreement-checkbox a:hover {
  text-decoration: underline;
}

.wechat-bind-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.primary-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-button:hover {
  background-color: #2563eb;
}

.primary-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.secondary-button {
  padding: 10px 16px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.secondary-button:hover {
  background-color: #e5e7eb;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 名片大图中的对角水印 */
.watermark-diagonal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.2);
  transform: rotate(-30deg);
  pointer-events: none;
}
.member-type-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  padding: 1px 3px;
  color: white;
  font-weight: bold;
  z-index: 1;
  text-align: center;
  min-width: 16px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}
.vip-badge {
  background: linear-gradient(to right, #000000, #8e7632);
}
.normal-badge {
  background-color: #3498db;
}
</style>

