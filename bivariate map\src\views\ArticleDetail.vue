<template>
  <div class="article-detail-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <!-- 左侧Logo -->
        <div class="header-left">
          <img src="../../public/image/newmap.png" alt="New Map" class="logo-image">
        </div>

        <!-- 中间区域 - 空白 -->
        <div class="header-center"></div>

        <!-- 右侧退出按钮 -->
        <div class="header-right">
          <button class="exit-button" @click="goBack">
            <span class="exit-icon">×</span>
            <span>{{ t('article.backButton') }}</span>
          </button>
        </div>
      </div>
    </header>

    <!-- 文章内容 -->
    <div class="article-content" v-if="article">
      <div class="article-header">
        <h1 class="article-title">{{ article.title }}</h1>
        <div class="article-meta">
          <div class="author-info" @click="showUserInfo">
            <div class="author-avatar-container">
              <img :src="getAvatarUrl(article.avatar)" :alt="t('article.authorInfo')" class="author-avatar">
              <!-- 根据会员等级显示VIP徽章 -->
              <div v-if="article.memberType === '普通会员'" class="vip-badge vip-regular" style="bottom: 0; right: 0;">
                VIP
              </div>
              <div v-else-if="article.memberType === '超级会员'" class="vip-badge vip-super" style="bottom: 0; right: 0;">
                SVIP
              </div>
            </div>
            <span class="author-name">{{ article.nickName || article.username || t('article.anonymous') }}</span>
          </div>
          <div class="article-stats">
            <span class="publish-time">{{ formatTime(article.createTime) }}</span>
          </div>
        </div>
      </div>

      <div class="article-tags" v-if="article.tags && article.tags.length > 0">
        <span v-for="tag in article.tags" :key="tag.tid || tag.id" class="tag">{{ tag.name }}</span>
      </div>

      <div class="article-body">
        <p class="content-text">{{ article.content }}</p>
      </div>

      <div v-if="imagesWithRatio.length > 0" class="article-images">
        <h3 class="images-title">{{ t('article.imageAttachments') }}</h3>
        <!-- 新的固定尺寸图片容器 -->
        <div class="fixed-image-container">
          <div :class="['image-layout', `layout-${imagesWithRatio.length}`]">
            <div v-for="(image, idx) in imagesWithRatio" :key="image.id"
              class="image-slot"
              @click="previewImage(image)">
              <img
                :src="getImageUrl(image.imgUrl)"
                :alt="t('article.imageAttachments')"
                class="fixed-image"
                @load="evt => onImageLoad(evt, idx)"
              >
            </div>
          </div>
        </div>
      </div>
      
      <!-- 评论区 -->
      <PostComments :postId="article?.id || route.params.id" />
      
      <!-- 调试信息 -->
      <div class="debug-info" style="display: none;">
        <p>{{ t('article.debug.articleId') }}: {{ article?.id || t('article.debug.none') }}</p>
        <p>{{ t('article.debug.routeParamId') }}: {{ route.params.id }}</p>
      </div>
    </div>

    <!-- 加载中 -->
    <div v-else-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>{{ t('article.loading') }}</p>
    </div>

    <!-- 加载失败 -->
    <div v-else class="error-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <p>{{ t('article.notFound') }}</p>
      <button class="back-to-list" @click="router.push('/small-square')">{{ t('article.backToList') }}</button>
    </div>

    <!-- 图片预览弹窗 -->
    <div v-show="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
      <div class="preview-container" @click.stop>
        <button class="close-preview" @click="closeImagePreview">&times;</button>
        <div class="image-inner">
          <!-- 加载指示器 -->
          <div v-if="previewState.isLoading" class="image-loading">
            <div class="loading-spinner"></div>
            <p>{{ t('article.imageLoading') }}</p>
          </div>
          
          <img
            v-else-if="previewImageUrl"
            :src="previewImageUrl"
            :alt="t('article.imagePreview')"
            class="preview-image"
            :style="{
              transform: `scale(${previewState.scale}) translate(${previewState.translateX}px, ${previewState.translateY}px)`,
              cursor: previewState.isDragging ? 'grabbing' : (previewState.scale > 1 ? 'grab' : 'default')
            }"
            @error="handleImageError"
            @wheel="handleImageWheel"
            @mousedown="handleImageMouseDown"
            @mousemove="handleImageMouseMove"
            @mouseup="handleImageMouseUp"
            @mouseleave="handleImageMouseUp"
          >
          <div v-else class="image-error">
            {{ t('article.imageLoadError') }}
          </div>
        </div>
        
        <!-- 图片导航和缩放控件 -->
        <div class="preview-controls-container">
          <!-- 导航控件 -->
          <div v-if="previewState.images && previewState.images.length > 1" class="preview-navigation">
            <button
              class="nav-button prev-button"
              @click.stop="prevImage"
              :disabled="previewState.currentIndex === 0 || previewState.buttonDisabled || previewState.isLoading"
              :class="{ 'button-disabled': previewState.buttonDisabled || previewState.isLoading }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <div class="preview-counter">
              {{ previewState.currentIndex + 1 }} / {{ previewState.images.length }}
            </div>
            <button
              class="nav-button next-button"
              @click.stop="nextImage"
              :disabled="previewState.currentIndex === previewState.images.length - 1 || previewState.buttonDisabled || previewState.isLoading"
              :class="{ 'button-disabled': previewState.buttonDisabled || previewState.isLoading }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>

          <!-- 缩放控件 -->
          <div class="zoom-controls">
            <button
              class="zoom-button zoom-out"
              @click.stop="zoomOut"
              :disabled="previewState.scale <= 0.5"
              title="缩小"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
                <line x1="8" y1="11" x2="14" y2="11"></line>
              </svg>
            </button>
            <div class="zoom-display">{{ Math.round(previewState.scale * 100) }}%</div>
            <button
              class="zoom-button zoom-in"
              @click.stop="zoomIn"
              :disabled="previewState.scale >= 3"
              title="放大"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
                <line x1="11" y1="8" x2="11" y2="14"></line>
                <line x1="8" y1="11" x2="14" y2="11"></line>
              </svg>
            </button>
            <button
              class="zoom-button zoom-reset"
              @click.stop="resetZoom"
              title="重置缩放"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                <path d="M21 3v5h-5"></path>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                <path d="M3 21v-5h5"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户信息弹窗 -->
    <div v-if="showUserInfoModal" class="user-info-modal" @click="closeUserInfo">
      <div class="user-info-card" @click.stop>
        <button class="close-button" @click="closeUserInfo">&times;</button>
        <div class="user-info-content-new">
          <!-- 用户信息区域重新布局：头像在左侧，信息在右侧 -->
          <div class="user-info-layout">
            <!-- 用户头像 -->
            <div class="user-avatar-container">
              <img :src="getAvatarUrl(article.avatar)" :alt="t('article.userInfo.title')" class="user-avatar-new">
              <div v-if="article.memberType === '普通会员'" class="vip-badge vip-regular">
                {{ t('avaterCart.normalMember') }}
              </div>
              <div v-else-if="article.memberType === '超级会员'" class="vip-badge vip-super">
                {{ t('avaterCart.superMember') }}
              </div>
            </div>
            
            <!-- 用户基本信息 -->
            <div class="user-info-side">
              <!-- 名字和职位行 -->
              <div class="info-row name-row">
                <span class="user-name-new">{{ article.nickName || article.username || t('article.anonymous') }}</span>
                <span style="margin-right: 5rem;">{{ t('article.userInfo.position') }}：{{ article.post }}</span>
              </div>
              <div class="business-intro" style="margin-left: -1px;" :title="article.introduction">{{ t('article.userInfo.businessIntro') }}：{{ truncatedBusinessIntro }}</div>
            </div>
          </div>
          
          <div class="user-info-rows">
            <!-- 公司行 -->
            <div class="info-row">
              <span class="info-label">{{ t('article.userInfo.companyName') }}:&nbsp;&nbsp;  {{ article.companyName || t('article.notProvided') }}</span>
            </div>  
            
            <!-- 电话行 -->
            <div class="info-row">
              <span class="info-label">{{ t('article.userInfo.phone') }}:&nbsp;&nbsp;{{ article.phone || t('article.notProvided') }}</span>
             
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '../store/user';
import { ElMessage } from 'element-plus';
import request from '../utils/request';
import { getArticleDetail } from '../api/article';
import { getUserMemberTypeById } from '../api/pay';
import PostComments from '../components/PostComments.vue';
import axios from 'axios';
import imageCache from '../utils/imageCache';
import { useI18n } from '../composables/useI18n';

// Initialize i18n with all needed functions
const { t, currentLocale, setLocale } = useI18n();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const article = ref({});
const loading = ref(true);
const showImagePreview = ref(false);
const previewImageUrl = ref('');
const showUserInfoModal = ref(false);
const baseUrl = import.meta.env.VITE_BASE_API || '';
const imagesWithRatio = ref([]); // 新增：存储带有比例信息的图片数组

// 返回按钮逻辑
const goBack = () => {
  // 从URL参数中获取来源信息
  const fromParam = route.query.from;
  
  // 根据来源信息进行不同的跳转
  if (fromParam === 'consult') {
    // 如果是从"我发布的资讯"进入，跳转到该部分
    router.push('/userinfo?activeTab=consult');
  } else if (fromParam === 'likes') {
    // 如果是从"我的点赞"进入，跳转到该部分
    router.push('/userinfo?activeTab=likes');
  } else {
    // 检查引用页，如果是从用户中心进入但没有指定参数，默认返回资讯部分
    const referer = document.referrer;
    if (referer && referer.includes('/userinfo')) {
      router.push('/userinfo?activeTab=consult');
    } else {
      // 其他情况正常后退
      router.go(-1);
    }
  }
};

// Watch for language changes to update UI text
watch(currentLocale, () => {
  console.log('Language changed to:', currentLocale.value);
  // If needed, refresh any language-dependent content here
});

// 测试用 - 检查文章ID是否正确传递
const articleId = computed(() => {
  console.log('当前文章ID:', route.params.id);
  return route.params.id;
});

// 预览状态
const previewState = ref({
  show: false,
  images: [],
  currentIndex: 0,
  isLoading: false, // 添加加载状态标志
  buttonDisabled: false, // 添加按钮禁用状态
  scale: 1, // 缩放比例
  translateX: 0, // X轴偏移
  translateY: 0, // Y轴偏移
  isDragging: false, // 是否正在拖拽
  lastMouseX: 0, // 上次鼠标X位置
  lastMouseY: 0 // 上次鼠标Y位置
});

// 获取头像URL的函数 - 使用缓存服务
const getAvatarUrl = (avatarUrl) => {
  return imageCache.getAvatarUrl(avatarUrl, baseUrl);
};

// 获取图片URL的函数 - 使用缓存服务
const getImageUrl = (imgUrl) => {
  return imageCache.getImageUrl(imgUrl, baseUrl);
};

// 图片加载处理 - 固定容器高度，图片等比例缩放适应
const onImageLoad = (evt, index) => {
  const img = evt.target;
  const aspectRatio = img.naturalWidth / img.naturalHeight;

  console.log(`图片 ${index + 1} 加载完成，宽高比: ${aspectRatio.toFixed(2)}`);

  // 容器高度固定，图片会通过 object-fit: contain 自动等比例缩放适应
};

// 预览图片 - 优化性能
const previewImage = (image) => {
  // 确保imagesWithRatio已初始化
  if (imagesWithRatio.value.length === 0 && article.value.articleImgList) {
    initializeImagesWithRatio();
  }
  
  // 预加载当前图片
  const imgUrl = getImageUrl(image.imgUrl);
  const img = new Image();
  img.src = imgUrl;
  
  // 显示预览
  previewImageUrl.value = imgUrl;
  showImagePreview.value = true;
  previewState.value.isLoading = true;
  
  // 设置预览状态
  previewState.value = {
    show: true,
    images: imagesWithRatio.value,
    currentIndex: imagesWithRatio.value.findIndex(img => img.id === image.id),
    isLoading: true,
    buttonDisabled: false,
    scale: 1, // 重置缩放
    translateX: 0, // 重置位置
    translateY: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  };
  
  // 确保找到了正确的索引
  if (previewState.value.currentIndex === -1) {
    previewState.value.currentIndex = 0;
  }
  
  // 图片加载完成后更新状态
  img.onload = () => {
    previewState.value.isLoading = false;
  };
  
  img.onerror = () => {
    previewState.value.isLoading = false;
    console.error('图片加载失败:', imgUrl);
  };
  
  // 预加载相邻图片
  preloadAdjacentImages();
};

// 预加载相邻图片
const preloadAdjacentImages = () => {
  const { images, currentIndex } = previewState.value;
  if (!images || images.length <= 1) return;
  
  // 预加载下一张图片
  if (currentIndex < images.length - 1) {
    const nextImg = new Image();
    nextImg.src = getImageUrl(images[currentIndex + 1].imgUrl);
  }
  
  // 预加载上一张图片
  if (currentIndex > 0) {
    const prevImg = new Image();
    prevImg.src = getImageUrl(images[currentIndex - 1].imgUrl);
  }
};

// 下一张图片 - 添加延迟保护
const nextImage = () => {
  if (previewState.value.buttonDisabled || previewState.value.isLoading) return;
  
  if (!previewState.value.images || previewState.value.images.length === 0) {
    console.error('没有可用的图片数组');
    return;
  }
  
  if (previewState.value.currentIndex < previewState.value.images.length - 1) {
    // 禁用按钮1秒
    previewState.value.buttonDisabled = true;
    previewState.value.isLoading = true;
    
    // 更新索引
    previewState.value.currentIndex++;
    const currentImage = previewState.value.images[previewState.value.currentIndex];
    
    if (currentImage && currentImage.imgUrl) {
      // 预加载图片
      const img = new Image();
      img.src = getImageUrl(currentImage.imgUrl);
      
      // 图片加载完成后更新预览
      img.onload = () => {
        previewImageUrl.value = getImageUrl(currentImage.imgUrl);
        previewState.value.isLoading = false;
        
        // 预加载下一张图片
        preloadAdjacentImages();
        
        // 1秒后启用按钮
        setTimeout(() => {
          previewState.value.buttonDisabled = false;
        }, 1000);
      };
      
      img.onerror = () => {
        console.error('图片加载失败:', getImageUrl(currentImage.imgUrl));
        previewState.value.isLoading = false;
        
        // 1秒后启用按钮
        setTimeout(() => {
          previewState.value.buttonDisabled = false;
        }, 1000);
      };
    } else {
      console.error('当前图片对象无效或缺少imgUrl属性:', currentImage);
      previewState.value.isLoading = false;
      
      // 1秒后启用按钮
      setTimeout(() => {
        previewState.value.buttonDisabled = false;
      }, 1000);
    }
  }
};

// 上一张图片 - 添加延迟保护
const prevImage = () => {
  if (previewState.value.buttonDisabled || previewState.value.isLoading) return;
  
  if (!previewState.value.images || previewState.value.images.length === 0) {
    console.error('没有可用的图片数组');
    return;
  }
  
  if (previewState.value.currentIndex > 0) {
    // 禁用按钮1秒
    previewState.value.buttonDisabled = true;
    previewState.value.isLoading = true;
    
    // 更新索引
    previewState.value.currentIndex--;
    const currentImage = previewState.value.images[previewState.value.currentIndex];
    
    if (currentImage && currentImage.imgUrl) {
      // 预加载图片
      const img = new Image();
      img.src = getImageUrl(currentImage.imgUrl);
      
      // 图片加载完成后更新预览
      img.onload = () => {
        previewImageUrl.value = getImageUrl(currentImage.imgUrl);
        previewState.value.isLoading = false;
        
        // 预加载上一张图片
        preloadAdjacentImages();
        
        // 1秒后启用按钮
        setTimeout(() => {
          previewState.value.buttonDisabled = false;
        }, 1000);
      };
      
      img.onerror = () => {
        console.error('图片加载失败:', getImageUrl(currentImage.imgUrl));
        previewState.value.isLoading = false;
        
        // 1秒后启用按钮
        setTimeout(() => {
          previewState.value.buttonDisabled = false;
        }, 1000);
      };
    } else {
      console.error('当前图片对象无效或缺少imgUrl属性:', currentImage);
      previewState.value.isLoading = false;
      
      // 1秒后启用按钮
      setTimeout(() => {
        previewState.value.buttonDisabled = false;
      }, 1000);
    }
  }
};

// 关闭预览
const closeImagePreview = () => {
  showImagePreview.value = false;
  previewState.value.show = false;
  // 重置缩放状态
  resetZoom();
};

// 缩放相关函数
const zoomIn = () => {
  if (previewState.value.scale < 3) {
    previewState.value.scale = Math.min(3, previewState.value.scale + 0.25);
    constrainTranslation();
  }
};

const zoomOut = () => {
  if (previewState.value.scale > 0.5) {
    previewState.value.scale = Math.max(0.5, previewState.value.scale - 0.25);
    // 如果缩小后图片变小，重置位置
    if (previewState.value.scale <= 1) {
      previewState.value.translateX = 0;
      previewState.value.translateY = 0;
    } else {
      constrainTranslation();
    }
  }
};

const resetZoom = () => {
  previewState.value.scale = 1;
  previewState.value.translateX = 0;
  previewState.value.translateY = 0;
  previewState.value.isDragging = false;
};

// 鼠标滚轮缩放
const handleImageWheel = (event) => {
  event.preventDefault();
  const delta = event.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.5, Math.min(3, previewState.value.scale + delta));

  if (newScale !== previewState.value.scale) {
    previewState.value.scale = newScale;

    // 如果缩小到1倍以下，重置位置
    if (newScale <= 1) {
      previewState.value.translateX = 0;
      previewState.value.translateY = 0;
    } else {
      // 限制平移范围，确保图片不会完全移出视野
      constrainTranslation();
    }
  }
};

// 鼠标拖拽相关函数
const handleImageMouseDown = (event) => {
  if (previewState.value.scale > 1) {
    previewState.value.isDragging = true;
    previewState.value.lastMouseX = event.clientX;
    previewState.value.lastMouseY = event.clientY;
    event.preventDefault();
  }
};

const handleImageMouseMove = (event) => {
  if (previewState.value.isDragging && previewState.value.scale > 1) {
    const deltaX = event.clientX - previewState.value.lastMouseX;
    const deltaY = event.clientY - previewState.value.lastMouseY;

    // 计算新的位置
    const newTranslateX = previewState.value.translateX + deltaX / previewState.value.scale;
    const newTranslateY = previewState.value.translateY + deltaY / previewState.value.scale;

    // 设置新位置
    previewState.value.translateX = newTranslateX;
    previewState.value.translateY = newTranslateY;

    // 约束平移范围
    constrainTranslation();

    previewState.value.lastMouseX = event.clientX;
    previewState.value.lastMouseY = event.clientY;
  }
};

const handleImageMouseUp = () => {
  previewState.value.isDragging = false;
};

// 约束平移范围的函数 - 更精确的边界计算
const constrainTranslation = () => {
  const scale = previewState.value.scale;
  if (scale <= 1) {
    previewState.value.translateX = 0;
    previewState.value.translateY = 0;
    return;
  }

  // 获取图片和容器的实际尺寸
  const imageElement = document.querySelector('.preview-image');
  const containerElement = document.querySelector('.image-inner');

  if (!imageElement || !containerElement) return;

  // 获取图片的原始尺寸（未缩放）
  const imageRect = imageElement.getBoundingClientRect();
  const containerRect = containerElement.getBoundingClientRect();

  // 计算图片在当前缩放下的实际尺寸
  const scaledImageWidth = imageRect.width;
  const scaledImageHeight = imageRect.height;

  // 如果图片小于容器，不允许移动
  if (scaledImageWidth <= containerRect.width && scaledImageHeight <= containerRect.height) {
    previewState.value.translateX = 0;
    previewState.value.translateY = 0;
    return;
  }

  // 计算最大允许的平移距离
  // 确保图片的边缘不会超出容器边界
  const maxTranslateX = Math.max(0, (scaledImageWidth - containerRect.width) / (2 * scale));
  const maxTranslateY = Math.max(0, (scaledImageHeight - containerRect.height) / (2 * scale));

  // 限制平移范围
  previewState.value.translateX = Math.max(
    -maxTranslateX,
    Math.min(maxTranslateX, previewState.value.translateX)
  );

  previewState.value.translateY = Math.max(
    -maxTranslateY,
    Math.min(maxTranslateY, previewState.value.translateY)
  );
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 获取标签名称
const getTagName = (tagId) => {
  // 如果传入的是整个标签对象而不是ID
  if (typeof tagId === 'object' && tagId.name) {
    return tagId.name;
  }
  
  // 标签ID映射表
  const tagMap = {
    1: '铁路',
    2: '海运',
    3: '空运',
    4: '物流',
    5: '中欧班列',
    6: '中亚班列',
    7: '中俄班列',
    8: '中蒙班列',
    9: '中越班列',
    10: '中老班列'
  };
  return tagMap[tagId] || `标签${tagId}`;
};

// 获取会员类型名称
const getMemberTypeName = (type) => {
  const memberTypes = {
    1: '普通会员',
    2: '高级会员',
    3: 'VIP会员',
    4: '钻石会员',
    5: '至尊会员'
  };
  return memberTypes[type] || `${type}`;
};

// 显示用户信息
const showUserInfo = () => {
  showUserInfoModal.value = true;
};

// 关闭用户信息
const closeUserInfo = () => {
  showUserInfoModal.value = false;
};

// 初始化处理图片数组 - 简化版本，适用于固定容器布局
const initializeImagesWithRatio = () => {
  if (article.value && article.value.articleImgList) {
    imagesWithRatio.value = article.value.articleImgList.map(img => {
      // 确保每个图片对象都有必要的属性
      if (!img.id) img.id = `img-${Math.random().toString(36).substring(2, 11)}`;

      return {
        ...img,
        // 确保imgUrl属性存在
        imgUrl: img.imgUrl || img.url || '',
      };
    });

    console.log('初始化图片数组完成:', imagesWithRatio.value);
  } else {
    console.warn('没有可用的图片列表');
    imagesWithRatio.value = [];
  }
};

// 获取文章详情
const fetchArticleDetail = async () => {
  const articleId = route.params.id;
  
  // 检查是否已经有文章数据（通过路由传递）
  if (route.params.articleData) {
    article.value = route.params.articleData;
    loading.value = false;
    console.log('使用路由传递的文章数据:', article.value);
    initializeImagesWithRatio(); // 初始化图片数组
    
    // 获取发布者会员信息
    if (article.value.userId) {
      fetchAuthorMemberInfo(article.value.userId);
    }
    return;
  }
  
  if (!articleId) {
    loading.value = false;
    console.error('文章ID无效');
    return;
  }

  try {
    console.log('开始获取文章详情，ID:', articleId);
    const response = await getArticleDetail(articleId);
    if (response.code === 200) {
      article.value = response.data || {};
      console.log('获取文章详情成功:', article.value);
      // 确保文章数据结构完整
      if (!article.value.tags) article.value.tags = [];
      if (!article.value.articleImgList) article.value.articleImgList = [];
      
      // 初始化图片数组
      initializeImagesWithRatio();
      
      // 获取发布者会员信息
      if (article.value.userId) {
        fetchAuthorMemberInfo(article.value.userId);
      }
      
      // 检查文章ID是否正确
      if (article.value.id) {
        console.log('文章ID有效，可以加载评论组件:', article.value.id);
      } else {
        console.warn('文章对象中没有ID字段:', article.value);
        // 尝试使用路由参数作为ID
        article.value.id = articleId;
      }

      // 文章加载成功后，显式加载评论
      console.log('手动加载评论列表, 文章ID:', article.value.id);
    } else {
      console.error('获取文章详情失败:', response.msg);
      // 如果不是认证问题，显示错误提示
      if (response.code !== 401) {
        ElMessage.error('获取文章详情失败: ' + (response.msg || '未知错误'));
      }
    }
  } catch (error) {
    console.error('获取文章详情出错:', error);
    ElMessage.error('获取文章详情失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 获取发布者会员信息
const fetchAuthorMemberInfo = async (userId) => {
  try {
    console.log('获取发布者会员信息，用户ID:', userId);
    const response = await getUserMemberTypeById(userId);
    if (response.code === 200) {
      // 更新文章对象中的会员类型
      article.value.memberType = response.data;
      console.log('获取发布者会员信息成功:', response.data);
    } else {
      console.error('获取发布者会员信息失败:', response.msg);
    }
  } catch (error) {
    console.error('获取发布者会员信息出错:', error);
  }
};

// 测试加载评论
const testLoadComments = async () => {
  try {
    console.log('开始测试加载评论, 文章ID:', article.value.id);
    
    // 1. 使用API模块请求
    console.log('方法1: 使用API模块请求');
    const response1 = await import('../api/comment').then(commentApi => {
      return commentApi.getCommentList(article.value.id, 'article');
    });
    console.log('API模块请求结果:', response1);
    
    // 2. 使用axios直接请求
    console.log('方法2: 使用axios直接请求');
    const baseURL = import.meta.env.VITE_BASE_API || '';
    console.log('基础URL:', baseURL);
    
    const userStore = useUserStore();
    const headers = {};
    if (userStore.token) {
      headers['Authorization'] = `Bearer ${userStore.token}`;
    }
    
    const response2 = await axios.get(`${baseURL}/comment/list`, {
      params: {
        targetId: article.value.id,
        targetType: 'article'
      },
      headers
    });
    console.log('axios请求结果:', response2.data);
    
    // 显示请求结果
    if (response1.code === 200) {
      console.log('测试加载评论成功:', response1.data);
      // 更新评论列表
      article.value.commentList = response1.data || [];
      ElMessage.success('评论加载成功，请查看控制台');
    } else {
      console.error('测试加载评论失败:', response1.msg);
      ElMessage.error('测试加载评论失败: ' + (response1.msg || '未知错误'));
    }
  } catch (error) {
    console.error('测试加载评论出错:', error);
    ElMessage.error('测试加载评论失败，请稍后再试');
  }
};

onMounted(() => {
  // 获取文章详情
  fetchArticleDetail();
  
  // 直接调试，在控制台输出当前文章ID
  console.log('ArticleDetail组件挂载，路由参数ID:', route.params.id);
});

// 直接加载文章数据的方法（供外部调用）
const loadArticleData = (data) => {
  if (!data) return;
  
  console.log('直接加载文章数据:', data);
  article.value = data;
  loading.value = false;
  
  // 确保文章数据结构完整
  if (!article.value.tags) article.value.tags = [];
  if (!article.value.articleImgList) article.value.articleImgList = [];
  
  // 初始化图片数组
  initializeImagesWithRatio();
};

// 如果是作为页面打开的，则检查URL参数中是否包含直接的文章数据
if (typeof window !== 'undefined' && window.location.search) {
  const urlParams = new URLSearchParams(window.location.search);
  const articleDataParam = urlParams.get('articleData');
  if (articleDataParam) {
    try {
      const decodedData = JSON.parse(decodeURIComponent(articleDataParam));
      loadArticleData(decodedData);
    } catch (error) {
      console.error('解析URL中的文章数据失败:', error);
    }
  }
}

// 计算属性：截断的公司简介
const truncatedBusinessIntro = computed(() => {
  if (!article.value.introduction) return '';
  return article.value.introduction.length > 10 
    ? article.value.introduction.substring(0, 10) + '...' 
    : article.value.introduction;
});

// 当前预览图片
const currentPreviewImage = computed(() => {
  if (previewState.value.currentIndex >= 0 && 
      previewState.value.images && 
      previewState.value.images.length > 0) {
    return previewState.value.images[previewState.value.currentIndex];
  }
  return null;
});

// 图片加载错误处理
const handleImageError = () => {
  console.error('图片加载失败');
  ElMessage.error('图片加载失败，请稍后再试');
};
</script>

<style scoped>
.article-detail-container {
  width: 80%;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding-top: 80px;
  /* 为固定定位的header预留空间 */
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 64px;
}

.header-content {
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
  width: 180px;
}

.logo-image {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: transform 0.3s;
}

.logo-image:hover {
  transform: scale(1.05);
}



.header-center {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 0 0 auto;
  width: 180px;
}

.exit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  padding: 9px 18px;
  border-radius: 24px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.35);
  height: 40px;
  white-space: nowrap;
}

.exit-button:hover {
  background: linear-gradient(135deg, #5258ef, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.45);
}

.exit-icon {
  font-size: 18px;
  line-height: 1;
  font-weight: 300;
  margin-top: 5px;
}

/* 文章内容 */
.article-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.article-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.article-title {
  font-size: 24px;
  color: #333;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #999;
  font-size: 14px;
  flex-wrap: wrap;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  padding: 5px;
  border-radius: 20px;
}

.author-info:hover {
  background-color: #f0f2f5;
}

.author-avatar-container {
  position: relative;
  width: 48px;
  height: 48px;
  margin-right: 8px;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.article-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.publish-time,
.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.article-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  color: #1890ff;
  background: #e6f7ff;
  transition: all 0.3s;
  border: 1px solid #91d5ff;
}

/* 调试按钮样式 */
.debug-actions {
  margin-top: 20px;
  text-align: center;
}

.debug-button {
  padding: 8px 16px;
  background-color: #ff7875;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.debug-button:hover {
  background-color: #ff4d4f;
}

.tag:hover {
  background: #bae7ff;
  transform: translateY(-2px);
}

.article-body {
  margin-bottom: 24px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.content-text {
  white-space: pre-line;
  line-height: 1.8;
  color: #333;
  font-size: 16px;
}

.article-images {
  margin-top: 24px;
  background-color: #fff;
  padding: 20px; /* 增加padding以匹配图片容器 */
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.images-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 500;
}

/* 新的固定尺寸图片容器样式 */
.fixed-image-container {
  width: 100%; /* 占满article-images的全部宽度 */
  margin: 0 auto;
  background: #f8f9fa; /* 与背景相近的颜色 */
  border: 1px solid #e9ecef; /* 淡灰色边框 */
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px; /* 增加内边距到20px */
}

.image-layout {
  display: flex;
  width: 100%;
  height: 450px; /* 固定高度 */
  background: #f8f9fa;
  gap: 15px; /* 增加图片间的缝隙 */
}

/* 1张图片：居中显示，占50%宽度 */
.image-layout.layout-1 {
  justify-content: flex-start;
}

.image-layout.layout-1 .image-slot {
  width: 50%;
}

/* 2张图片：左右2等分 */
.image-layout.layout-2 .image-slot {
  width: calc(50% - 5px); /* 减去gap的一半 */
}

/* 3张图片：左中右3等分 */
.image-layout.layout-3 .image-slot {
  width: calc(33.333% - 6.67px); /* 减去gap的比例 */
}

.image-slot {
  position: relative;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

.image-slot:hover {
  transform: scale(1.02);
  z-index: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.fixed-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain; /* 完整显示图片，保持比例，不裁剪 */
  object-position: center;
  transition: transform 0.3s ease;
  border-radius: 4px;
}

.image-slot:hover .fixed-image {
  transform: scale(1.05);
}

/* 保留旧的图片样式作为备用，但主要使用新的固定容器样式 */
.article-image {
  width: 100%;
  object-fit: contain;
  object-position: center center;
  transition: transform 0.2s;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 预览模式下的图片样式 - 所有比例的图片在预览时使用相同的固定高度样式 */
.preview-image.article-image-square,
.preview-image.article-image-wide,
.preview-image.article-image-tall,
.preview-image.article-image-ultratall {
  height: 70vh !important; /* 强制使用统一固定高度 */
  width: auto !important; /* 宽度自动适应 */
  min-height: 0 !important; /* 移除最小高度限制 */
  max-height: none !important; /* 移除最大高度限制 */
  object-fit: contain !important;
}

/* 预览控件容器 */
.preview-controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  gap: 20px;
}

/* 导航控件 */
.preview-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 8px 15px;
  border-radius: 20px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.nav-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
  transition: all 0.2s;
}

.nav-button:hover {
  background: #f0f0f0;
  color: #e53935;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-counter {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 10px;
  min-width: 60px;
  text-align: center;
}

/* 缩放控件 */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.zoom-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
  transition: all 0.2s;
}

.zoom-button:hover:not(:disabled) {
  background: #f0f0f0;
  color: #e53935;
  transform: scale(1.05);
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-display {
  color: #333;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  min-width: 45px;
  text-align: center;
}

/* 预览图片样式更新 */
.preview-image {
  max-height: 80vh !important; /* 增加最大高度 */
  width: auto !important;
  height: auto !important;
  min-width: 60vw !important; /* 增加最小宽度 */
  max-width: 90vw !important; /* 增加最大宽度 */
  object-fit: contain !important;
  transition: transform 0.1s ease-out;
  user-select: none;
  -webkit-user-drag: none;
  transform-origin: center center; /* 确保从中心缩放 */
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .article-detail-container {
    width: 90%;
  }

  .header-content {
    width: 90%;
  }
}

@media (max-width: 768px) {
  .article-detail-container {
    width: 100%;
    padding-top: 64px;
  }

  .header-content {
    width: 100%;
    padding: 0 12px;
  }

  .header {
    padding: 12px 0;
  }

  .header-left {
    width: auto;
  }



  .article-content {
    padding: 16px;
    margin: 12px;
    border-radius: 8px;
  }

  .article-title {
    font-size: 20px;
  }

  /* 移动端固定图片容器调整 */
  .fixed-image-container {
    width: 100%; /* 移动端也占满全宽 */
    padding: 15px; /* 移动端适中的内边距 */
  }

  .image-layout {
    height: 300px; /* 移动端固定高度 */
    gap: 12px; /* 移动端适中的缝隙 */
  }

  /* 移动端3张图片改为2列布局 */
  .image-layout.layout-3 {
    flex-wrap: wrap;
    height: 400px; /* 固定高度以容纳两行 */
  }

  .image-layout.layout-3 .image-slot {
    width: calc(50% - 6px); /* 调整宽度以适应新的gap */
    height: calc(50% - 6px);
  }

  .image-layout.layout-3 .image-slot:nth-child(3) {
    width: 100%;
  }

  /* 调整移动端的宽度计算 */
  .image-layout.layout-2 .image-slot {
    width: calc(50% - 6px);
  }

  .preview-container {
    width: 98vw;
    max-width: 98vw;
    padding: 5px;
    padding-top: 30px; /* 调整顶部高度 */
  }

  .image-inner {
    min-width: 300px; /* 移动端减小最小宽度 */
  }

  .preview-image {
    min-width: 70vw !important; /* 移动端使用70%视口宽度 */
    max-width: 90vw !important;
  }

  .close-preview {
    top: 8px;
    right: 8px;
    font-size: 24px;
  }
  
  .preview-controls-container {
    flex-direction: column;
    gap: 10px;
  }

  .preview-navigation {
    padding: 6px 10px;
    gap: 10px;
  }

  .zoom-controls {
    padding: 6px 8px;
    gap: 6px;
  }

  .nav-button,
  .zoom-button {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .preview-container {
    width: 99vw;
    max-width: 99vw;
    padding: 3px;
    padding-top: 25px; /* 调整顶部高度 */
  }

  .image-inner {
    min-width: 250px; /* 超小屏幕进一步减小 */
  }

  .preview-image {
    min-width: 80vw !important; /* 超小屏幕使用80%视口宽度 */
    max-width: 95vw !important;
  }
  
  .close-preview {
    top: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    font-size: 20px;
  }
  
  .preview-navigation {
    padding: 5px 8px;
    gap: 8px;
  }

  .zoom-controls {
    padding: 5px 6px;
    gap: 5px;
  }

  .nav-button,
  .zoom-button {
    width: 26px;
    height: 26px;
  }

  .zoom-display {
    font-size: 11px;
    min-width: 40px;
    padding: 3px 6px;
  }
}

/* 用户信息弹窗样式 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.user-info-card {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px; /* 增加最大宽度 */
  max-height: 90vh;
  overflow-y: auto;
  padding: 20px; /* 增加内边距 */
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.user-info-content-new {
  padding: 20px 15px;
}

.user-info-layout {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
  gap: 15px;
}

.user-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar-new {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-info-side {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.vip-badge {
  position: absolute;
  bottom: 0;
  right: -5px;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 20;
  line-height: 1;
  min-width: 18px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #fbbf24;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border-color: #fbbf24;
  animation: svip-glow 2s ease-in-out infinite alternate;
}

/* 文章头部作者信息区域的VIP徽章位置调整 */
.header-vip-badge {
  position: relative;
  bottom: auto;
  right: auto;
  margin-left: 5px;
  font-size: 0.6rem;
  height: 16px;
  min-width: 30px;
  border-radius: 8px;
  padding: 1px 4px;
  animation: none;
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(59, 130, 246, 0.5);
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(251, 191, 36, 0.5);
  }
}

.user-info-rows {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-row {
  margin-bottom: 5px;
}

.user-name-new {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.business-intro {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
  position: relative;
  cursor: default;
}

.business-intro[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-label {
  color: #666;
  min-width: 70px;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-size: 14px;
}

.info-id {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  font-weight: bold;
}

.article-image {
  width: 100%;
  object-fit: contain;
  object-position: center center;
  transition: transform 0.2s;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 图片预览弹窗 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  overflow: auto;
}

.preview-container {
  position: relative;
  width: 95vw; /* 进一步增加宽度 */
  max-width: 1400px; /* 增加最大宽度 */
  height: auto;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px solid #6c757d; /* 灰色边框 */
  border-radius: 8px;
  background-color: white;
  padding: 8px; /* 减少padding */
  padding-top: 35px; /* 减少顶部padding */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.image-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80vh; /* 增加高度，减少空白 */
  overflow: hidden; /* 确保图片不会超出容器 */
  background-color: white;
  position: relative;
  min-width: 600px; /* 设置最小宽度 */
}

.preview-image {
  height: 70vh; /* 固定高度 */
  width: auto; /* 宽度自适应 */
  object-fit: contain;
  max-width: 90vw; /* 防止超宽图片溢出 */
}

.close-preview {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  color: #e53935;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1200;
  transition: all 0.2s;
}

.close-preview:hover {
  transform: scale(1.1);
  color: #c62828;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  width: 100%;
  color: #e53935;
  font-size: 16px;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}

/* 加载指示器样式 */
.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
</style>