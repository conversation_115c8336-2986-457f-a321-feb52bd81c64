<template>
  <div class="personal-center">
    <!-- 主内容区 -->
    <div class="content-container">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <!-- 返回按钮 -->
        <div class="back-to-map" @click="backToMap">
          <i-lucide-map class="back-icon" />
          <span>{{ t('userInfo.backToHome') }}</span>
        </div>

        <div class="profile-section">
                  <div class="avatar-container" @click="showAvatarModal">
          <!-- VIP 徽章 - 根据会员等级显示，放在外部 -->
          <div v-if="userInfo.memberType === '普通会员'" class="vip-badge vip-regular">
            VIP
          </div>
          <div v-else-if="userInfo.memberType === '超级会员'" class="vip-badge vip-super">
            SVIP
          </div>
          <div class="avatar">
            <img :src="avatarUrl" alt="用户头像" />
          </div>
          <div class="avatar-overlay">
            <i-lucide-camera class="camera-icon" />
            <span>{{ t('userInfo.changeAvatar') }}</span>
          </div>
        </div>
          <h2 class="username">{{ userInfo.nickName || t('userInfo.profile.name') }}</h2>
          <p class="user-location">
            <i-lucide-map-pin class="icon" />
            <span>{{ userInfo.location || t('userInfo.profile.locationLoading') }}</span>
          </p>
        </div>

        <nav class="navigation">
          <div v-for="item in navItems" :key="item.id" :class="['nav-item', { active: activeNav === item.id }]"
            @click="handleNavClick(item.id)">
            <component :is="item.icon" class="nav-icon" />
            <span class="nav-text">{{ item.name }}</span>
          </div>
        </nav>

        <div class="sidebar-footer">
          <button class="logout-btn" @click="Logout">
            <i-lucide-log-out class="icon" />
            <span>{{ t('userInfo.profile.logout') }}</span>
          </button>
        </div>
      </aside>

      <!-- 主要内容 -->
      <main class="main-content">
        <header class="content-header">
          <h1>{{ getHeaderTitle() }}</h1>
        </header>

        <div class="content-body">
          <!-- 个人资料 -->
          <div v-if="activeNav === 'profile'" class="profile-card">
            <div class="card-header">
              <h2>{{ t('userInfo.profile.title') }}</h2>
              <p>{{ t('userInfo.profile.subtitle') }}</p>
            </div>

            <!-- 个人资料表单 -->
            <form @submit.prevent="saveProfile">
              <!-- 账号信息部分 -->
              <div class="form-section">
                <div class="section-header">
                  <h3>{{ t('userInfo.profile.accountInfo') }}</h3>
                </div>
                
                <div class="account-info-grid">
                  <!-- 会员类型 -->
                  <div class="account-info-item">
                    <div class="info-label">
                      <i-lucide-crown class="info-icon" />
                      <span class="section-title">{{ t('userInfo.profile.memberType') }}</span>
                    </div>
                    <div class="info-value" :class="{'premium-member': userInfo.memberType === '超级会员'}">
                      {{ userInfo.memberType || t('userInfo.profile.regularUser') }}
                    </div>
                  </div>
                  
                  <!-- 已开通站点 - 靠右浮动 -->
                  <div class="account-info-item sites-info float-right">
                    <div class="info-label">
                      <i-lucide-map-pin class="info-icon" />
                      <span class="section-title">{{ t('userInfo.profile.sitesInfo') }}</span>
                      <button class="add-site-btn" @click="handleAddSite">
                        <span>增加新站点</span>
                      </button>
                    </div>
                    <div class="site-badges">
                      <span v-if="userSites.length === 0" class="no-sites-text">
                        无
                      </span>
                      <span v-else v-for="(site, index) in userSites" :key="index" class="site-badge" 
                        :title="site.nameEn ? `${site.name} (${site.nameEn}) - ${site.memberType}` : `${site.name} - ${site.memberType}`">
                        {{ site.name }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <!-- 微信绑定按钮 -->
                <div class="wechat-binding-section" v-if="!isWechatBound">
                  <div class="wechat-binding-info">
                    <div class="binding-icon-container">
                      <img src="../../public/image/wx.jpg" alt="WeChat" class="wechat-icon" />
                    </div>
                    <div class="binding-text">
                      <h4>{{ t('userInfo.profile.wechatBinding.title') }}</h4>
                     
                    </div>
                  </div>
                  <button type="button" class="bind-wechat-btn" @click="openBindWechatModal">
                    <i-lucide-link class="btn-icon" />
                    <span>{{ t('userInfo.profile.wechatBinding.bindButton') }}</span>
                  </button>
                </div>
              </div>
              
              <div class="form-section">
                <div class="section-header">
                  <h3>{{ t('userInfo.profile.required') }}</h3>
                </div>
              
                <div class="form-grid">
                  <!-- 姓名 -->
                  <div class="form-group name-field">
                    <label for="name">{{ t('userInfo.profile.name') }}</label>
                    <div class="input-container">
                      <i-lucide-user class="input-icon" />
                      <input id="name" v-model="userInfo.nickName" type="text"
                        :placeholder="t('userInfo.profile.namePlaceholder')" />
                    </div>
                  </div>

                  <!-- 职位 -->
                  <div class="form-group">
                    <label for="position">{{ t('userInfo.profile.position') }}</label>
                    <div class="input-container">
                      <i-lucide-briefcase class="input-icon" />
                      <input id="position" v-model="userInfo.post" type="text"
                        :placeholder="t('userInfo.profile.position')" />
                    </div>
                  </div>

                  <!-- 公司名称 -->
                  <div class="form-group">
                    <label for="company">{{ t('userInfo.profile.company') }}</label>
                    <div class="input-container">
                      <i-lucide-building class="input-icon" />
                      <input id="company" v-model="userInfo.companyName" type="text"
                        :placeholder="t('userInfo.profile.companyPlaceholder')" />
                    </div>
                  </div>

                  <!-- 联系电话 -->
                  <div class="form-group">
                    <label for="phone">{{ t('userInfo.profile.phone') }}</label>
                    <div class="input-container">
                      <i-lucide-phone class="input-icon" />
                      <input id="phone" v-model="userInfo.phonenumber" type="tel"
                        :placeholder="t('userInfo.profile.phonePlaceholder')" />
                    </div>
                  </div>

                  <!-- 邮箱 -->
                  <div class="form-group">
                    <label for="email">{{ t('userInfo.profile.email') }}</label>
                    <div class="input-container">
                      <i-lucide-mail class="input-icon" />
                      <input id="email" v-model="userInfo.email" type="email"
                        :placeholder="t('userInfo.profile.emailPlaceholder')" />
                    </div>
                  </div>

                  <!-- 微信 -->
                  <div class="form-group">
                    <label for="wechat">{{ t('userInfo.profile.wechat') }}</label>
                    <div class="input-container">
                      <i-lucide-message-square class="input-icon" />
                      <input id="wechat" v-model="userInfo.wx" type="text"
                        :placeholder="t('userInfo.profile.promptdialogbox')" disabled />
                    </div>
                  </div>

                  <!-- QQ -->
                  <div class="form-group">
                    <label for="qq">{{ t('userInfo.profile.qq') }}</label>
                    <div class="input-container">
                      <i-lucide-message-circle class="input-icon" />
                      <input id="qq" v-model="userInfo.qq" type="text"
                        :placeholder="t('userInfo.profile.promptdialogbox')" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 文件上传区域 -->
              <div class="form-section">
                <div class="section-header">
                  <h3>{{ t('userInfo.profile.fileUpload') }}</h3>
                </div>

                <div class="upload-grid">
                  <!-- 名片上传 -->
                  <div class="business-card-upload" @click="triggerFileUpload('businessCard')">
                    <input type="file" ref="businessCardInput" class="hidden" accept="image/*"
                      @change="handleFileUpload('businessCard', $event)" />

                    <div v-if="!userInfo.businessCard" class="upload-placeholder">
                      <div class="upload-icon">
                        <i-lucide-credit-card class="icon" />
                      </div>
                      <div class="upload-text">
                        <h4>{{ t('userInfo.profile.businessCardUpload') }}</h4>
                        <p>{{ t('userInfo.profile.businessCardUploadHint') }}</p>
                      </div>
                    </div>

                    <div v-else class="business-card-preview" @click.stop="showBusinessCardModal">
                      <img :src="businessCardPreview" :alt="t('userInfo.profile.businessCardUpload')" class="card-image" />
                      <div class="card-overlay">
                        <div class="overlay-content">
                          <i-lucide-eye class="view-icon" />
                          <span>{{ t('userInfo.profile.clicktoview') }}</span>
                        </div>
                        <button type="button" class="remove-card" @click.stop="removeFile('businessCard')">
                        X
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- 营业执照上传 -->
                  <div class="business-license-upload" @click="triggerFileUpload('businessLicense')">
                    <input type="file" ref="businessLicenseInput" class="hidden" accept="image/*"
                      @change="handleFileUpload('businessLicense', $event)" />

                    <div v-if="!userInfo.businessLicense" class="upload-placeholder">
                      <div class="upload-icon">
                        <i-lucide-file-text class="icon" />
                      </div>
                      <div class="upload-text">
                        <h4>{{ t('userInfo.profile.businessLicenseUpload') }}</h4>
                        <p>{{ t('userInfo.profile.businessLicenseUploadHint') }}</p>
                      </div>
                    </div>

                    <div v-else class="business-license-preview" @click.stop="showBusinessLicenseModal">
                      <img :src="businessLicensePreview" :alt="t('userInfo.profile.businessLicenseUpload')" class="license-image" />
                      <div class="license-overlay">
                        <div class="overlay-content">
                          <i-lucide-eye class="view-icon" />
                          <span>{{ t('userInfo.profile.clicktoview') }}</span>
                        </div>
                        <button type="button" class="remove-license" @click.stop="removeFile('businessLicense')">
                        X
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 表单操作按钮 -->
              <div class="form-actions">
                <button type="button" class="btn-reset" @click="resetForm">
                  <i-lucide-refresh-cw class="icon" />
                  <span>{{ t('userInfo.profile.reset') }}</span>
                </button>
                <button type="submit" class="btn-save">
                  <i-lucide-save class="icon" />
                  <span>{{ t('userInfo.profile.save') }}</span>
                </button>
              </div>
            </form>
          </div>

          <!-- 抵值券内容 -->
          <div v-if="activeNav === 'vouchers'" class="vouchers-container">
            <div class="card-header">
              <h2>{{ t('userInfo.vouchers.title') }}</h2>
            </div>

            <div v-if="vouchers.length === 0" class="empty-vouchers">
              <div class="empty-icon">
                <i-lucide-ticket class="icon" />
              </div>
              <h3>{{ t('userInfo.vouchers.empty') }}</h3>
            </div>

            <div v-else class="vouchers-list">
              <div v-for="(voucher, index) in vouchers" :key="index" class="voucher-card"
                :class="{ 
                  'voucher-expired': voucher.isUsed === 2,
                  'voucher-used': voucher.isUsed === 1,
                  'voucher-available': voucher.isUsed === 0 
                }">
                <!-- 可用标记，只在可用券上显示 -->
                <div v-if="voucher.isUsed === 0" class="voucher-ribbon">
                  <span class="available-voucher-label">可使用</span>
                </div>
                
                <div class="voucher-left">
                  <div class="voucher-amount">
                    <span class="currency">¥</span>
                    <span class="value">{{ voucher.amount }}</span>
                  </div>
                  <div class="voucher-threshold" v-if="voucher.threshold > 0">
                    {{ t('userInfo.vouchers.threshold', { amount: voucher.threshold }) }}
                  </div>
                  <button v-if="voucher.isUsed === 0" class="voucher-use-btn" @click="goToMemberCenter($event)">
                    {{ t('userInfo.vouchers.goUse') }}
                  </button>
                </div>

                <div class="voucher-divider">
                  <div class="circle top"></div>
                  <div class="dashed-line"></div>
                  <div class="circle bottom"></div>
                </div>

                <div class="voucher-right">
                  <div class="voucher-name">{{ t('userInfo.vouchers.title') }}</div>
                  <div class="voucher-validity">
                    <div class="validity-date">
                      <i-lucide-calendar class="icon" />
                      <span>{{ t('userInfo.vouchers.validUntil') }}: {{ formatDate(voucher.expiryTime) }}</span>
                    </div>
                    <!-- <div class="validity-days" :class="getDaysClass(voucher.validDays)">
                      <i-lucide-clock class="icon" />
                      <span>{{ getRemainingDaysText(voucher.validDays) }}</span>
                    </div> -->
                  </div>
                  <div class="voucher-status">
                    <span v-if="voucher.isUsed !== 0" class="status-badge" :class="getStatusClass(voucher)">
                      {{ getStatusText(voucher) }}
                    </span>
                    <router-link v-else to="/member" class="use-voucher-btn">
                      去使用
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 支付记录内容 -->
          <div v-if="activeNav === 'payment-records'" class="payment-records-container">
            <div class="card-header">
              <h2>{{ t('userInfo.payment.title') }}</h2>
              <p>{{ t('userInfo.payment.subtitle') }}</p>
            </div>
            
            <!-- 搜索筛选区域 -->
            <div class="search-filter-container">
              <div class="search-form">
                <div class="form-item">
                  <label>{{ t('userInfo.payment.search.orderNo') }}</label>
                  <input type="text" v-model="searchParams.orderNo" :placeholder="t('userInfo.payment.search.orderNo')" />
                </div>
                <div class="form-item">
                  <label>{{ t('userInfo.payment.search.status') }}</label>
                  <select v-model="searchParams.status">
                    <option value="">{{ t('userInfo.payment.search.all') }}</option>
                    <option value="1">{{ t('userInfo.payment.search.paid') }}</option>
                    <option value="2">{{ t('userInfo.payment.search.unpaid') }}</option>
                    <option value="3">{{ t('userInfo.payment.search.canceled') }}</option>
                    <option value="4">{{ t('userInfo.payment.search.refunded') }}</option>
                  </select>
                </div>
                <div class="form-actions">
                  <button class="search-btn" @click="handleSearch">
                    {{ t('userInfo.payment.search.searchBtn') }}
                  </button>
                  <button class="reset-btn" @click="resetSearch">
                    {{ t('userInfo.payment.search.resetBtn') }}
                  </button>
                </div>
              </div>
            </div>

            <div class="payment-records-list">
              <!-- 加载状态 -->
              <div v-if="paymentRecordsLoading" class="loading-container">
                <div class="loading-spinner"></div>
                <span>{{ t('userInfo.payment.loading') }}</span>
              </div>

              <!-- 空状态 -->
              <div v-else-if="!paymentRecords.length" class="empty-state">
                <div class="empty-icon">
                  <i-lucide-file-x class="icon" />
                </div>
                <h3>{{ t('userInfo.payment.empty.title') }}</h3>
                <p>{{ t('userInfo.payment.empty.subtitle') }}</p>
              </div>

              <!-- 数据展示 -->
              <div v-else class="records-table-container">
                <table class="records-table">
                  <thead>
                    <tr>
                      <th>{{ t('userInfo.payment.table.orderNo') }}</th>
                      <th>{{ t('userInfo.payment.table.productName') }}</th>
                      <th>{{ t('userInfo.payment.table.amount') }}</th>
                      <th>{{ t('userInfo.payment.table.status') }}</th>
                      <th>{{ t('userInfo.payment.table.payTime') }}</th>
                      <th>{{ t('userInfo.payment.table.actions') }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="record in paymentRecords" :key="record.orderNo" :class="{ 'paid': record.status === 1 }">
                      <td>{{ record.orderNo }}</td>
                      <td>{{ record.title }}</td>
                      <td class="amount">¥{{ record.amount.toFixed(2) }}</td>
                      <td>
                        <span :class="['status-badge', getStatusClass(record.status)]">
                          {{ getPaymentStatusText(record.status) }}
                        </span>
                      </td>
                      <td>{{ record.payTime ? formatDate(record.payTime, true) : t('userInfo.payment.table.unpaidText') }}</td>
                      <td class="actions">
                        <button 
                          class="detail-btn" 
                          @click="viewPaymentDetail(record)"
                          :disabled="record.status === 0 || record.status === 2"
                          :class="{'detail-btn-disabled': record.status === 0 || record.status === 2}"
                        >
                          {{ t('userInfo.payment.table.viewDetails') }}
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination-container" v-if="pagination.total > 10">
                  <el-pagination
                    v-model="pagination.current"
                    :page-size="10"
                    layout="total, prev, pager, next, jumper"
                    :total="pagination.total"
                    @current-change="handlePageChange"
                    prev-text="上一页"
                    next-text="下一页"
                    :pager-count="5"
                    key="payment-pagination"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 业务介绍内容 -->
          <div v-if="activeNav === 'help'" class="business-intro-container">
            <div class="card-header">
              <h2>{{ t('userInfo.businessIntro.title') }}</h2>
              <p>{{ t('userInfo.businessIntro.subtitle') }}</p>
            </div>

            <div class="business-intro-content">
              <div class="intro-text-container" :class="{ 'editing': isEditing }">
                <textarea v-if="isEditing" v-model="businessIntroText" class="intro-textarea"
                  :placeholder="t('userInfo.businessIntro.placeholder')" maxlength="500" @input="updateCharCount"></textarea>
                <div v-else class="intro-display" v-html="formattedBusinessIntro"></div>
                <div v-if="isEditing" class="char-count">
                  {{ t('userInfo.businessIntro.charCount', { count: charCount }) }}
                </div>
              </div>

              <div class="intro-actions">
                <button v-if="isEditing" class="btn-cancel" @click="cancelEdit">
                  {{ t('userInfo.businessIntro.cancel') }}
                </button>
                <button v-if="isEditing" class="btn-save" @click="saveBusinessIntro">
                  {{ t('userInfo.businessIntro.save') }}
                </button>
                <button v-else class="btn-edit" @click="startEdit">
                  {{ t('userInfo.businessIntro.edit') }}
                </button>
              </div>
            </div>
          </div>

          <!-- 我发布的咨询 -->
          <div v-if="activeNav === 'consult'">
            <div class="card-header">
              <h2>{{ t('userInfo.consult.title') }}</h2>
              <p>{{ t('userInfo.consult.subtitle') }}</p>
            </div>

            <div v-if="userArticles.length === 0" class="empty-consult">
              <div class="empty-icon">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </div>
              <h3>{{ t('userInfo.consult.empty.title') }}</h3>
              <p>{{ t('userInfo.consult.empty.subtitle') }}</p>
            </div>

            <div v-else class="article-grid">
              <div v-for="article in userArticles" :key="article.id" class="article-card">
                <!-- 文章图片或内容预览 -->
                <div class="article-image-container" @click="viewArticleDetail(article.id)">
                  <img 
                    v-if="hasArticleImage(article)"
                    :src="getArticleImage(article)" 
                    :alt="t('userInfo.consult.articleImage')" 
                    class="article-image"
                    @error="handleImageError"
                  />
                  <!-- 没有图片时显示文章内容 -->
                  <div v-else class="card-no-image">
                    <div class="card-no-image-text" :title="article.content">{{ article.content || article.title || '无标题' }}</div>
                  </div>
                  <!-- 编辑按钮浮层 -->
                  <div class="article-actions-overlay">
                    <button class="action-btn edit-btn" @click.stop="editConsult(article.id)">
                      <svg class="icon-sm" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                      </svg>
                    </button>
                    <button class="action-btn delete-btn" @click.stop="deleteConsult(article.id)">
                      <svg class="icon-sm" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                      </svg>
                    </button>
                  </div>
                </div>
                
                <!-- 文章信息 -->
                <div class="article-info" @click="viewArticleDetail(article.id)">
                  <h3 class="article-title">{{ article.title }}</h3>
                  
                  <div class="article-meta">
                    <div class="article-author">
                      <img :src="getUserAvatar(article)" alt="作者头像" class="author-avatar" />
                      <span>{{ article.nickName || article.username || '匿名用户' }}</span>
                    </div>
                    
                    <div class="article-stats">
                      <div class="stat">
                        <svg class="icon-sm" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                        <span>{{ t('userInfo.consult.likeCount', { count: article.likeCount || 0 }) }}</span>
                      </div>
                      <div class="stat">
                        <svg class="icon-sm" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        <span>{{ formatDateString(article.createTime) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 我的点赞 -->
          <div v-if="activeNav === 'likes'">
            <div class="card-header">
              <h2>{{ t('userInfo.likes.title') }}</h2>
              <p>{{ t('userInfo.likes.subtitle') }}</p>
            </div>

            <div v-if="loadingLikedArticles" class="loading-container">
              <div class="loading-spinner"></div>
            </div>

            <div v-else-if="likedArticles.length === 0" class="empty-state">
              <div class="empty-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </div>
              <h3>{{ t('userInfo.likes.empty.title') }}</h3>
              <p>{{ t('userInfo.likes.empty.subtitle') }}</p>
            </div>

            <div v-else class="article-grid">
              <div v-for="article in likedArticles" :key="article.id" class="article-card">
                <!-- 文章图片或内容预览 -->
                <div class="article-image-container" @click="viewArticleDetail(article.id)">
                  <img 
                    v-if="hasArticleImage(article)"
                    :src="getArticleImage(article)" 
                    :alt="t('userInfo.consult.articleImage')" 
                    class="article-image"
                    @error="handleImageError"
                  />
                  <!-- 没有图片时显示文章内容 -->
                  <div v-else class="card-no-image">
                    <div class="card-no-image-text" :title="article.content">{{ article.content || article.title || '无标题' }}</div>
                  </div>
                  
                  <!-- 添加点赞操作按钮 -->
                  <div class="article-actions-overlay">
                    <button class="action-button unlike-button" @click.stop="unlikeArticle(article)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                      </svg>
                      <span>{{ t('userInfo.likes.unlikeButton') }}</span>
                    </button>
                  </div>
                </div>
                
                <!-- 文章信息 -->
                <div class="article-info" @click="viewArticleDetail(article.id)">
                  <h3 class="article-title">{{ article.title }}</h3>
                  
                  <div class="article-meta">
                    <div class="article-author">
                      <img :src="getUserAvatar(article)" alt="作者头像" class="author-avatar" />
                      <span>{{ article.nickName || article.username || '匿名用户' }}</span>
                    </div>
                    
                    <div class="article-stats">
                      <!-- 只显示发布时间 -->
                      <div class="stat">
                        <svg class="icon-sm" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        <span>{{ formatDateString(article.createTime) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 密码修改 -->
          <div v-if="activeNav === 'settings'">
            <ChangePassword />
          </div>
        </div>
      </main>
    </div>

    <!-- 名片放大显示模态框 -->
    <div v-if="showCardModal" class="modal-overlay" @click="closeBusinessCardModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ t('userInfo.profile.businessCardUpload') }}</h3>
          <button class="modal-close" @click="closeBusinessCardModal">
            X
          </button>
        </div>
        <div class="modal-body">
          <img :src="businessCardPreview" :alt="t('userInfo.profile.businessCardUpload')" class="modal-image" />
        </div>
        <div class="modal-footer">
          <button class="btn-download" @click="downloadBusinessCard">
           
            <span>{{ t('userInfo.profile.downloadBusinessCard') }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 营业执照放大显示模态框 -->
    <div v-if="showLicenseModal" class="modal-overlay" @click="closeBusinessLicenseModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ t('userInfo.profile.businessLicenseUpload') }}</h3>
          <button class="modal-close" @click="closeBusinessLicenseModal">
        X
          </button>
        </div>
        <div class="modal-body">
          <img :src="businessLicensePreview" :alt="t('userInfo.profile.businessLicenseUpload')" class="modal-image" />
        </div>
        <div class="modal-footer">
          <button class="btn-download" @click="downloadBusinessLicense">
        
            <span>{{ t('userInfo.profile.downloadBusinessLicense') }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 头像预览和上传模态框 -->
    <div v-if="showAvatar" class="modal-overlay" @click="closeAvatarModal">
      <div class="modal-content avatar-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ t('userInfo.profile.title') }}</h3>
          <button class="modal-close" @click="closeAvatarModal">
            X
          </button>
        </div>
        <div class="modal-body">
          <div class="avatar-preview-container">
            <!-- VIP 徽章 - 根据会员等级显示 -->
            <div v-if="userInfo.memberType === '普通会员'" class="vip-badge vip-regular modal-vip-badge">
              VIP
            </div>
            <div v-else-if="userInfo.memberType === '超级会员'" class="vip-badge vip-super modal-vip-badge">
             SVIP
            </div>
            <img :src="avatarUrl" :alt="t('userInfo.profile.title')" class="avatar-preview" />
          </div>
        </div>
        <div class="modal-footer">
          <input type="file" ref="avatarInput" class="hidden" accept="image/*" @change="handleAvatarUpload" />
          <button class="btn-upload" @click="triggerAvatarUpload">
            <i-lucide-upload class="icon" />
            <span>{{ t('userInfo.profile.Avatar') }}</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 编辑功能已移至CreatePost.vue -->
    
    <!-- 支付详情Dialog -->
    <el-dialog
      v-model="showPaymentDetailModal"
      :title="t('userInfo.payment.detail.title')"
      width="550px"
      :close-on-click-modal="true"
      :show-close="true"
      @close="closePaymentDetailModal"
      destroy-on-close
      class="payment-detail-dialog"
    >
      <div v-if="currentOrderDetail" class="payment-detail-content">
        <!-- 订单状态指示器 -->
        <div class="order-status-indicator">
          <div class="checkmark-circle animated" v-if="currentOrderDetail.status === 1">
            <div class="checkmark draw"></div>
          </div>
          <div class="status-icon" :class="getStatusClass(currentOrderDetail.status)" v-else>
            <i-lucide-clock v-if="currentOrderDetail.status === 0" class="icon" />
            <i-lucide-x v-else-if="currentOrderDetail.status === 2 || currentOrderDetail.status === 3" class="icon" />
            <i-lucide-rotate-ccw v-else-if="currentOrderDetail.status === 4" class="icon" />
          </div>
          <div class="status-text">{{ getPaymentStatusText(currentOrderDetail.status) }}</div>
        </div>
        
        <!-- 订单信息卡片 -->
        <div class="detail-card">
          <div class="detail-card-header">
            <i-lucide-file-text class="header-icon" />
            <h4>{{ t('userInfo.payment.detail.orderInfo') }}</h4>
          </div>
          
          <div class="detail-items-container">
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.amount') }}：</div>
              <div class="detail-value amount">¥{{ currentOrderDetail.amount.toFixed(2) }}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.orderNo') }}：</div>
              <div class="detail-value">{{ currentOrderDetail.orderNo }}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.productName') }}：</div>
              <div class="detail-value">{{ currentOrderDetail.title }}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.createTime') }}：</div>
              <div class="detail-value">{{ formatDateWithSeconds(currentOrderDetail.createTime) }}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.stationInfo') }}：</div>
              <div class="detail-value">{{ currentOrderDetail.stationNameZh && currentOrderDetail.stationNameEn ? `${currentOrderDetail.stationNameZh} (${currentOrderDetail.stationNameEn})` : '无' }}</div>
            </div>
          </div>
        </div>
        
        <!-- 用户信息卡片 -->
        <div class="detail-card">
          <div class="detail-card-header">
            <i-lucide-user class="header-icon" />
            <h4>{{ t('userInfo.payment.detail.userInfo') }}</h4>
          </div>
          
          <div class="detail-items-container">
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.userId') }}：</div>
              <div class="detail-value">{{ currentOrderDetail.userId }}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">{{ t('userInfo.payment.detail.nickname') }}：</div>
              <div class="detail-value">{{ currentOrderDetail.nickName || t('userInfo.payment.detail.notSet') }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button plain @click="closePaymentDetailModal">{{ t('userInfo.payment.detail.close') }}</el-button>
          <!-- 取消订单按钮 - 只有未支付状态(0)的订单可以取消 -->
          <el-button v-if="currentOrderDetail && currentOrderDetail.status === 0" 
                     type="danger" 
                     :icon="'Delete'"
                     @click="cancelOrder">
            {{ t('userInfo.payment.detail.cancelOrder') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 绑定微信模态框 -->
    <div v-if="showBindWechatModal" class="modal-overlay" @click="closeBindWechatModal">
      <div class="modal-content wechat-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ t('userInfo.profile.wechatBinding.title') }}</h3>
          <button class="modal-close" @click="closeBindWechatModal">
            <!-- <i-lucide-x class="icon" /> -->X
          </button>
        </div>
        <div class="modal-body wechat-modal-body">
          
          <div class="qr-code-container">
            <div class="qr-code">
              <iframe
                v-if="qrUrl"
                :src="qrUrl"
                frameborder="0"
                class="wx-login-frame"
                @load="handleIframeLoad"
              ></iframe>
              <div v-else class="qr-loading">
                <div class="loading-spinner"></div>
                <span>{{ t('userInfo.profile.wechatBinding.loadingQrCode') }}</span>
              </div>
            </div>
      
          </div>
        </div>
        <div class="modal-footer">
          <p class="wechat-bind-tip">{{ t('userInfo.profile.wechatBinding.scanInstruction') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '../composables/useI18n'
import { useUserStore } from '@/store/user'
import { getUserInfo, updataUserInfo as updateUserInfo, logout ,uploadAvatar,getopenidboolen} from '@/api/user'
import { getCouponList } from '@/api/voucher'
import { getUserPaymentRecords, getmembertype, getUserMemberInfo } from '@/api/pay'
import { getUserArticleList, getUserLikedArticles, deleteArticleLike, deleteArticle } from '@/api/article'
import { getQrCodeByState, checkScanStatus } from '@/api/wechat'
import ChangePassword from '@/components/ChangePassword.vue'

const userStore = useUserStore()
const { t } = useI18n()
const router = useRouter()

// 添加名片相关状态
const showCardModal = ref(false)
const businessCardPreview = ref('')

// 添加站点相关状态
const userSites = ref([])
const showAddSiteModal = ref(false)

// 添加营业执照相关状态
const showLicenseModal = ref(false)
const businessLicensePreview = ref('')

// 头像相关
const showAvatar = ref(false)
const avatarInput = ref(null)
const avatarUrl = ref(userStore.getAvatar)

// 添加位置相关状态
const location = ref({
  province: '',
  city: ''
})

// 不再需要文章编辑相关变量，因为编辑功能已移至CreatePost.vue

// 抵值券数据
const vouchers = ref([])

// 我的点赞文章列表
const likedArticles = ref([])
const loadingLikedArticles = ref(false)

// 格式化日期函数
const formatDateString = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) return dateStr; // 如果日期无效，直接返回原字符串
  
  return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化日期并显示秒
const formatDateWithSeconds = (dateStr) => {
  if (!dateStr) return '无';
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) return dateStr;
  
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
};

// 处理文章内容函数（截取预览）
const getContentPreview = (article) => {
  // 返回文章标题作为预览，实际可以根据需要从其他字段获取内容
  return `标题: ${article.title || '无标题'}`;
};

// 判断文章是否有图片
const hasArticleImage = (article) => {
  return article.articleImgList && article.articleImgList.length > 0 && article.articleImgList[0].imgUrl;
};

// 获取文章图片
const getArticleImage = (article) => {
  console.log('获取文章图片，文章数据:', article);

  // 检查文章是否有图片列表
  if (article.articleImgList && article.articleImgList.length > 0) {
    const imgUrl = article.articleImgList[0].imgUrl;
    console.log('找到图片URL:', imgUrl);

    // 检查图片URL是否存在
    if (imgUrl) {
      // 如果是完整URL则直接返回，否则添加API基础路径
      if (imgUrl.startsWith('http')) {
        console.log('返回完整URL:', imgUrl);
        return imgUrl;
      } else {
        const fullUrl = import.meta.env.VITE_BASE_API + imgUrl;
        console.log('返回拼接URL:', fullUrl);
        return fullUrl;
      }
    }
  }

  console.log('没有找到图片，返回null以显示文章内容');
  // 如果没有图片或URL无效，返回null，这样模板会显示文章内容
  return null;
};

// 获取用户头像
const getUserAvatar = (article) => {
  // 检查头像是否存在
  if (article.avatar) {
    // 如果是完整URL则直接返回，否则添加API基础路径
    if (article.avatar.startsWith('http')) {
      return article.avatar;
    } else {
      return import.meta.env.VITE_BASE_API + article.avatar;
    }
  }
  // 如果没有头像，返回默认头像
  return '/image/default-avatar.png';
};

// 查看文章详情
const viewArticleDetail = (id) => {
  // 添加来源标识，根据当前激活的导航项决定
  const fromSection = activeNav.value;
  router.push(`/article-detail/${id}?from=${fromSection}`);
};

// 使用计算属性从userArticles生成咨询列表
const consultList = computed(() => {
  return userArticles.value.map(article => ({
    id: article.id,
    title: article.title || '无标题',
    content: getContentPreview(article),
    date: formatDateString(article.createTime)
  }));
})

// 业务介绍相关状态
const isEditing = ref(false);
const businessIntroText = ref('');
const originalBusinessIntro = ref('');
const charCount = ref(0);

// 修改用户信息对象
const userInfo = reactive({
  userName: '',    // 用户账号，后端校验必填
  nickName: '',
  post: '', // 新增职位字段
  companyName: '',
  phonenumber: '',
  email: '',
  wx: '',
  qq: '',
  businessCard: null,
  businessLicense: null,
  memberType: '',  // 会员类型
  name: '',        // 用户真实姓名
  sex: '',         // 性别
  roleGroup: '',   // 角色组
  userId: null     // 用户ID
})

// 用户文章列表数据
const userArticles = ref([])

// 获取用户发布的文章
const fetchUserArticles = async () => {
  try {
    const userId = userInfo.userId || userStore.userInfo.userId
    
    // 确保用户ID存在
    if (!userId) {
      console.warn('获取文章列表：用户ID不存在')
      return
    }

    const data = {
      userId: userId
    }
    
    console.log('请求用户文章列表，参数:', data)
    
    const response = await getUserArticleList(data)
    if (response.code === 200) {
      console.log('获取用户文章列表成功:', response)
      // 更新取值方式: 使用response.rows
      userArticles.value = response.rows || []
      console.log('用户文章数据:', userArticles.value)
    } else {
      console.error('获取用户文章列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取用户文章列表出错:', error)
  }
}

// 在组件挂载时获取数据
onMounted(() => {
  // 立即设置头像，避免加载延迟
  avatarUrl.value = userStore.getAvatar;
  
  console.log('组件挂载 - userSites初始值:', userSites.value);
  
  getuserInfo() // 会在获取用户信息后调用fetchUserArticles

  // userStore.getUserInfoAction()
  getLocationInfo()
  fetchVouchers()
  fetchUserSites() // 获取用户站点列表
  
  // 检查URL参数中是否有activeTab
  const urlParams = new URLSearchParams(window.location.search)
  const tabParam = urlParams.get('activeTab')
  if (tabParam && ['profile', 'help', 'vouchers', 'payment-records', 'consult', 'likes', 'settings'].includes(tabParam)) {
    activeNav.value = tabParam
    
    // 如果跳转到likes标签页，自动加载点赞文章列表
    if (tabParam === 'likes') {
      fetchLikedArticles()
    }
  }
  
  // 如果初始导航是支付记录，则加载支付记录
  if (activeNav.value === 'payment-records') {
    fetchPaymentRecords()
  }
  
  // 添加额外的延迟检查，确保状态更新
  setTimeout(() => {
    console.log('组件挂载后1秒 - userSites:', userSites.value);
    console.log('组件挂载后1秒 - userSites长度:', userSites.value.length);
  }, 1000);
})

// 根据会员等级设置标签样式
const userTagClass = computed(() => {
  switch (userInfo.membershipLevel) {
    case '超级会员':
      return 'super-member'
    case '会员':
      return 'member'
    default:
      return 'regular-user'
  }
})

// 导航项
const navItems = [
  { id: 'profile', name: t('userInfo.nav.profile'), icon: 'i-lucide-user' },
  { id: 'help', name: t('userInfo.nav.help'), icon: 'i-lucide-help-circle' },
  { id: 'vouchers', name: t('userInfo.nav.vouchers'), icon: 'i-lucide-ticket' },
  { id: 'payment-records', name: t('userInfo.nav.paymentRecords'), icon: 'i-lucide-credit-card' },
  { id: 'consult', name: t('userInfo.nav.consult'), icon: 'i-lucide-message-square' },
  { id: 'likes', name: t('userInfo.nav.likes'), icon: 'i-lucide-heart' },
  { id: 'settings', name: t('userInfo.nav.settings'), icon: 'i-lucide-settings' }
]

// 当前激活的导航项
const activeNav = ref('profile')

// 修改导航点击处理
const handleNavClick = (itemId) => {
  activeNav.value = itemId
  // 点击我的点赞时，获取点赞列表
  if (itemId === 'likes') {
    fetchLikedArticles()
  }
}

// 获取用户点赞的文章列表
const fetchLikedArticles = async () => {
  if (!userStore.userInfo?.userId) {
    ElMessage.warning('请先登录')
    return
  }
  
  loadingLikedArticles.value = true
  
  try {
    const res = await getUserLikedArticles()
    if (res && res.code === 200 && res.data) {
      likedArticles.value = res.data || []
      console.log('获取点赞文章成功:', likedArticles.value)
      // 检查第一篇文章的图片数据结构
      if (likedArticles.value.length > 0) {
        console.log('第一篇点赞文章的数据结构:', likedArticles.value[0])
        console.log('第一篇文章的图片列表:', likedArticles.value[0].articleImgList)
      }
    } else {
      likedArticles.value = []
      ElMessage.info('暂无点赞内容')
    }
  } catch (error) {
    console.error('获取点赞文章失败:', error)
    ElMessage.error('获取点赞文章失败')
    likedArticles.value = []
  } finally {
    loadingLikedArticles.value = false
  }
}

// 获取页面标题
const getHeaderTitle = () => {
  switch (activeNav.value) {
    case 'settings':
      return t('userInfo.nav.settings');
    case 'vouchers':
      return t('userInfo.nav.vouchers');
    case 'help':
      return t('userInfo.nav.help');
    case 'payment-records':
      return t('userInfo.nav.paymentRecords');
    case 'consult':
      return t('userInfo.nav.consult');
    case 'likes':
      return t('userInfo.nav.likes');
    default:
      return t('userInfo.nav.profile');
  }
};

// 文件上传引用
const businessCardInput = ref(null)
const businessLicenseInput = ref(null)

// 触发文件上传
const triggerFileUpload = (type) => {
  if (type === 'businessCard') {
    businessCardInput.value.click()
  } else if (type === 'businessLicense') {
    businessLicenseInput.value.click()
  }
}

// 处理文件上传
const handleFileUpload = (type, event) => {
  const file = event.target.files[0]
  if (!file) return

  // 打印文件信息
  console.log('文件信息:', {
    类型: type,
    文件名: file.name,
    文件大小: `${(file.size / 1024).toFixed(2)}KB`,
    文件类型: file.type,
    最后修改时间: new Date(file.lastModified).toLocaleString()
  })

  if (type === 'businessCard') {
    userInfo.businessCard = file
    // 创建预览URL
    businessCardPreview.value = URL.createObjectURL(file)
  } else if (type === 'businessLicense') {
    userInfo.businessLicense = file
    // 创建营业执照预览URL
    businessLicensePreview.value = URL.createObjectURL(file)
  }
}

// 移除文件
const removeFile = (type) => {
  if (type === 'businessCard') {
    userInfo.businessCard = null
    businessCardPreview.value = ''
    if (businessCardInput.value) businessCardInput.value.value = ''
  } else if (type === 'businessLicense') {
    userInfo.businessLicense = null
    businessLicensePreview.value = ''
    if (businessLicenseInput.value) businessLicenseInput.value.value = ''
  }
}

// 显示名片模态框
const showBusinessCardModal = () => {
  if (businessCardPreview.value) {
    showCardModal.value = true
  }
}

// 关闭名片模态框
const closeBusinessCardModal = () => {
  showCardModal.value = false
}

// 下载名片
const downloadBusinessCard = () => {
  if (businessCardPreview.value) {
    const link = document.createElement('a')
    link.href = businessCardPreview.value
    link.download = t('userInfo.profile.downloadBusinessCard') + '.jpg'
    link.click()
  }
}

// 显示营业执照模态框
const showBusinessLicenseModal = () => {
  if (businessLicensePreview.value) {
    showLicenseModal.value = true
  }
}

// 去使用优惠券，跳转到会员中心
const goToMemberCenter = (event) => {
  router.push('/member')
  // 阻止事件冒泡，避免整个卡片的点击事件
  event.stopPropagation()
}

// 关闭营业执照模态框
const closeBusinessLicenseModal = () => {
  showLicenseModal.value = false
}

// 下载营业执照
const downloadBusinessLicense = () => {
  if (businessLicensePreview.value) {
    const link = document.createElement('a')
    link.href = businessLicensePreview.value
    link.download = t('userInfo.profile.downloadBusinessLicense') + '.jpg'
    link.click()
  }
}

// 保存个人资料
const saveProfile = async () => {
  try {
    // 确保同步业务介绍字段的值到userInfo对象中
    userInfo.introduction = businessIntroText.value || '';
    
    // 创建一个FormData对象
    const formData = new FormData();
    
    // 1. 准备 JSON 对象，并指定文件名 "user.json"
    const userData = {
      userId: userInfo.userId,
      userName: userInfo.userName, // 添加用户账号字段，满足后端校验
      nickName: userInfo.nickName,
      post: userInfo.post,
      companyName: userInfo.companyName,
      businessIntroduction: userInfo.businessIntroduction,
      // 确保业务介绍字段被包含
      introduction: userInfo.introduction || businessIntroText.value,
      phonenumber: userInfo.phonenumber,
      email: userInfo.email,
      sex: userInfo.sex,
      qq: userInfo.qq,
      wx: userInfo.wx
    }
    
    console.log('保存个人资料，包含业务介绍:', userData.introduction);
    
    const userBlob = new Blob(
      [JSON.stringify(userData)],
      { type: 'application/json' }
    )
    formData.append('user', userBlob, 'user.json')
    
    // 2. businessCard：只有 File 类型才 append
    if (userInfo.businessCard instanceof File) {
      formData.append(
        'businessCard',
        userInfo.businessCard,
        userInfo.businessCard.name
      )
    }
    
    // 3. businessLicense：同理
    if (userInfo.businessLicense instanceof File) {
      formData.append('businessLicense', userInfo.businessLicense)
    } else if (typeof userInfo.businessLicense === 'string' && userInfo.businessLicense) {
      // 只有当是非空字符串时才传入
      formData.append('businessLicense', userInfo.businessLicense)
    }
  
    const res = await updateUserInfo(formData)
      
    if (res.code === 200) {
      ElMessage.success(t('userInfo.profile.uploadSuccess'))
      // 刷新一下用户信息
      await getuserInfo() // 使用getuserInfo而不是userStore.getUserInfoAction确保业务介绍被正确刷新
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('保存个人资料失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(userInfo, {
    nickName: '',
    post: '',
    companyName: '',//
    phonenumber: '',
    email: '',
    wx: '',
    qq: '',
    businessCard: null,
    businessLicense: null
  })

  businessCardPreview.value = ''
  businessLicensePreview.value = ''

  // 重置文件输入
  if (businessCardInput.value) businessCardInput.value.value = ''
  if (businessLicenseInput.value) businessLicenseInput.value.value = ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无数据'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  
  // 判断是否需要显示时间 (主要用于支付记录)
  if (arguments.length > 1 && arguments[1] === true) {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  // 默认只返回日期部分
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 格式化日期时间 (总是显示时分秒)
const formatDateTime = (dateString) => {
  if (!dateString) return '暂无数据'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 判断券是否可用
const isVoucherValid = (voucher) => {
  // 如果传入的是数字（兼容旧的调用方式）
  if (typeof voucher === 'number') {
    return voucher > 0;
  }
  
  // 如果传入的是对象
  if (typeof voucher === 'object' && voucher !== null) {
    return voucher.isUsed === 0;
  }
  
  return false;
}

// 获取剩余天数文本
const getRemainingDaysText = (days) => {
  if (days <= 0) {
    return t('userInfo.vouchers.expired')
  } else if (days === 1) {
    return t('userInfo.vouchers.remainingDays', { days: days })
  } else {
    return t('userInfo.vouchers.remainingDays', { days: days })
  }
}

// 获取剩余天数样式
const getDaysClass = (days) => {
  if (days <= 0) {
    return 'days-expired'
  } else if (days <= 3) {
    return 'days-warning'
  } else {
    return 'days-normal'
  }
}

// 获取状态文本
const getStatusText = (voucher) => {
  if (typeof voucher === 'boolean') {
    // 兼容旧的调用方式
    return voucher ? t('userInfo.vouchers.useNow') : t('userInfo.vouchers.expired');
  }
  
  // 根据isUsed字段判断状态
  switch (voucher.isUsed) {
    case 0:
      return t('userInfo.vouchers.useNow');
    case 1:
      return t('userInfo.vouchers.used');
    case 2:
      return t('userInfo.vouchers.expired');
    default:
      return t('userInfo.vouchers.expired');
  }
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  switch (status) {
    case 0:
      return '未支付'
    case 1:
      return '已支付'
    case 2:
      return '已取消'
    case 3:
      return '已取消'
    case 4:
      return '已退款'
    default:
      return '未知状态'
  }
}

// 获取状态样式
const getStatusClass = (status) => {
  // 检查是否为对象（处理优惠券对象）
  if (typeof status === 'object' && status !== null) {
    switch (status.isUsed) {
      case 0:
        return 'status-valid'
      case 1:
        return 'status-used'
      case 2:
        return 'status-expired'
      default:
        return ''
    }
  }
  
  // 检查是否为布尔值（处理优惠券状态，兼容旧方式）
  if (typeof status === 'boolean') {
    return status ? 'status-valid' : 'status-expired'
  }
  
  // 处理数字状态码（支付记录状态）
  switch (status) {
    case 1:
      return 'status-valid'
    case 0:
      return 'status-expired'
    case 2:
    case 3:
    case 4:
      return 'status-used'
    default:
      return ''
  }
}

// 修改 Logout 函数
async function Logout() {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await logout()
    userStore.token = ''
    userStore.userInfo = {}
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/')
    ElMessage({
      message: '退出成功',
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      ElMessage({
        message: '退出失败',
        type: 'error',
        duration: 2000
      })
    }
  }
}

async function getuserInfo() {
  // 分别获取用户基本信息和会员状态
  const [response, memberResponse, openidboolenResponse] = await Promise.all([getUserInfo(), getmembertype(), getopenidboolen()])
  
  // 处理微信绑定状态
  if (openidboolenResponse && openidboolenResponse.code === 200) {
    // 如果data为true则不显示绑定微信按钮，如果为false则显示
    isWechatBound.value = openidboolenResponse.data === true;
    console.log('微信绑定状态:', isWechatBound.value ? '已绑定' : '未绑定');
  }
  
  if (response.code === 200) {
    // 添加调试日志
    // console.log('获取到的用户信息:', response.data);
    // console.log('业务介绍字段值:', {
    //   introduction: response.data.introduction,
    //   businessIntroduction: response.data.businessIntroduction
    // });

    // 使用 userStore.getAvatar 获取头像 URL
    avatarUrl.value = userStore.getAvatar;
    userInfo.userName = response.data.userName || '' // 添加userName字段
    userInfo.nickName = response.data.nickName || response.data.userName || ''
    userInfo.phonenumber = response.data.phonenumber === 'null' ? '' : response.data.phonenumber || ''
    userInfo.qq = response.data.qq === 'null' ? '' : response.data.qq || ''
    userInfo.wx = response.data.wx === 'null' ? '' : response.data.wx || ''
    userInfo.post = response.data.post === 'null' ? '' : response.data.post || ''
    userInfo.email = response.data.email === 'null' ? '' : response.data.email || ''
    userInfo.companyName = response.data.companyName === 'null' ? '' : response.data.companyName || ''
    userInfo.name = response.data.name || ''
    userInfo.sex = response.data.sex || ''
    userInfo.roleGroup = response.data.roleGroup || ''
    userInfo.userId = response.data.userId
    
    // 统一从 getmembertype 接口获取会员状态
    if (memberResponse && memberResponse.code === 200) {
      const memberType = memberResponse.data
      userInfo.memberType = memberType
      
      // 保存会员状态到状态管理
      userStore.setMemberType(memberType)
      
      // 保存会员状态到本地存储中
      const userInfoFromStorage = JSON.parse(localStorage.getItem('userInfo') || '{}')
      userInfoFromStorage.memberType = memberType
      localStorage.setItem('userInfo', JSON.stringify(userInfoFromStorage))
    } else {
      userInfo.memberType = '普通会员' // 默认值
      console.warn('获取会员状态失败:', memberResponse?.message)
    }
    
    // 用户信息加载完成后获取文章列表
    fetchUserArticles()

    // 处理业务介绍字段，优先使用introduction字段
    const introContent = response.data.introduction || response.data.businessIntroduction;
    businessIntroText.value = introContent === 'null' ? '' : introContent || '';
    originalBusinessIntro.value = businessIntroText.value;
    console.log('设置业务介绍文本为:', businessIntroText.value);

    // 处理名片URL
    if (response.data.businessCard) {
      userInfo.businessCard = response.data.businessCard
      businessCardPreview.value = import.meta.env.VITE_BASE_API + response.data.businessCard
    }

    // 处理营业执照URL
    if (response.data.businessLicense) {
      userInfo.businessLicense = response.data.businessLicense
      businessLicensePreview.value = import.meta.env.VITE_BASE_API + response.data.businessLicense
    }
  } else {
    console.error('获取用户信息失败:', response.message)
  }
}

// 获取抵值券数据
async function fetchVouchers() {
  try {
    // 检查用户是否登录
    if (!userStore.token || !userStore.userInfo || !userStore.userInfo.userId) {
      ElMessage.warning(t('userInfo.vouchers.loginRequired'))
      return
    }

    const userId = userStore.userInfo.userId
    console.log(userStore.userInfo);

    const response = await getCouponList(userId)
    if (response.code === 200) {
      console.log(response);

      // 获取所有抵值券
      const allVouchers = response.data || []
      
      // 按可用状态排序：可用的(isUsed=0)放前面，然后是已使用的(isUsed=1)，最后是已过期的(isUsed=2)
      // 对于每种状态内部，按金额从大到小排序
      allVouchers.sort((a, b) => {
        // 首先按照isUsed字段排序
        if (a.isUsed !== b.isUsed) {
          return a.isUsed - b.isUsed // 0排前面，2排后面
        }
        // 其次按照金额排序（从大到小）
        return b.amount - a.amount
      })
      
      vouchers.value = allVouchers
    } else {
      console.error('获取抵值券失败:', response.message)
    }
  } catch (error) {
    console.error('获取抵值券出错:', error)
  }
}

// 获取用户IP地址和位置
async function getLocationInfo() {
  try {
    // 先获取IP地址
    const ipResponse = await fetch('https://api64.ipify.org?format=json')
    const { ip } = await ipResponse.json()

    // 使用IP获取位置信息
    const locationResponse = await fetch(`https://api.vore.top/api/IPdata?ip=${ip}`)
    const locationData = await locationResponse.json()

    if (locationData.code === 200 && locationData.ipdata) {
      location.value = {
        province: locationData.ipdata.info1 || '未知省份',
        city: locationData.ipdata.info2 || '未知城市'
      }

      // 更新用户信息中的位置
      userInfo.location = `${location.value.province}-${location.value.city}`
    }
  } catch (error) {
    console.error('获取位置信息失败:', error)
    location.value = {
      province: '未知省份',
      city: '未知城市'
    }
    userInfo.location = '位置获取失败'
  }
}

// 头像相关逻辑
const showAvatarModal = () => {
  showAvatar.value = true
}

const closeAvatarModal = () => {
  showAvatar.value = false
}

const triggerAvatarUpload = () => {
  avatarInput.value.click()
}

const handleAvatarUpload = async (event) => {
  const file = event.target.files[0]

  if (!file) return

  const formData = new FormData()
  
  // 创建用户数据对象并转为JSON Blob（可以添加userId等信息）
  const userData = {
    userId: userInfo.userId,
    userName: userInfo.userName // 添加用户账号字段，满足后端校验
  };
  
  // 将JSON数据添加到FormData，并指定文件名
  const userBlob = new Blob(
    [JSON.stringify(userData)],
    { type: 'application/json' }
  );
  formData.append('user', userBlob, 'user.json');
  
  // 添加头像文件（带文件名）
  formData.append('avatarfile', file, file.name)

  try {
    const response = await uploadAvatar(formData)
    if (response.code === 200) {
      // 更新本地头像URL
      avatarUrl.value = import.meta.env.VITE_BASE_API + response.imgUrl

      ElMessage.success('头像上传成功')
      closeAvatarModal()
      
      // 更新本地存储中userInfo的avatar并刷新用户信息
      const updatedUserInfo = {
        ...userStore.userInfo,
        avatar: response.imgUrl
      }
      
      localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo))
      userStore.setUserInfo(updatedUserInfo)
    } else {
      ElMessage.error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }

  // 清空文件输入以允许重复上传相同文件
  event.target.value = ''
}

// 格式化业务介绍文本，将换行符转换为<br>标签
const formattedBusinessIntro = computed(() => {
  if (!businessIntroText.value) {
    return t('userInfo.businessIntro.emptyText');
  }
  return businessIntroText.value.replace(/\n/g, '<br>');
});

// 更新字符计数
const updateCharCount = () => {
  charCount.value = businessIntroText.value.length;
};

// 开始编辑业务介绍
const startEdit = () => {
  originalBusinessIntro.value = businessIntroText.value;
  isEditing.value = true;
  // 初始化字符计数
  charCount.value = businessIntroText.value.length;
};

// 取消编辑
const cancelEdit = () => {
  businessIntroText.value = originalBusinessIntro.value;
  isEditing.value = false;
};

// 保存业务介绍
const saveBusinessIntro = async () => {
  try {
    // 创建FormData对象
    const formData = new FormData();
    
    // 创建完整的用户数据对象，保留所有现有字段
    const userData = {
      // 首先复制当前用户信息中的所有字段
      ...userInfo,
      // 然后只更新业务介绍字段
      introduction: businessIntroText.value,
      // 确保包含必要的ID字段
      userId: userStore.userInfo.userId,
      userName: userInfo.userName
    };

    console.log('准备发送业务介绍数据:', userData);
    
    // 将完整的用户数据对象添加到FormData，并指定文件名
    const userBlob = new Blob(
      [JSON.stringify(userData)],
      { type: 'application/json' }
    );
    formData.append('user', userBlob, 'user.json');
    
    // 调用更新用户信息API
    const res = await updateUserInfo(formData)
    console.log('业务介绍保存响应:', res);

    if (res.code === 200) {
      ElMessage({
        message: '业务介绍保存成功',
        type: 'success',
        duration: 2000
      });

      // 保存成功后更新原始值
      originalBusinessIntro.value = businessIntroText.value;
      isEditing.value = false;

      // 刷新用户信息
      await getuserInfo();
    } else {
      ElMessage({
        message: res.msg || '保存失败，请重试',
        type: 'error',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('保存业务介绍失败:', error);
    ElMessage({
      message: '保存失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

// 添加返回地图的逻辑
const backToMap = () => {
  // 实现返回地图的逻辑
  router.push('/')
};

// 编辑咨询
const editConsult = (id) => {
  console.log('编辑咨询', id);
  // 跳转到创建/编辑文章页面，并传递文章ID和来源
  router.push(`/create-post?id=${id}&from=userinfo`);
};

// 删除咨询
const deleteConsult = (id) => {
  console.log('准备删除文章', id);
  
  // 使用美化的确认对话框
  ElMessageBox.confirm(
    '<div class="delete-confirm-content">' +
      '<div class="delete-icon-container">' +
        '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="delete-icon"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>' +
      '</div>' +
      '<h3 class="delete-title">' + t('userInfo.consult.deleteConfirm.title') + '</h3>' +
      '<p class="delete-desc">' + t('userInfo.consult.deleteConfirm.content') + '</p>' +
    '</div>',
    {
      title: t('userInfo.consult.deleteConfirm.title'),
      confirmButtonText: t('userInfo.consult.deleteConfirm.confirm'),
      cancelButtonText: t('userInfo.consult.deleteConfirm.cancel'),
      type: 'warning',
      dangerouslyUseHTMLString: true, // 允许使用HTML字符串
      confirmButtonClass: 'el-button--danger',
      center: true,
      roundButton: true
    }
  ).then(async () => {
    try {
      // 调用API删除文章
      const response = await deleteArticle(id, userInfo.userId);
      
      if (response.code === 200) {
        // 更新本地数据，从列表中移除该文章
        userArticles.value = userArticles.value.filter(item => item.id !== id);
        
        ElMessage({
          type: 'success',
          message: response.msg || '文章删除成功',
          duration: 2000
        });
      } else {
        throw new Error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除文章失败:', error);
      ElMessage({
        type: 'error',
        message: `删除失败: ${error.message || '请重试'}`,
        duration: 3000
      });
    }
  }).catch(() => {
    // 用户取消删除
    ElMessage({
      type: 'info',
      message: t('userInfo.consult.deleteConfirm.cancel'),
      duration: 1500
    });
  });
};

// 检查文章是否有图片
const hasArticleImages = (articleId) => {
  const article = userArticles.value.find(a => a.id === articleId);
  return article && article.articleImgList && article.articleImgList.length > 0;
};

// 获取文章的第一张图片
const getFirstArticleImage = (articleId) => {
  const article = userArticles.value.find(a => a.id === articleId);
  if (article && article.articleImgList && article.articleImgList.length > 0) {
    return import.meta.env.VITE_BASE_API + article.articleImgList[0].imgUrl;
  }
  return ''; // 默认返回空字符串
};

// 已在前面定义过函数，此处不需要重复定义

// 处理图片加载错误
const handleImageError = (e) => {
  console.log('图片加载失败，原始URL:', e.target.src);
  // 设置默认图片（使用新的logo作为默认图片）
  e.target.src = '/image/newMaptitlelogo.png';
  // 防止循环触发error事件
  e.target.onerror = null;
  console.log('已设置默认图片');
};


// 支付记录相关
const paymentRecords = ref([])
const paymentRecordsLoading = ref(false)
// 分页相关
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})
// 搜索相关
const searchParams = ref({
  orderNo: '',
  status: ''
})
// 支付详情模态窗口
const showPaymentDetailModal = ref(false)
const currentOrderDetail = ref(null)

// 搜索处理
const handleSearch = () => {
  pagination.value.current = 1 // 搜索时重置为第一页
  fetchPaymentRecords()
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    orderNo: '',
    status: ''
  }
  pagination.value.current = 1
  fetchPaymentRecords()
}

// 获取支付记录
const fetchPaymentRecords = async () => {
  if (!userInfo.userId) return
  
  paymentRecordsLoading.value = true
  try {
    // 按照后端需要的参数格式传递
    const params = {
      userId: userInfo.userId,
      // 可选参数，根据搜索条件添加
      orderNo: searchParams.value.orderNo || undefined,
      status: searchParams.value.status || undefined,
      
      // 添加分页参数 - 固定为10条/页
      pageNum: pagination.value.current,
      pageSize: 10
    }
    
    const res = await getUserPaymentRecords(params)
    if (res.code === 200) {
      // 后端返回的是分页数据，使用正确的属性
      paymentRecords.value = res.rows || []
      // 更新分页信息，确保当前页正确设置
      pagination.value = {
        current: Number(res.pageNum) || 1,
        total: res.total || 0
      }
      console.log('当前页码:', pagination.value.current, '总记录数:', pagination.value.total)
    } else {
      ElMessage.error(res.msg || '获取支付记录失败')
    }
  } catch (error) {
    console.error('获取支付记录失败:', error)
    ElMessage.error('获取支付记录失败，请稍后重试')
  } finally {
    paymentRecordsLoading.value = false
  }
}

// 分页变化处理
const handlePageChange = (page) => {
  console.log('页码变化:', page)
  pagination.value.current = Number(page)
  fetchPaymentRecords()
}

// 不再需要每页条数变化处理，因为固定为10条/页

// 监听导航变化，当切换到支付记录页时加载数据
watch(activeNav, (newVal) => {
  if (newVal === 'payment-records') {
    fetchPaymentRecords()
  }
})

// 监听搜索参数变化，确保分页状态正确
watch(() => [searchParams.value.orderNo, searchParams.value.status], () => {
  if (activeNav.value === 'payment-records') {
    pagination.value.current = 1
  }
}, { deep: true })

// 我们将使用已存在的formatDate和getStatusClass函数

// 查看支付详情
const viewPaymentDetail = (record) => {
  currentOrderDetail.value = record
  showPaymentDetailModal.value = true
}

// 关闭支付详情模态窗口
const closePaymentDetailModal = () => {
  showPaymentDetailModal.value = false
}

// 取消订单
const cancelOrder = async () => {
  if (!currentOrderDetail.value) return
  
  try {
    const stationId = localStorage.getItem('stationId')
    const orderNo = currentOrderDetail.value.orderNo
    
    if (!stationId) {
      ElMessage.error('无法获取站点信息，请重试')
      return
    }
    
    const confirmResult = await ElMessageBox.confirm(
      '确定要取消该订单吗？此操作不可逆。',
      '取消订单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (confirmResult === 'confirm') {
      const res = await putinvalidateOrder(orderNo, stationId)
      
      if (res.code === 200) {
        ElMessage.success('订单已取消')
        // 更新订单状态为已取消
        currentOrderDetail.value.status = 2 // 设置为已取消状态
        // 刷新订单列表
        fetchPaymentRecords()
      } else {
        ElMessage.error(res.msg || '取消订单失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败，请稍后重试')
    }
  }
}

// 处理添加站点
const handleAddSite = () => {
  // 判断用户是否是会员
  const isMember = userInfo.memberType === '普通会员' || userInfo.memberType === '超级会员';
  
  if (isMember) {
    // 如果是会员，跳转到会员页面并传递参数让其自动打开站点选择弹窗
    router.push('/member?showStationSelector=true');
  } else {
    // 如果不是会员，直接跳转到会员页面
    router.push('/member');
  }
}

// 获取用户站点列表
const fetchUserSites = async () => {
  try {
    console.log('开始获取用户站点信息...')
    const response = await getUserMemberInfo()
    
    console.log('获取用户站点信息完整响应:', response)
    console.log('userSites当前值:', userSites.value)

    if (response.code === 200) {
      console.log('获取用户会员信息成功:', response.data)

      // 根据实际API响应格式处理数据
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        console.log('原始站点数据:', response.data)
        
        // 先将站点数据转换为所需格式
        const mappedSites = response.data.map((station, index) => {
          console.log(`处理站点 ${index + 1}:`, {
            id: station.id,
            stationZh: station.stationZh,
            stationEn: station.stationEn,
            memberType: station.memberType
          })
          return {
            id: station.id,
            name: station.stationZh, // 使用中文站点名称
            nameEn: station.stationEn, // 保存英文名称
            status: 'active',
            memberType: station.memberType,
            endTime: station.endTime
          }
        })
        
        // 执行去重操作：当中文名和英文名都相同时，只保留一个
        const uniqueMap = new Map()
        const uniqueSites = []
        
        mappedSites.forEach(site => {
          // 创建唯一键 - 中文名+英文名
          const key = `${site.name}_${site.nameEn}`
          
          // 如果这个组合还没有出现过，则添加到结果中.还有就是如果站点名为null也不添加
          if (!uniqueMap.has(key) && site.name !== null) {
            uniqueMap.set(key, true)
            uniqueSites.push(site)
          } else {
            console.log(`发现重复站点: ${site.name}(${site.nameEn})，已忽略`)
          }
        })
        
        userSites.value = uniqueSites
        
        console.log('去重后的站点列表:', userSites.value)
        console.log(`原始站点数量: ${mappedSites.length}, 去重后站点数量: ${userSites.value.length}`)
      } else {
        // 如果没有站点信息，设置为空数组
        userSites.value = []
        console.log('用户暂无开通的站点')
        console.log('userSites值设置为空数组:', userSites.value)
      }
    } else {
      console.error('获取用户站点列表失败:', response.msg)
      userSites.value = []
      console.log('API响应错误，userSites值设置为空数组:', userSites.value)
    }
  } catch (error) {
    console.error('获取用户站点列表失败:', error)
    userSites.value = []
    console.log('获取站点信息出错，userSites值设置为空数组:', userSites.value)
    // 不显示错误提示，因为站点信息不是必需的
  }
}

// 取消点赞文章
const unlikeArticle = async (article) => {
  try {
    // 显示加载中提示
    ElMessage.info({
      message: '正在取消点赞...',
      duration: 1000
    });

    // 调用取消点赞API
    const res = await deleteArticleLike(article.id);

    if (res && res.code === 200) {
      ElMessage.success('已取消点赞');

      // 重新获取点赞列表数据，确保数据同步
      await fetchLikedArticles();

    } else {
      ElMessage.error(res?.msg || '取消点赞失败');
    }
  } catch (error) {
    console.error('取消点赞出错:', error);
    ElMessage.error('取消点赞失败，请稍后再试');
  }
}

// 添加微信绑定相关状态
const showBindWechatModal = ref(false)
const qrUrl = ref('')
const wechatStatusMsg = ref('请使用微信扫码绑定')
const wxScanInterval = ref(null)
const wxState = ref('')
const isWechatBound = ref(false) // 控制是否显示微信绑定按钮

// 打开绑定微信模态框
const openBindWechatModal = async () => {
  showBindWechatModal.value = true
  initWechatQrCode()
}

// 关闭绑定微信模态框
const closeBindWechatModal = () => {
  showBindWechatModal.value = false
  qrUrl.value = ''
  
  // 清除扫描状态检查定时器
  if (wxScanInterval.value) {
    clearInterval(wxScanInterval.value)
  }
}

// 初始化微信二维码
const initWechatQrCode = async () => {
  try {
    wechatStatusMsg.value = t('userInfo.profile.wechatBinding.loadingQrCode');
    
    // 获取当前用户token，用于绑定操作
    const token = localStorage.getItem('token');
    if (!token) {
      ElMessage.error(t('userInfo.profile.wechatBinding.loginRequired'));
      wechatStatusMsg.value = t('userInfo.profile.wechatBinding.loginRequired');
      return;
    }
    
    // 创建state参数，包含token信息
    // const token =  token;
    
    // 调用后端获取二维码，传递state参数
    console.log('到底huohuo去好的撒');
    
    const res = await getQrCodeByState(token);
    
    if (res.code === 200) {
      wxState.value = res.data.state;
      qrUrl.value = res.data.url; // 使用后端返回的完整授权URL
      console.log("微信登录URL:", res.data.url);
      wechatStatusMsg.value = t('userInfo.profile.wechatBinding.scanInstruction');
      
      // 确保iframe在加载时正确显示
      setTimeout(() => {
        const iframe = document.querySelector('.wx-login-frame');
        if (iframe) {
          iframe.style.visibility = 'visible';
          // 根据屏幕宽度设置不同的尺寸
          if (window.innerWidth <= 480) {
            iframe.style.width = '320px';
            iframe.style.height = '320px';
          } else {
            iframe.style.width = '340px';
            iframe.style.height = '340px';
          }
        }
      }, 200);
      
      // 启动轮询检查扫码状态
      // startScanStatusCheck();
    }
  } catch (error) {
          wechatStatusMsg.value = t('userInfo.profile.wechatBinding.qrCodeFailed');
    console.error("获取二维码失败:", error);
  }
};

// 开始检查扫码状态
const startScanStatusCheck = () => {
  // 清除可能存在的旧定时器
  if (wxScanInterval.value) {
    clearInterval(wxScanInterval.value);
  }
  
  // 设置新定时器
  wxScanInterval.value = setInterval(async () => {
    try {
      if (!wxState.value) return;
      
      const res = await checkScanStatus(wxState.value);
      console.log("扫码状态:", res);
      
      if (res.code === 200) {
        // 处理不同的返回格式
        if (res.data && res.data.status) {
          // 格式1: {data: {status: 'xxx', openid: 'xxx'}}
          if (res.data.status === 'scanned') {
            wechatStatusMsg.value = t('userInfo.profile.wechatBinding.scanSuccess');
          } else if (res.data.status === 'confirmed') {
            wechatStatusMsg.value = t('userInfo.profile.wechatBinding.authSuccess');
            clearInterval(wxScanInterval.value);
            
            // 处理绑定
            handleWechatBind(res.data.openid);
          } else if (res.data.status === 'expired') {
            wechatStatusMsg.value = t('userInfo.profile.wechatBinding.qrExpired');
            clearInterval(wxScanInterval.value);
          } else if (res.data.status === 'bound') {
            // 绑定成功
            wechatStatusMsg.value = t('userInfo.profile.wechatBinding.bindingComplete');
            clearInterval(wxScanInterval.value);
            
            // 更新用户信息
            ElMessage.success('微信绑定成功');
            setTimeout(() => {
              closeBindWechatModal();
              getuserInfo(); // 刷新用户信息
            }, 1500);
          }
        } else if (res.openid) {
          // 格式2: {openid: 'xxx'}
          wechatStatusMsg.value = t('userInfo.profile.wechatBinding.authSuccess');
          clearInterval(wxScanInterval.value);
          handleWechatBind(res.openid);
        } else if (res.status === true) {
          // 格式3: {status: true} - 后端直接处理了绑定
          wechatStatusMsg.value = t('userInfo.profile.wechatBinding.bindingComplete');
          clearInterval(wxScanInterval.value);
          
          ElMessage.success('微信绑定成功');
          setTimeout(() => {
            closeBindWechatModal();
            getuserInfo(); // 刷新用户信息
          }, 1500);
        }
      }
    } catch (error) {
      console.error("检查扫码状态错误:", error);
    }
  }, 2000); // 每2秒检查一次
};

// 处理微信绑定
const handleWechatBind = async (openid) => {
  if (!openid) {
    ElMessage.error('绑定失败：未获取到微信信息');
    return;
  }
  
  try {
    wechatStatusMsg.value = t('userInfo.profile.wechatBinding.binding');
    
    // 获取当前用户token
    const token = localStorage.getItem('token');
    if (!token) {
      ElMessage.error(t('userInfo.profile.wechatBinding.loginRequired'));
      wechatStatusMsg.value = t('userInfo.profile.wechatBinding.loginFailure');
      return;
    }
    
    // 方法1: 通过表单提交绑定
    // 创建一个FormData对象
    const formData = new FormData();
    
    // 创建用户数据对象并转为JSON Blob
    const userData = {
      userId: userInfo.userId,
      userName: userInfo.userName, // 添加用户账号字段，满足后端校验
      wx: openid, // 设置微信openid到wx字段
      token: token // 添加token
    };
    
    console.log("准备更新用户信息:", userData);
    
    // 将JSON数据添加到FormData，并指定文件名
    const userBlob = new Blob(
      [JSON.stringify(userData)],
      { type: 'application/json' }
    );
    formData.append('user', userBlob, 'user.json');
    
    // 调用更新用户信息API
    const res = await updateUserInfo(formData);
    
    if (res.code === 200) {
      ElMessage({
        message: t('userInfo.profile.wechatBinding.bindSuccess'),
        type: 'success',
        duration: 3000
      });
      
      // 更新表单中的wx字段
      userInfo.wx = openid;
      
      // 关闭模态框
      setTimeout(() => {
        closeBindWechatModal();
        // 刷新用户信息
        getuserInfo();
      }, 1000);
    } else {
      ElMessage.error(res.msg || t('userInfo.profile.wechatBinding.bindFailed'));
      wechatStatusMsg.value = t('userInfo.profile.wechatBinding.bindFailed');
    }
  } catch (error) {
    console.error('微信绑定失败:', error);
    ElMessage.error(t('userInfo.profile.wechatBinding.bindFailed'));
    wechatStatusMsg.value = t('userInfo.profile.wechatBinding.bindFailed');
  }
};

// 处理iframe加载完成事件
const handleIframeLoad = () => {
  console.log('微信登录iframe加载完成');
  wechatStatusMsg.value = t('userInfo.profile.wechatBinding.scanInstruction');
};
</script>

<style scoped>
/* 保留原有样式 */
.personal-center {
  max-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  overflow-x: hidden;
}

.content-container {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  gap: 24px;
  min-height: 100vh;
  overflow: visible;
}

.sidebar {
  width: 260px;
  position: sticky;
  top: 20px;
  align-self: flex-start;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: visible;
}

.content-body {
  flex: 1;
  overflow: visible;
}

.profile-card,
.vouchers-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #eaedf0;
  margin-bottom: 20px;
}

.avatar-container {
  position: relative;
  margin-bottom: 16px;
  display: inline-block;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-tag {
  position: absolute;
  bottom: -5px;
  right: -5px;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-tag.regular-user {
  background-color: #64748b;
}

.user-tag.member {
  background-color: #7EE8BA;
}

.user-tag.super-member {
  background-color: #FFD700;
}

.username {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 8px 0 4px;
  color: #1e293b;
}

.user-location {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #64748b;
}

.user-location .icon {
  width: 14px;
  height: 14px;
  margin-right: -15px;
}

.navigation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: #f1f5f9;
}

.nav-item.active {
  background: #f1f5f9;
  font-weight: 500;
}

.nav-icon {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  color: #64748b;
}

.nav-item.active .nav-icon {
  color: #0f172a;
}

.nav-text {
  font-size: 0.9rem;
  color: #334155;
}

.sidebar-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eaedf0;
}

.logout-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  background: #fee2e2;
  border: none;
  color: #ef4444;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: #fecaca;
}

.logout-btn .icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.content-header h1 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f1f5f9;
}

.action-btn .icon {
  width: 18px;
  height: 18px;
  color: #64748b;
}

.card-header {
  margin-bottom: 24px;
}

.card-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1e293b;
}

.card-header p {
  color: #64748b;
  font-size: 0.875rem;
}

.form-section {
  margin-bottom: 28px;
}

.section-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #eaedf0;
  padding-bottom: 8px;
}

.section-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.form-group {
  position: relative;
}

.form-group.full-width {
  grid-column: span 2;
}

label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 6px;
  color: #475569;
}

.input-container {
  position: relative;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #94a3b8;
  z-index: 1;
}

.wechat-btn-container {
  width: 40%;
}

.bind-wechat-btn-full {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 15px;
  background: #07C160;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bind-wechat-btn-full:hover {
  background: #06AD56;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

.bind-wechat-btn-full .icon {
  width: 18px;
  height: 18px;
}

/* 调整姓名输入框及其标签的垂直位置，使其与其他输入框对齐 */
.name-field {
  margin-top: 12px;
}

input,
textarea {
  width: 100%;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 10px 10px 10px 34px;
  color: #334155;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

textarea {
  padding: 10px;
  resize: none;
}

input:focus,
textarea:focus {
  outline: none;
  border-color: #94a3b8;
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.1);
}

input::placeholder,
textarea::placeholder {
  color: #cbd5e1;
}

.textarea-container {
  position: relative;
  width: 100%;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 名片上传样式 - 按照标准名片比例 (85.6mm x 54mm ≈ 1.58:1) */
.business-card-upload {
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  aspect-ratio: 1.58;
  /* 标准名片比例 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.business-card-upload:hover {
  background: #f1f5f9;
  border-color: #94a3b8;
}

.business-card-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.business-card-preview:hover .card-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.view-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.remove-card {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-card:hover {
  background: rgba(239, 68, 68, 1);
}

.remove-card .icon {
  width: 14px;
  height: 14px;
  color: white;
}

/* 营业执照上传样式 - 按照营业执照比例 (A4横向 ≈ 1.41:1) */
.business-license-upload {
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  aspect-ratio: 1.41;
  /* 营业执照横向比例 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.business-license-upload:hover {
  background: #f1f5f9;
  border-color: #94a3b8;
}

.business-license-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.license-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.license-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.business-license-preview:hover .license-overlay {
  opacity: 1;
}

.remove-license {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-license:hover {
  background: rgba(239, 68, 68, 1);
}

.remove-license .icon {
  width: 14px;
  height: 14px;
  color: white;
}

.upload-card {
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 6px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-card:hover {
  background: #f1f5f9;
  border-color: #94a3b8;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.upload-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.upload-icon .icon {
  width: 20px;
  height: 20px;
  color: #64748b;
}

.upload-text h4 {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  color: #334155;
}

.upload-text p {
  font-size: 0.75rem;
  color: #94a3b8;
}

.upload-complete {
  display: flex;
  align-items: center;
  width: 100%;
}

.file-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  background: #f0fdf4;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-icon .icon {
  width: 18px;
  height: 18px;
  color: #22c55e;
}

.file-info {
  flex: 1;
  overflow: hidden;
}

.file-info h4 {
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 2px;
  color: #22c55e;
}

.file-info p {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-file {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #fee2e2;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-file:hover {
  background: #fecaca;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn-reset,
.btn-save {
  padding: 12px 36px;
  font-size: 1.1rem;
  border-radius: 6px;
  min-width: 120px;
  text-align: center;
  justify-content: center;
}

.btn-reset {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.btn-reset:hover {
  background: #f1f5f9;
}

.btn-save {
  background: #1890ff;
  border: none;
  color: #ffffff;
}

.btn-save:hover {
  background: #40a9ff;
}

.btn-reset .icon,
.btn-save .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.hidden {
  display: none;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 10px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.modal-close {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f1f5f9;
}

.modal-close .icon {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.modal-body {
  padding: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

.modal-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modal-footer {
  padding: 10px 15px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
}

.btn-download {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: #0f172a;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-download:hover {
  background: #1e293b;
}

.btn-download .icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* 抵值券样式 */
.vouchers-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.empty-vouchers {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon .icon {
  width: 30px;
  height: 30px;
  color: #94a3b8;
}

.empty-vouchers h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #334155;
}

.empty-vouchers p {
  color: #64748b;
  font-size: 0.9rem;
}

.vouchers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.voucher-card {
  display: flex;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 16px;
}

.voucher-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.voucher-expired {
  opacity: 0.7;
  filter: grayscale(0.7);
}

.voucher-used {
  opacity: 0.8;
  filter: grayscale(0.3);
}

.voucher-available {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* 添加丝带标记样式 */
.voucher-ribbon {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.available-voucher-label {
  display: inline-block;
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.use-voucher-btn {
  display: inline-block;
  background-color: #6366f1;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.use-voucher-btn:hover {
  background-color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.voucher-left {
  width: 120px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
}

.voucher-amount {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.currency {
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 4px;
}

.value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.voucher-threshold {
  font-size: 0.8rem;
  opacity: 0.9;
}

.voucher-divider {
  position: relative;
  width: 20px;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.circle {
  width: 20px;
  height: 20px;
  background: #f5f7fa;
  border-radius: 50%;
  position: absolute;
}

.circle.top {
  top: -10px;
}

.circle.bottom {
  bottom: -10px;
}

.dashed-line {
  flex: 1;
  width: 0;
  border-left: 2px dashed #e2e8f0;
  margin: 10px 0;
}

.voucher-use-btn {
  background-color: #fff;
  color: #ff6b6b;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-top: 10px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.voucher-use-btn:hover {
  background-color: #f8f8f8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.voucher-right {
  flex: 1;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.voucher-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

.voucher-validity {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.validity-date,
.validity-days {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #64748b;
}

.validity-date .icon,
.validity-days .icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}

.days-warning {
  color: #f59e0b;
  font-weight: 500;
}

.days-expired {
  color: #ef4444;
  font-weight: 500;
}

.days-normal {
  color: #64748b;
}

.voucher-status {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-valid {
  background-color: #ecfdf5;
  color: #10b981;
}

.status-used {
  background-color: #f1f5f9;
  color: #64748b;
}

.status-expired {
  background-color: #fef2f2;
  color: #ef4444;
}

/* 头像相关样式 */
.avatar-container {
  position: relative;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  color: white;
  font-size: 0.8rem;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.avatar-modal {
  max-width: 400px;
  width: 90%;
}

.avatar-preview-container {
  position: relative;
  display: inline-block;
}

.avatar-preview {
  max-width: 300px;
  max-height: 300px;
  width: 100%;
  border-radius: 8px;
  object-fit: cover;
}

.modal-vip-badge {
  bottom: 15px;
  right: -15px;
  font-size: 12px;
  padding: 3px 8px;
  min-width: 30px;
  border: none; /* 移除边框 */
}

.btn-upload {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: #0f172a;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-upload:hover {
  background: #1e293b;
}

.btn-upload .icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
    padding: 15px;
  }

  .sidebar {
    position: relative;
    width: 100%;
    height: auto;
    margin-bottom: 20px;
    top: 0;
    max-height: none;
  }

  .voucher-card {
    flex-direction: column;
  }

  .voucher-left {
    width: 100%;
    padding: 15px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .voucher-use-btn {
    margin-top: 0;
    margin-left: 10px;
  }

  .voucher-divider {
    display: none;
  }
}

@media (max-width: 768px) {
  .content-container {
    padding: 12px;
  }

  .sidebar,
  .main-content {
    border-radius: 6px;
  }

  .form-grid,
  .upload-grid {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .voucher-validity {
    flex-direction: column;
  }
  
  /* Responsive WeChat binding section */
  .wechat-binding-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .bind-wechat-btn {
    width: 100%;
    justify-content: center;
  }
  
  .binding-text p {
    max-width: 100%;
  }

  .modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }

  .modal-image {
    max-height: 50vh;
  }

  .content-header {
    padding: 12px 16px;
  }

  .content-header h1 {
    font-size: 1.2rem;
  }

  .card-header h2 {
    font-size: 1.1rem;
  }

  .profile-card,
  .vouchers-container,
  .business-intro-container {
    padding: 16px;
  }

  .consult-header {
    padding: 12px 16px;
  }

  .consult-content {
    padding: 12px 16px;
  }

  .consult-actions {
    padding: 10px 16px;
  }

  .consult-title {
    font-size: 1rem;
  }

  .content-preview {
    font-size: 0.9rem;
    max-height: 80px;
  }

  .intro-text-container {
    height: 200px;
  }

  .intro-textarea {
    height: 150px;
  }

  .btn-edit,
  .btn-save,
  .btn-cancel {
    padding: 10px 24px;
    font-size: 1rem;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 10px;
  }

  .sidebar {
    padding: 16px;
  }

  .profile-section {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }

  .avatar {
    width: 70px;
    height: 70px;
  }
  
  .vip-badge {
    font-size: 8px;
    padding: 2px 4px;
    min-width: 20px;
    bottom: -3px;
    right: -3px;
  }

  .username {
    font-size: 1rem;
  }

  .nav-item {
    padding: 8px 10px;
  }

  .nav-icon {
    margin-right: 10px;
  }

  .nav-text {
    font-size: 0.85rem;
  }

  .back-to-map {
    padding: 8px 10px;
    font-size: 0.9rem;
  }

  .back-icon {
    width: 16px;
    height: 16px;
  }

  .content-header {
    padding: 10px 14px;
    margin-bottom: 16px;
  }

  .content-header h1 {
    font-size: 1.1rem;
  }

  .action-btn {
    width: 32px;
    height: 32px;
  }

  .card-header {
    margin-bottom: 16px;
  }

  .card-header h2 {
    font-size: 1rem;
  }

  .card-header p {
    font-size: 0.8rem;
  }

  .section-header h3 {
    font-size: 0.9rem;
  }

  label {
    font-size: 0.8rem;
    margin-bottom: 4px;
  }

  input,
  textarea {
    padding: 8px 8px 8px 30px;
    font-size: 0.85rem;
  }

  .input-icon {
    left: 8px;
    width: 14px;
    height: 14px;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
    margin-top: 16px;
  }

  .btn-reset,
  .btn-save {
    width: 100%;
    padding: 10px;
    font-size: 0.95rem;
    min-width: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .voucher-right {
    padding: 12px;
  }

  .voucher-name {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .validity-date,
  .validity-days {
    font-size: 0.75rem;
  }

  .voucher-amount .value {
    font-size: 2rem;
  }

  .voucher-amount .currency {
    font-size: 1rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
  }

  .empty-icon {
    width: 50px;
    height: 50px;
  }

  .empty-icon .icon {
    width: 25px;
    height: 25px;
  }

  .empty-vouchers h3,
  .empty-consult h3 {
    font-size: 1rem;
  }

  .empty-vouchers p,
  .empty-consult p {
    font-size: 0.8rem;
  }

  .btn-edit-consult,
  .btn-delete-consult {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .intro-text-container {
    height: 180px;
    padding: 15px;
  }

  .intro-textarea {
    height: 130px;
  }

  .intro-display {
    font-size: 0.9rem;
  }

  .char-count {
    font-size: 0.75rem;
    bottom: 8px;
    right: 15px;
  }

  .btn-edit,
  .btn-save,
  .btn-cancel {
    padding: 8px 20px;
    font-size: 0.9rem;
    min-width: 80px;
  }

  .modal-header {
    padding: 15px;
  }

  .modal-header h3 {
    font-size: 1.1rem;
  }

  .modal-body {
    padding: 15px;
  }

  .modal-footer {
    padding: 12px 15px;
  }

  .btn-download,
  .btn-upload {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}

/* 添加极小屏幕适配 */
@media (max-width: 360px) {
  .content-container {
    padding: 8px;
  }

  .sidebar {
    padding: 12px;
  }

  .profile-section {
    padding-bottom: 12px;
    margin-bottom: 12px;
  }

  .avatar {
    width: 60px;
    height: 60px;
  }

  .username {
    font-size: 0.95rem;
  }

  .nav-item {
    padding: 7px 8px;
  }

  .nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  .nav-text {
    font-size: 0.8rem;
  }

  .back-to-map {
    padding: 7px 8px;
    font-size: 0.8rem;
    margin-bottom: 12px;
  }

  .back-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }

  .content-header {
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  .content-header h1 {
    font-size: 1rem;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  .action-btn .icon {
    width: 16px;
    height: 16px;
  }

  .card-header h2 {
    font-size: 0.95rem;
  }

  .card-header p {
    font-size: 0.75rem;
  }

  .profile-card,
  .vouchers-container,
  .business-intro-container {
    padding: 12px;
  }

  .btn-reset,
  .btn-save {
    padding: 8px;
    font-size: 0.9rem;
  }

  .consult-title {
    font-size: 0.9rem;
  }

  .consult-date {
    font-size: 0.75rem;
  }

  .btn-edit-consult,
  .btn-delete-consult {
    padding: 5px 10px;
    font-size: 0.75rem;
  }

  .intro-text-container {
    height: 160px;
    padding: 12px;
  }

  .intro-textarea {
    height: 120px;
  }

  .char-count {
    font-size: 0.7rem;
  }

  .btn-edit,
  .btn-save,
  .btn-cancel {
    padding: 7px 15px;
    font-size: 0.85rem;
    min-width: 70px;
  }
}

/* 横屏模式适配 */
@media (max-height: 500px) and (orientation: landscape) {
  .content-container {
    flex-direction: row;
    align-items: flex-start;
    overflow-y: auto;
    max-height: 100vh;
  }

  .sidebar {
    position: sticky;
    top: 10px;
    width: 220px;
    max-height: calc(100vh - 20px);
    overflow-y: auto;
    padding: 15px;
  }

  .main-content {
    max-height: 100vh;
    overflow-y: auto;
  }

  .profile-section {
    padding-bottom: 12px;
    margin-bottom: 12px;
  }

  .avatar {
    width: 60px;
    height: 60px;
  }

  .username {
    font-size: 0.95rem;
    margin: 6px 0 2px;
  }

  .nav-item {
    padding: 6px 8px;
  }

  .nav-icon {
    width: 16px;
    height: 16px;
  }

  .modal-content {
    max-height: 90vh;
    max-width: 80vw;
    display: flex;
    flex-direction: column;
  }

  .modal-body {
    flex: 1;
    overflow: auto;
  }
}

/* 业务介绍样式 */
.business-intro-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

.business-intro-content {
  margin-top: 20px;
}

.intro-text-container {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  height: 250px;
  /* 固定高度 */
  margin-bottom: 16px;
  transition: all 0.2s ease;
  position: relative;
  overflow: auto;
}

.intro-text-container.editing {
  background: #fff;
  border-color: #94a3b8;
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.1);
}

.intro-textarea {
  width: 100%;
  height: 260px; /* Increased by 30% from 200px to 260px */
  /* 固定高度，留出空间给字数统计 */
  background: transparent;
  border: none;
  padding: 0;
  color: #334155;
  font-size: 0.95rem;
  line-height: 1.6;
  resize: none;
  /* 禁止调整大小 */
  font-family: inherit;
}

.intro-textarea:focus {
  outline: none;
}

.intro-display {
  color: #334155;
  font-size: 0.95rem;
  line-height: 1.6;
  white-space: pre-wrap;
  min-height: 260px; /* Added to match the textarea height */
}

.intro-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}

.btn-edit,
.btn-save,
.btn-cancel {
  padding: 12px 36px;
  font-size: 1.1rem;
  border-radius: 6px;
  min-width: 120px;
  text-align: center;
  justify-content: center;
}

.btn-edit {
  background: #1890ff;
  border: none;
  color: white;
}

.btn-edit:hover {
  background: #40a9ff;
}

.btn-save {
  background: #1890ff;
  border: none;
  color: #ffffff;
}

.btn-save:hover {
  background: #40a9ff;
}

.btn-cancel {
  background: #ffffff;
  border: 1px solid #1890ff;
  color: #1890ff;
}

.btn-cancel:hover {
  background: #f0f7ff;
}

.btn-edit .icon,
.btn-save .icon,
.btn-cancel .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* 空咨询内容样式 */
.empty-consult {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
}

.empty-consult .empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-consult .icon {
  width: 30px;
  height: 30px;
  color: #94a3b8;
}

.empty-consult h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #334155;
}

.empty-consult p {
  color: #64748b;
  font-size: 0.9rem;
}

.char-count {
  position: absolute;
  bottom: 10px;
  right: 20px;
  color: #94a3b8;
  font-size: 0.8rem;
}

/* 返回地图按钮样式 */
.back-to-map {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 16px;
  background: #1890ff;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-to-map:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.back-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

/* 我发布的咨询样式 */
.consult-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.consult-item {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.consult-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.consult-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  background: #f8fafc;
}

.consult-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
}

.consult-date {
  font-size: 0.85rem;
  color: #64748b;
}

.consult-content {
  padding: 16px 20px;
}

.content-preview {
  font-size: 0.95rem;
  color: #475569;
  line-height: 1.6;
  max-height: 120px;
  overflow: hidden;
  white-space: pre-line;
}

.consult-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 12px 20px;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

.btn-edit-consult,
.btn-delete-consult {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-edit-consult {
  background: #f1f5f9;
  color: #334155;
  border: 1px solid #e2e8f0;
}

.btn-edit-consult:hover {
  background: #e2e8f0;
}

.btn-delete-consult {
  background: #fff2f2;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.btn-delete-consult:hover {
  background: #fee2e2;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  color: #64748b;
  font-size: 0.85rem;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.image-preview {
  margin-top: 12px;
  margin-bottom: 12px;
  width: 100%;
  height: 150px;
  overflow: hidden;
  border-radius: 6px;
}

.article-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-thumbnail:hover {
  transform: scale(1.05);
}

.error-image {
  object-fit: contain;
  background-color: #f8fafc;
}

.btn-view-detail {
  background-color: #f0f9ff;
  color: #0ea5e9;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.btn-view-detail:hover {
  background-color: #e0f2fe;
}

/* 新的文章卡片网格样式 */
.article-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.article-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.article-image-container {
  position: relative;
  height: 160px;
  overflow: hidden;
  cursor: pointer;
}

.article-actions-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.article-card:hover .article-actions-overlay {
  opacity: 1;
}

.article-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.article-content-preview {
  width: 100%;
  height: 100%;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-color: #f0f2f5;
  color: #666;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  white-space: normal;
  line-height: 1.5;
}

/* 没有图片时的卡片样式 */
.card-no-image {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  color: #666;
  font-size: 15px; /* 增加字体大小 */
  text-align: center; /* 居中对齐 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
  padding: 20px 25px; /* 左右增加内边距 */
  box-sizing: border-box; /* 确保padding不会增加元素尺寸 */
  cursor: pointer;
  transition: transform 0.5s ease; /* 添加过渡效果与图片一致 */
}

.card-no-image-text {
  display: -webkit-box;
  -webkit-line-clamp: 5; /* 显示5行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word; /* 使用break-word替代break-all */
  line-height: 1.6; /* 增加行高 */
  width: 100%;
  max-height: 8em; /* 5行文字的高度 */
  position: relative;
  padding: 0 15px; /* 左右添加更大的内边距 */
  text-align: center; /* 文本居中显示 */
  margin: 0 auto; /* 水平居中 */
  color: #555; /* 稍微深一点的颜色 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.article-card:hover .article-image,
.article-card:hover .card-no-image {
  transform: scale(1.05);
}

.article-actions-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.article-card:hover .article-actions-overlay {
  opacity: 1;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(4px);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.unlike-button {
  color: #ef4444;
}

.unlike-button:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.action-button .icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.article-info {
  padding: 16px;
}

.article-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #1e293b;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  height: 2.8em;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.article-author {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.article-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #94a3b8;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.icon-sm {
  width: 14px;
  height: 14px;
}

/* 添加错误图片处理样式 */
.error-image {
  opacity: 0.7;
  filter: grayscale(100%);
  object-fit: contain;
  background-color: #f8fafc;
}

/* 暗色模式样式调整 */
@media (prefers-color-scheme: dark) {
  .article-card {
    background-color: #1e1e1e;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .article-title {
    color: #e0e0e0;
  }
  
  .article-author {
    color: #94a3b8;
  }
  
  .stat {
    color: #94a3b8;
  }
  
  .action-btn {
    background-color: rgba(30, 30, 30, 0.9);
  }
  
  .error-image {
    background-color: #1e1e1e;
  }
}

/* 删除确认框样式 */
:deep(.el-message-box) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  border: none;
  padding: 0;
}

:deep(.el-message-box__header) {
  background-color: #f8fafc;
  padding: 15px 20px;
}

:deep(.el-message-box__title) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

:deep(.el-message-box__content) {
  padding: 24px;
}

:deep(.el-message-box__btns) {
  padding: 12px 20px;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

:deep(.el-button--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

:deep(.el-button--primary:hover) {
  background-color: #2563eb;
  border-color: #2563eb;
}

:deep(.el-button--danger) {
  background-color: #ef4444;
  border-color: #ef4444;
}

:deep(.el-button--danger:hover) {
  background-color: #dc2626;
  border-color: #dc2626;
}

.delete-confirm-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.delete-icon-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #fee2e2;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.delete-icon {
  font-size: 32px;
  color: #ef4444;
}

.delete-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.delete-desc {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* 编辑相关样式已移至CreatePost.vue */

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {

  /* 增大触摸目标区域，提高可点击性 */
  .nav-item,
  .back-to-map,
  .btn-edit-consult,
  .btn-delete-consult,
  .btn-view-detail,
  .btn-edit,
  .btn-save,
  .btn-cancel,
  .btn-reset,
  .btn-save,
  .action-btn,
  .logout-btn,
  .btn-download,
  .btn-upload {
    min-height: 44px;
    /* 符合Apple建议的最小触摸目标大小 */
  }

  /* 消除悬停效果，避免iOS上的双击问题 */
  .nav-item:hover,
  .back-to-map:hover,
  .consult-item:hover,
  .btn-edit:hover,
  .btn-save:hover,
  .btn-cancel:hover,
  .btn-edit-consult:hover,
  .btn-delete-consult:hover,
  .btn-view-detail:hover,
  .btn-reset:hover,
  .btn-save:hover,
  .logout-btn:hover,
  .btn-download:hover,
  .btn-upload:hover {
    transform: none;
  }

  /* 确保表单元素有足够的触摸区域 */
  input,
  textarea,
  select,
  button {
    font-size: 16px;
    /* 防止iOS上的自动缩放 */
    padding: 12px 12px 12px 36px;
  }

  /* 调整输入框内的图标位置 */
  .input-icon {
    left: 12px;
  }

  /* 确保模态框上的操作按钮足够大 */
  .modal-close {
    width: 44px;
    height: 44px;
  }

  /* 上传区域更容易点击 */
  .business-card-upload,
  .business-license-upload {
    min-height: 120px;
  }

  /* 改善模态框的关闭体验 */
  .modal-overlay {
    padding: 10px;
  }

  /* 优化滚动体验 */
  .sidebar,
  .main-content,
  .intro-text-container,
  .modal-body {
    -webkit-overflow-scrolling: touch;
  }
}

/* 表单元素焦点优化 */
@media (max-width: 768px) {

  input:focus,
  textarea:focus {
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
    border-color: #1890ff;
    outline: none;
  }

  /* 增加表单间距，防止误触 */
  .form-group {
    margin-bottom: 16px;
  }

  /* 调整咨询项目的触摸反馈 */
  .consult-item:active {
    background-color: #f8fafc;
  }
}

/* 修复iOS滚动问题 */
@supports (-webkit-touch-callout: none) {

  .content-container,
  .sidebar,
  .main-content {
    -webkit-overflow-scrolling: touch;
  }

  /* 确保iOS上的固定高度元素可滚动 */
  .intro-text-container {
    -webkit-overflow-scrolling: touch;
  }
}

/* 模态框在移动端的优化 */
@media (max-width: 480px) {
  .modal-overlay {
    padding: 10px;
    align-items: flex-end;
    /* 在小屏幕上从底部弹出 */
  }

  .modal-content {
    width: 100%;
    max-width: 100%;
    border-radius: 12px 12px 0 0;
    /* 只有顶部圆角 */
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  /* 微信二维码在移动端的响应式调整 */
  .qr-code, .wx-login-frame {
    width: 230px;
    height: 230px;
  }
  
  .wechat-modal {
    max-width: 100%;
    max-height: 80vh;
    margin: 0;
  }
  
  .wechat-modal-body {
    padding: 5px;
  }
}

@media (max-width: 480px) {
  .modal-body {
    overflow-y: auto;
    flex: 1;
  }

  .avatar-modal {
    width: 100%;
    max-width: 100%;
  }

  .avatar-preview {
    max-width: 100%;
    height: auto;
    max-height: 50vh;
  }

  /* 改善文件上传体验 */
  .upload-placeholder .upload-text h4 {
    font-size: 0.85rem;
  }

  .upload-placeholder .upload-text p {
    font-size: 0.7rem;
  }

  .upload-icon {
    width: 32px;
    height: 32px;
  }

  .upload-icon .icon {
    width: 16px;
    height: 16px;
  }
}

/* 优化表单按钮操作 */
@media (max-width: 480px) {
  .form-actions {
    position: sticky;
    bottom: 0;
    background: white;
    margin: 0 -16px -16px;
    padding: 12px 16px;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }

  .intro-actions {
    position: sticky;
    bottom: 0;
    background: white;
    margin: 10px -16px -16px;
    padding: 12px 16px;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
  }

  .consult-actions {
    padding: 10px 16px;
  }
}

/* 极小屏幕上的额外优化 */
@media (max-width: 360px) {

  .form-actions,
  .intro-actions {
    margin: 0 -12px -12px;
    padding: 10px 12px;
  }
}

/* 平滑过渡效果 */
.sidebar,
.main-content,
.profile-card,
.vouchers-container,
.business-intro-container,
.consult-item,
.intro-text-container,
.modal-content,
.nav-item,
.back-to-map,
.btn-edit-consult,
.btn-delete-consult,
.btn-edit,
.btn-save,
.btn-cancel,
.btn-reset,
.btn-download,
.btn-upload,
.action-btn,
.logout-btn,
input,
textarea,
.avatar-container,
.avatar-overlay,
.business-card-upload,
.business-license-upload {
  transition: all 0.3s ease;
}

/* WeChat binding section styles */
.wechat-binding-section {
  background-color: #f8fafc;
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.wechat-binding-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1d5db;
}

.wechat-binding-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.binding-icon-container {
  width: 42px;
  height: 42px;
  border-radius: 10px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.binding-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.wechat-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.binding-text h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.binding-text p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  max-width: 400px;
}

.bind-wechat-btn {
  background-color: #07C160;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(7, 193, 96, 0.25);
}

.bind-wechat-btn:hover {
  background-color: #06AD56;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(7, 193, 96, 0.3);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* VIP 徽章样式 */
.vip-badge {
  position: absolute;
  bottom: 10px;
  right: -10px;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  border: none; /* 移除边框 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 20;
  line-height: 1;
  min-width: 24px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #d5b449;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  /* 移除黄色边框 */
  animation: svip-glow 2s ease-in-out infinite alternate;
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.5); /* 修改为黑色阴影 */
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.5); /* 移除金色阴影，使用黑色阴影 */
  }
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: #e0e0e0;
  }

  .personal-center {
    background-color: #121212;
  }

  .sidebar,
  .main-content,
  .profile-card,
  .vouchers-container,
  .business-intro-container,
  .content-header,
  .consult-item {
    background: #1e1e1e;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .sidebar {
    border: 1px solid #333;
  }

  .profile-section,
  .sidebar-footer {
    border-color: #333;
  }

  .username {
    color: #e0e0e0;
  }

  .user-location,
  .nav-text {
    color: #aaa;
  }

  .nav-item.active {
    background: #333;
  }

  .nav-icon {
    color: #aaa;
  }

  .nav-item.active .nav-icon {
    color: #e0e0e0;
  }

  .content-header h1,
  .card-header h2 {
    color: #e0e0e0;
  }

  .card-header p,
  .section-header h3 {
    color: #aaa;
  }

  input,
  textarea {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  input::placeholder,
  textarea::placeholder {
    color: #777;
  }

  .input-icon {
    color: #777;
  }

  .business-card-upload,
  .business-license-upload {
    background: #2a2a2a;
    border-color: #444;
  }

  .upload-text h4 {
    color: #e0e0e0;
  }

  .upload-text p {
    color: #aaa;
  }

  .btn-reset {
    background: #2a2a2a;
    border-color: #444;
    color: #aaa;
  }

  .btn-reset:hover {
    background: #333;
  }

  .consult-header,
  .consult-actions {
    background: #252525;
    border-color: #333;
  }

  .consult-title {
    color: #e0e0e0;
  }

  .consult-date {
    color: #aaa;
  }

  .content-preview {
    color: #bbb;
  }

  .btn-edit-consult {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .btn-edit-consult:hover {
    background: #333;
  }

  .btn-delete-consult {
    background: rgba(239, 68, 68, 0.2);
    color: #ff6b6b;
    border-color: rgba(239, 68, 68, 0.3);
  }

  .btn-delete-consult:hover {
    background: rgba(239, 68, 68, 0.3);
  }

  .intro-text-container {
    background: #2a2a2a;
    border-color: #444;
  }

  .intro-display {
    color: #e0e0e0;
  }

  .modal-content {
    background: #1e1e1e;
  }

  .modal-header,
  .modal-footer {
    border-color: #333;
  }

  .modal-header h3 {
    color: #e0e0e0;
  }

  .modal-close {
    background: #2a2a2a;
    border-color: #444;
  }

  .modal-close .icon {
    color: #aaa;
  }

  .btn-cancel {
    background: #2a2a2a;
    border-color: #1890ff;
  }

  .btn-download,
  .btn-upload {
    background: #1e293b;
  }

  /* 暗色模式下的表单操作区域 */
  @media (max-width: 480px) {

    .form-actions,
    .intro-actions {
      background: #1e1e1e;
      box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.3);
    }
  }
}

/* 账号信息样式 */
.account-info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 20px;
  align-items: stretch;
  justify-content: space-between;
}

.account-info-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
  text-align: left;
  min-height: 130px;
  height: auto;
  box-sizing: border-box;
}

.account-info-item:first-child {
  width: 35%;
}

.account-info-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f1f5f9;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #333;
  text-align: left;
  font-weight: 500;
  margin-bottom: 4px;
}

.info-icon {
  width: 18px;
  height: 18px;
  color: #6366f1;
}

.section-title {
  margin-left: -25px;
  font-size: 20px;
}

.info-value {
  font-size: 18px;
  font-weight: 600;
  color: #334155;
  text-align: left;
  margin-top: 15px;
}

.info-value.premium-member {
  color: #f59e0b;
  font-size: 22px;
}

.sites-info {
  width: 55%;
}

.site-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
  margin-bottom: 8px;
  text-align: left;
  width: 100%;
}

.site-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 16px;
  background-color: #818cf8;
  color: white;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.site-badge.add-site {
  background-color: #dbeafe;
  color: #3b82f6;
  cursor: pointer;
}

.site-badge.add-site:hover {
  background-color: #bfdbfe;
}

.no-sites-text {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  font-style: italic;
  padding: 6px 0;
  display: block;
  margin: 5px 0;
}

.add-icon {
  width: 14px;
  height: 14px;
  margin-right: 3px;
}

.float-right {
  margin-left: auto;
}

.redirect-link {
  display: flex;
  align-items: center;
  color: #6366f1;
  transition: all 0.2s ease;
  margin-left: auto;
  font-size: 14px;
}

.redirect-link:hover {
  color: #4f46e5;
}

.redirect-icon {
  width: 14px;
  height: 14px;
  margin-left: 4px;
}

.add-site-btn {
  display: flex;
  align-items: center;
  background-color: #6366f1;
  color: white;
  border-radius: 20px;
  padding: 6px 14px;
  margin-left: auto;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.add-site-btn:hover {
  background-color: #4f46e5;
  box-shadow: 0 3px 6px rgba(79, 70, 229, 0.4);
  color: white;
}

.add-site-btn .add-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.info-label .redirect-link {
  margin-left: auto;
  font-size: 0.8rem;
}

/* 支付记录样式 */
.payment-records-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.search-filter-container {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-end;
}

.form-item {
  flex: 1;
  min-width: 200px;
}

.form-item label {
  display: block;
  margin-bottom: 6px;
  font-size: 0.9rem;
  color: #64748b;
}

.form-item input,
.form-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
}

.form-actions {
  display: flex;
  gap: 8px;
}

.search-btn,
.reset-btn {
  display: flex;
  align-items: center;
  padding: 9px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-btn {
  background: #0f172a;
  color: white;
  border: none;
}

.search-btn:hover {
  background: #1e293b;
}

.reset-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.reset-btn:hover {
  background: #f1f5f9;
}

.search-btn .icon,
.reset-btn .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.payment-records-list {
  position: relative;
  min-height: 200px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
}

.records-table-container {
  overflow-x: auto;
}

.records-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.records-table th,
.records-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.records-table th {
  font-weight: 600;
  color: #334155;
  background: #f8fafc;
}

.records-table tr:hover {
  background-color: #f8fafc;
}

.records-table tr.paid {
  background-color: #f0fdf4;
}

.records-table .amount {
  font-weight: 600;
  color: #0f172a;
}

.records-table .actions {
  width: 120px;
  text-align: center;
}

.detail-btn {
  padding: 6px 12px;
  background: #e0f2fe;
  color: #0ea5e9;
  border: none;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.detail-btn:hover {
  background: #bae6fd;
}

.detail-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.detail-btn-disabled:hover {
  background: #e0f2fe;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 支付详情弹窗样式 */
.payment-detail-content {
  padding: 16px;
  color: #333;
  max-width: 100%;
  overflow: hidden;
}

.order-status-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-sizing: border-box;
}

.status-text {
  margin-top: 12px;
  font-size: 16px;
  font-weight: 600;
}

.checkmark-circle {
  width: 50px;
  height: 50px;
  position: relative;
  border-radius: 50%;
  background-color: #00ab55;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.status-icon.status-valid {
  background-color: #00ab55;
  color: white;
}

.status-icon.status-expired {
  background-color: #ffa000;
  color: white;
}

.status-icon.status-used {
  background-color: #d14343;
  color: white;
}

.status-icon .icon {
  width: 30px;
  height: 30px;
}

.qr-code {
  width: 350px; /* 进一步增加二维码容器宽度 */
  height: 350px; /* 进一步增加二维码容器高度 */
  border: 1px dashed #ddd;
  border-radius: 10px;
  margin-bottom: 10px;
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkmark-circle.animated {
  animation: scale-up 0.5s ease-in-out;
}

@keyframes scale-up {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.checkmark {
  width: 30px;
  height: 15px;
  border-left: 3px solid #fff;
  border-bottom: 3px solid #fff;
  transform: rotate(-45deg);
  margin-top: -4px;
  animation: checkmark-draw 0.6s ease-in-out 0.2s forwards;
  opacity: 0;
}

@keyframes checkmark-draw {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  100% {
    width: 30px;
    height: 15px;
    opacity: 1;
  }
}

/* 重复定义已整合到上面的样式中 */

.wx-login-frame {
  width: 340px; /* 增加iframe宽度以匹配容器 */
  height: 340px; /* 增加iframe高度以匹配容器 */
  border: none;
  transform: scale(1);
  transform-origin: center center; /* 调整变换原点 */
  max-width: 100%;
  max-height: 100%;
}

.detail-card-header {
  background-color: #f0f0f0;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.header-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  color: #1890ff;
}

.detail-card-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.qr-instructions {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* 此样式已被上面的定义替代 */

.wechat-modal {
  max-width: 550px; /* 进一步增加弹窗宽度 */
  width: 100%;
  display: flex;
  flex-direction: column;
  max-height: 95vh; /* 增加最大高度 */
  background-color: #fff;
  border-radius: 12px;
}

.wechat-modal-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: visible;
  height: auto; /* 确保高度能适应内容 */
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.wechat-icon {
  width: 40px;
  height: 40px;
  padding: 10px;
  border-radius: 50%;
  background-color: #07C160;
  color: white;
}

.wechat-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  text-align: center;
}

.wechat-status-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f0f9f0;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
}

.success-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.success-icon .icon {
  width: 40px;
  height: 40px;
  color: #07C160;
}

.status-message {
  font-size: 16px;
  font-weight: 600;
  color: #07C160;
  margin: 0 0 5px 0;
}

.status-hint {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.wechat-bind-tip {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 添加移动设备上的微信二维码样式 */
@media (max-width: 480px) {
  .wechat-modal {
    max-width: 380px;
    max-height: none;
    height: auto;
    margin: 0 auto;
  }
  
  .qr-code {
    width: 340px;
    height: 340px;
  }
  
  .modal-overlay {
    align-items: center;
    padding: 5px;
  }
}

/* 支付详情对话框样式 */
.payment-detail-dialog {
  --el-dialog-padding-primary: 0;
}

.payment-detail-dialog .el-dialog__header {
  padding: 16px;
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
}

.payment-detail-dialog .el-dialog__body {
  padding: 0;
}

.payment-detail-dialog .el-dialog__footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.detail-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.detail-items-container {
  padding: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #666;
  padding: 0;
  text-align: left;
  flex: 0 0 110px;
  margin: 0;
}

.detail-value {
  font-size: 14px;
  color: #333;
  word-break: break-word;
  flex: 1;
  text-align: right;
}

.detail-value.amount {
  font-size: 22px;
  font-weight: 700;
  color: #ff4d4f;
}
</style>


