export default {

  chatModal: {
    contactDetails: "联系人详细信息",
    companyName: "公司名称",
    introduction: "简介",
    email: "邮箱",
    phone: "电话",
    qq: "QQ",
    wechat: "微信",
    memberType: "会员类型",
    contactList: "联系人列表",
    searchResults: "搜索结果",
    searchPlaceholder: "手机号或昵称查找联系人",
    search: "搜索",
    startChat: "点击开始聊天",
    messageInput: "输入消息...",
    send: "发送",
    justNow: "刚刚",
    minutesAgo: "{minutes}分钟前",
    hoursAgo: "{hours}小时前",
    yesterday: "昨天",
    superMember: "超级会员"
  },
  comments: {
    title: "评论",
    writeComment: "写下你的评论...",
    loginFirst: "请先登录再评论",
    submit: "发布",
    submitting: "发布中...",
    reply: "回复",
    delete: "删除",
    noComments: "暂无评论，快来抢沙发吧！",
    replyToComment: "回复评论",
    writeReply: "写下你的回复...",
    cancel: "取消",
    sending: "发送中...",
    deleteConfirm: "确定要删除这条评论吗？",
    deleteSuccess: "评论已删除",
    deleteFailed: "删除失败",
    postSuccess: "评论发布成功",
    postFailed: "评论发布失败",
    replySuccess: "回复发布成功",
    replyFailed: "回复发布失败",
    showAllReplies: "查看全部 {count} 条回复",
    hideReplies: "收起回复",
    anonymous: "匿名用户",
    replyTo: "回复 @{name}：",
    emptyComment: "评论内容不能为空",
    charLimit: "{current}/{max}字"
  },
  article: {
    backButton: '返回',
    authorInfo: '作者信息',
    anonymous: '匿名用户',
    imageAttachments: '图片附件',
    imagePreview: '图片预览',
    notFound: '文章不存在或已被删除',
    notProvided: '未提供',
    loading: '正在加载文章...',
    backToList: '返回列表',
    imageLoadError: '图片加载失败，请稍后再试',
    imageLoading: '图片加载中...',
    userInfo: {
      title: '用户信息',
      position: '职位',
      businessIntro: '业务简介',
      companyName: '公司名称',
      phone: '联系电话',
      userId: '用户ID'
    },
    debug: {
      articleId: '文章ID',
      routeParamId: '路由参数ID',
      none: '无'
    },
    dateFormat: '{year}-{month}-{day} {hours}:{minutes}:{seconds}'
  },
  createPost: {
    title: '发布资讯',
    editTitle: '编辑资讯',
    back: '返回',
    loading: '加载中...',
    form: {
      titleLabel: '标题',
      titlePlaceholder: '请输入标题（20字以内）',
      contentLabel: '内容',
      contentPlaceholder: '请输入内容',
      uploadLabel: '图片上传',
      uploadOptional: '（可选，点击已经上传的图片可以进行切换）',
      addImage: '添加图片',
      tagsLabel: '标签',
      tagsOptional: '（可多选）',
      tagsSelected: '已选择{count}个标签',
      tagsHint: '请选择相关标签',
      noTags: '暂无可用标签',
      converting: '正在处理...'
    },
    actions: {
      publish: '发布',
      publishing: '发布中...',
      saveChanges: '保存修改',
      saving: '保存中...',
      cancelEdit: '取消编辑'
    },
    imageLimit: '最多只能上传3张图片',
    uploadFailed: '上传失败',
    membership: {
      title: '会员专享功能',
      description1: '发布资讯是会员专享功能',
      description2: '成为会员即可发布资讯、联系合作伙伴等',
      benefits: {
        posting: '发布资讯',
        vouchers: '获取优惠券',
        support: '专属客服支持'
      },
      cancel: '暂不开通',
      confirm: '立即开通'
    },
    voucher: {
      title: '恭喜获得优惠券',
      type: '会员专享优惠券',
      validity: '{days}天内有效',
      success: '恭喜您获得了一张会员优惠券！',
      later: '稍后使用',
      useNow: '立即使用'
    }
  },
  nav: {
    chinese: '中文',
    english: '英文',
    switch3D: '切换3D',
    switch2D: '切换平面',
    member: '会员',
    login: '登录',
    messages: '消息'
  },
  login: {
    title: '账号登录',
    codeLogin: '验证码登录',
    passwordLogin: '密码登录',
    phoneNumber: '手机号',
    verificationCode: '验证码',
    password: '密码',
    captcha: '图形验证码',
    seconds: '秒',
    enterPhoneNumber: '请输入手机号',
    enterVerificationCode: '请输入验证码',
    enterPassword: '请输入密码',
    enterCaptcha: '请输入验证码',
    refreshCaptcha: '点击刷新验证码',
    getCode: '获取验证码',
    agreementPrefix: '我已阅读并同意',
    userAgreement: '《用户协议》',
    and: '和',
    privacyPolicy: '《隐私协议》',
    login: '登录',
    otherLoginMethods: '其他登录方式',
    phoneVerification: '手机号注册',
    wechatScan: '微信扫码',
    noAccount: '还没有账号？',
    register: '注册账号',
    invalidPhone: '请输入正确的手机号格式',
    codeSent: '验证码已发送',
    codeSendFailed: '验证码发送失败',
    codeSendError: '验证码发送失败，请稍后重试',
    codeSendErrorLog: '验证码发送错误:',
    codeFormatError: '验证码必须是数字',
    agreementRequired: '请先阅读并同意用户协议和隐私政策',
    success: '登录成功',
    failure: '登录失败，请检查账号信息',
    errorRetry: '登录失败，请稍后重试',
    loginError: '登录错误:',
  },
  register: {
    phoneTitle: '手机号注册',
    phoneNumber: '手机号',
    verificationCode: '验证码',
    password: '密码',
    confirmPassword: '确认密码',
    enterPhoneNumber: '请输入手机号',
    enterVerificationCode: '请输入验证码',
    enterPassword: '请输入密码',
    enterPasswordAgain: '请再次输入密码',
    secondsRemaining: '秒后重发',
    getCode: '获取验证码',
    agreementPrefix: '我已阅读并同意',
    userAgreement: '《用户协议》',
    and: '和',
    privacyPolicy: '《隐私协议》',
    register: '注册',
    otherRegisterMethods: '其他注册方式',
    wechatScan: '微信扫码',
    alreadyHaveAccount: '已有账号？',
    goToLogin: '去登录',
    codeSent: '验证码已发送',
    codeSendFailed: '验证码发送失败',
    codeSendError: '验证码发送失败，请稍后重试',
    codeSendErrorLog: '验证码发送错误:',
    agreementRequired: '请阅读并同意用户协议和隐私协议',
    completeInfo: '请填写完整信息',
    passwordMismatch: '两次输入的密码不一致',
    registerLoginSuccess: '注册并登录成功',
    registerSuccessLoginManually: '注册成功，请手动登录',
    autoLoginFailed: '自动登录失败:',
    success: '注册成功',
    failure: '注册失败',
    registerError: '注册错误:',
    errorRetry: '注册失败，请稍后重试',
  },
  agreement: {
    confirmTitle: '协议确认',
    wechatConfirmText: '使用微信扫码登录需要同意我们的用户协议和隐私政策。是否同意？',
    userAgreement: '《用户协议》',
    and: '和',
    privacyPolicy: '《隐私协议》',
    cancel: '取消',
    agreeAndContinue: '同意并继续',
    cancelConfirmTitle: '取消协议确认',
    cancelConfirmText: '微信扫码登录需要同意用户协议和隐私政策。取消同意将返回上一页面。',
    confirmCancel: '是否确认取消？',
    dontCancel: '不取消',
    confirmReturn: '确认返回',
    userAgreementTitle: '用户协议',
    userAgreementContent: '这是用户协议内容...',
    privacyAgreementTitle: '隐私协议',
    privacyAgreementContent: '这是隐私协议内容...',
    agreeButton: '同意'
  },
  worldMap: {
    popularsites: '热门站点',
    searchPlaceholder: '搜索站点...',
    searching: '搜索中...',
    inquire: '查询',
    clear: '清空',
    tabs: {
      hot: '热门',
      history: '历史',
      cnRu: '中俄',
      cnAs: '中亚', 
      cnEu: '中欧',
      cnSa: '东南亚'
    },
    routeOptions: {
      title: '可选路线方案',
      count: '共{count}条路线',
      expand: '展开其他路线',
      collapse: '收起其他路线'
    },
    transport: {
      title: '选择运输方式',
      road: '公路运输',
      rail: '铁路',
      air: '航空运输',
      sea: '海运运输'
    },
    stationType: {
      start: '初始站',
      destination: '目的站' 
    },
    placeholders: {
      start: '输入起始地点...',
      destination: '输入目的地点...'
    },
    routeInfo: {
      mileage: '里程数',
      borderPort: '转关口岸',
      duration: '耗时',
      trafficCondition: '拥堵情况',
      availability: '舱位情况'
    },
    rotateHint: {
      title: '请横屏使用以获得最佳体验',
      subtitle: '请旋转设备获得更好的体验'
    },
    searchResults: '搜索结果',
    noResults: '未找到结果',
    planRoute: '规划路线',
    planning: '规划中...',
    routeDetails: {
      title: '路线详情',
      stations: '途经站点',
      distance: '总里程',
      time: '预计时间',
      congestion: '路况状况',
      capacity: '舱位情况'
    },
    businessIntro: {
      title: '业务介绍',
      contact: '私信',
      close: '关闭'
    }
  },
  right: {
    share: '分享',
    miniProgram: '公众号',
    wechat: '微信社群',
    weixinshequn: '扫码进入微信社群',
    gonzhao: '扫码进入公众号',
    phone: '电话咨询',
    online: '在线',
    customer: '客服',
    shareTip: '分享内容已自动复制，粘贴到对话框发送即可',
    shares:'分享到',
  },
  userCenter: {
    title: '个人中心',
    profile: {
      title: '个人资料',
      subtitle: '完善您的个人信息，让更多人了解您',
      required: '详细信息',
      optional: '选填信息',
      upload: '文件上传',
      businessCard: {
        title: '名片上传',
        desc: '点击上传您的电子名片'
      },
      businessLicense: {
        title: '营业执照上传', 
        desc: '点击上传您的营业执照'
      },
      uploadSuccess: '上传成功',
      reset: '重置',
      save: '保存'
    },
    nav: {
      vouchers: '我的优惠券',
      profile: '个人资料',
      settings: '账号设置',
      Mylikes: '我的点赞',
      help: '帮助中心'
    }
  },
  userInfo: {
    title: '个人中心',
    settings: '账号设置',
    backToHome: '返回首页',
    changeAvatar: '更换头像',
    nav: {
      profile: '个人资料',
      help: '业务介绍',
      vouchers: '我的优惠券',
      paymentRecords: '支付记录',
      consult: '我发布的资讯',
      settings: '账号设置',
      likes: '我的点赞'
    },
    profile: {
      title: '个人资料',
      promptdialogbox: '填写即同意显示',
      subtitle: '完善您的个人信息，让更多人了解您',
      required: '详细信息',
      optional: '选填信息',
      name: '姓名',
      phone: '电话',
      qq: 'QQ号码',
      wechat: '微信号',
      email: '邮箱',
      company: '公司名称',
      businessIntro: '业务介绍',
      fileUpload: '文件上传',
      Avatar: '头像上传',
      businessCardUpload: '名片上传',
      businessCardUploadHint: '点击上传您的电子名片',
      businessLicenseUpload: '营业执照上传',
      businessLicenseUploadHint: '点击上传您的营业执照',
      uploadSuccess: '上传成功',
      clicktoview: '点击查看',
      reset: '重置',
      position: '职位',
      save: '保存',
      logout: '退出登录',
      locationLoading: '正在获取位置...',
      namePlaceholder: '请输入您的姓名',
      phonePlaceholder: '请输入您的电话号码',
      qqPlaceholder: '请输入您的QQ号码',
      wechatPlaceholder: '请输入您的微信号',
      emailPlaceholder: '请输入您的邮箱',
      companyPlaceholder: '请输入您的公司名称',
      downloadBusinessCard: '查看名片',
      downloadBusinessLicense: '查看营业执照',
      accountInfo: '账号信息',
      memberType: '会员类型',
      regularUser: '普通会员',
      sitesInfo: '已开通站点',
      addSite: '增加新站点',
      wechatBinding: {
        title: '微信绑定',
        bindButton: '绑定微信账号',
        alreadyBound: '您已绑定微信账号',
        rebindHint: '重新扫码可更换绑定的微信账号',
        loadingQrCode: '正在加载二维码...',
        scanInstruction: '打开微信扫一扫，即可将您的微信与账号进行绑定',
        loginRequired: '请先登录后再绑定微信',
        bindSuccess: '微信绑定成功',
        bindFailed: '绑定失败，请重试',
        qrCodeFailed: '二维码获取失败，请重试',
        scanSuccess: '扫码成功，等待确认...',
        authSuccess: '授权成功，正在处理...',
        qrExpired: '二维码已过期，请刷新',
        bindingComplete: '微信绑定成功!',
        binding: '正在绑定您的微信账号...',
        loginFailure: '绑定失败，请先登录'
      }
    },
    vouchers: {
      title: '我的优惠券',
      empty: '暂无优惠券',
      threshold: '满{amount}元可用',
      validUntil: '有效期至',
      remainingDays: '剩余{days}天',
      expired: '已过期',
      used: '已使用',
      canUse: '可使用',
      goUse: '去使用',
      loginRequired: '请登录后查看您的优惠券'
    },
    payment: {
      title: '支付记录',
      subtitle: '查看您的支付历史记录',
      loading: '正在加载支付记录...',
      empty: {
        title: '暂无支付记录',
        subtitle: '您的支付记录将会显示在这里'
      },
      search: {
        orderNo: '订单号',
        status: '状态',
        all: '全部',
        paid: '已支付',
        unpaid: '未支付',
        canceled: '已取消',
        searchBtn: '搜索',
        resetBtn: '重置'
      },
      table: {
        orderNo: '订单号',
        productName: '产品名称',
        amount: '金额',
        status: '状态',
        payTime: '支付时间',
        actions: '操作',
        unpaidText: '未支付',
        viewDetails: '详情'
      },
      detail: {
        title: '支付详情',
        orderInfo: '订单信息',
        orderNo: '订单号',
        productName: '产品名称',
        createTime: '创建时间',
        stationInfo: '站点信息',
        userInfo: '用户信息',
        userId: '用户ID',
        nickname: '昵称',
        notSet: '未设置',
        close: '关闭',
        cancelOrder: '取消订单',
        amount: '支付金额'
      }
    },
    businessIntro: {
      title: '业务介绍',
      subtitle: '向潜在合作伙伴介绍您的业务',
      placeholder: '描述您的业务服务、专长或其他您想分享的信息...',
      charCount: '{count}/500字',
      emptyText: '暂无业务介绍。点击编辑按钮添加您的业务信息。',
      edit: '编辑',
      save: '保存',
      cancel: '取消'
    },
    consult: {
      title: '我发布的资讯',
      subtitle: '管理您发布的所有资讯',
      empty: {
        title: '暂无发布资讯',
        subtitle: '您发布的资讯将会显示在这里'
      },
      deleteConfirm: {
        title: '删除资讯',
        content: '确定要删除这条资讯吗？此操作无法撤销。',
        confirm: '删除',
        cancel: '取消'
      },
      authorAvatar: '作者头像',
      articleImage: '文章图片',
      likeCount: '{count}次点赞'
    },
    likes: {
      title: '我的点赞',
      subtitle: '您点赞过的文章',
      empty: {
        title: '暂无点赞内容',
        subtitle: '您点赞的文章将会显示在这里'
      },
      unlikeButton: '取消点赞'
    }
  },
  changePassword: {
    title: '修改密码',
    subtitle: '请输入您的当前密码和新密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmPassword: '确认新密码',
    currentPasswordPlaceholder: '请输入当前密码',
    newPasswordPlaceholder: '请输入新密码',
    confirmPasswordPlaceholder: '请再次输入新密码',
    strengthLabel: '密码强度：',
    cancel: '取消',
    submit: '确认修改',
    requirements: {
      minLength: '至少8个字符',
      number: '至少包含1个数字'
    },
    strength: {
      veryWeak: '非常弱',
      weak: '弱',
      medium: '中等',
      strong: '强'
    },
    success: '密码修改成功！',
    error: {
      currentPasswordRequired: '请输入当前密码',
      newPasswordRequired: '请输入新密码',
      confirmPasswordRequired: '请确认新密码',
      passwordMismatch: '两次输入的密码不一致',
      minLength: '密码长度至少为8个字符',
      requireNumber: '密码必须包含至少一个数字',
      sameAsOld: '新密码不能与当前密码相同',
      generalError: '密码修改失败，请稍后重试'
    },
    errors: {
      minLength: '密码长度至少为8个字符',
      mismatch: '两次输入的密码不一致',
      currentPassword: '请输入当前密码',
      newPassword: '请输入新密码',
      confirmPassword: '请确认新密码',
      sameAsCurrent: '新密码不能与当前密码相同',
      number: '密码必须包含至少一个数字'
    },
    failure: '密码修改失败，请稍后重试'
  },
  member: {
    title: '会员中心',
    memberStatus: '会员状态',
    statusDesc: '您当前的会员信息和权益',
    regularMember: '普通会员',
    premiumMember: '超级会员',
    notActive: '未开通会员',
    expiryDate: '到期时间',
    memberBenefits: '开通会员享受更多权益',
    memberPlans: '会员套餐',
    recommended: '推荐',
    premium: '尊享',
    pricePerMonth: '月',
    alreadyActivated: '续费',
    premiumRenewalNotice: '超级会员请联系客服进行续费',
    benefits: {
      unlimitedViews: '无限次内容浏览',
      prioritySupport: '优先客服响应',
      monthlyDownloads: '每月10次下载特权',
      allRegularBenefits: '包含普通会员所有权益',
      dedicatedSupport: '专属客服一对一服务',
      unlimitedDownloads: '无限次下载特权',
      priorityAccess: '超级内容优先获取'
    },
    buttons: {
      activate: '立即开通',
      contactService: '联系客服开通',
      uploadDocs: '上传资料',
      explore: '了解会员权益',
      renew: '续费'
    },
    payment: {
      title: '支付普通会员',
      titleRenew: '续费普通会员',
      titleStations: '站点购买',
      memberAmount: '会员费用',
      stationAmount: '站点费用',
      discount: '优惠券',
      total: '应付金额',
      coupon: {
        use: '使用优惠券 (¥20)',
        available: '可用优惠券',
        select: '选择优惠券',
        none: '不使用优惠券',
        expiry: '有效期至',
        invalid: '该优惠券不可用'
      },
      method: {
        title: '支付方式',
        wechat: '微信支付',
        alipay: '支付宝',
        card: '银行卡'
      },
      confirm: '确认支付',
      cancel: '取消',
      success: '支付成功',
      processing: '正在支付',
      alipay: {
        title: '支付宝支付',
        prompt: '使用支付宝支付请联系客服',
        contactService: '联系客服'
      },
      bankTransfer: {
        title: '对公付款信息',
        companyName: '公司名称',
        bankName: '开户银行',
        accountNumber: '银行账号',
        transferAmount: '转账金额',
        notes: '转账须知',
        notesTitle: '转账须知：',
        notesList: [
          '请务必在转账备注中填写您的用户ID：',
          '转账后请保存好转账凭证，并联系客服提供付款信息',
          '我们将在确认收款后1-2个工作日内为您开通会员'
        ],
        contactService: '联系客服',
        close: '关闭'
      }
    },
    upload: {
      title: '上传资料',
      type: {
        label: '咨询类型',
        general: '一般咨询',
        technical: '技术支持',
        billing: '账单问题',
        membership: '会员相关'
      },
      content: {
        label: '咨询内容',
        placeholder: '请详细描述您的问题...'
      },
      attachment: {
        label: '上传附件',
        drag: '点击或拖拽文件到此处上传',
        hint: '支持PNG、JPG、PDF等格式，最大10MB',
        uploaded: '已上传文件:'
      },
      submit: '提交上传',
      cancel: '取消'
    },
    station: {
      zhushi:'注:会员有效期内站点有效，到期暂停，续费会员恢复。',
      selectStation: '购买站点',
      stationFee: '单站点费用',
      selectStationTitle: '购买站点',
      selectStationSubtitle: '请选择您的站点',
      searchPlaceholder: '搜索站点名称...',
      hotStations: '热门站点',
      stationHot: '热门',
      stationExit: '出境',
      noResults: '未找到匹配的站点',
      noResultsHint: '请尝试其他关键词',
      loading: '正在加载车站信息...',
      confirm: '确认购买',
      cancel: '取消',
      needMember: '请先成为会员后再选择站点',
      multiSelectHint: '可多选',
      singleSelectHint: '单选模式',
      selectedStations: '已选站点',
      selectedStation: '已选站点',
      totalPrice: '总价'
    },
    wxpay: {
      title: '微信支付',
      scanTip: '请使用微信扫描二维码完成支付',
      closeConfirm: '是否关闭支付二维码，关闭后订单将会失效!!!',
      timeout: '支付超时，请重试',
      generateFailed: '生成二维码失败，请重试',
      getFailed: '获取支付二维码失败',
      createOrderFailed: '创建订单失败',
      requestFailed: '支付请求失败，请重试'
    },
    memberBenefitsTitle: '会员权益'
  },
  avaterCart: {
    siteMembers: '此站点代理',
    superMember: 'SVIP',
    normalMember: 'VIP',
    vipMembers: 'VIP',
    normalMembers: 'VIP',
    expand: '展开',
    collapse: '收起',
    more: '更多',
    notProvided: '暂未提供',
    contactQQ: '联系QQ',
    qqNotProvided: '该供应商暂未提供QQ号码',
    contactNow: '立即联系',
    businessIntro: '业务简介',
    loginFirst: '请先登录',
    noCompanyInfo: '暂无公司信息',
    noNickname: '未设置昵称',
    userId: 'ID'
  },
  plaza: {
    smallPlaza: "小广场",
    searchPlaceholder: "搜索标题、内容",
    publish: "发布",
    exitPlaza: "退出小广场",
    tags: "标签",
    all: "全部",
    topPost: "置顶",
    noContent: "暂无相关内容",
    postImage: "帖子图片",
    previewImage: "预览图片",
    companyIntro: "公司简介",
    edit: "编辑",
    delete: "删除",
    report: "举报",
    notProvided: "未提供",
    position: "担任职位",
    businessIntro: "业务简介",
    companyName: "公司名称",
    contactPhone: "联系电话",
    userId: "用户ID",
    membersOnlyMessage: "发布资讯功能仅对会员开放",
    tip: "提示",
    confirm: "确定",
    systemBusy: "系统繁忙，请稍后再试",
    supplierInfo: {
      title: "供应商信息",
      contactPerson: "联系人",
      position: "担任职位",
      businessScope: "业务范围",
      companyName: "公司名称",
      phone: "联系电话"
    },
    searchResults: {
      notFound: "没有找到相关内容",
      tryOtherKeywords: "您搜索的\"{keyword}\"暂时没有哦，请尝试其他关键词",
      noContent: "暂无内容"
    },
    loading: "加载中...",
    allLoaded: "已加载全部内容",
    scrollToLoadMore: "向下滑动加载更多",
    dateFormat: "{year}年{month}月{day}日 {hours}:{minutes}",
    articleImage: "文章图片",
    userAvatar: "用户头像",
    noTitle: "无标题",
    user: "用户",
    anonymous: "匿名用户",
    unknownTime: "未知时间",
    dateFormatError: "日期格式错误",
    loginRequired: "请先登录后再操作",
    likeSuccess: "点赞成功",
    likeFailed: "点赞失败",
    unlikeSuccess: "已取消点赞",
    unlikeFailed: "取消点赞失败",
    networkError: "网络错误，操作失败"
  },
  wechat: {
    scanTitle: '微信扫码登录',
    loadingQrCode: '正在加载二维码...',
    agreementPrefix: '我已阅读并同意',
    userAgreement: '《用户协议》',
    and: '和',
    privacyPolicy: '《隐私协议》',
    otherLoginMethods: '其他登录方式',
    phoneVerification: '手机号注册',
    alreadyHaveAccount: '已有账号？',
    goToLogin: '去登录',
    scanInstructions: '请使用微信扫码登录',
    qrCodeError: '二维码获取失败，请重试',
    qrCodeErrorLog: '获取二维码失败:',
    loginUrlLog: '微信登录URL:',
    iframeLoaded: '微信登录iframe加载完成',
  },
  bindPhone: {
    title: '绑定手机号到微信账号',
    description: '为了完成微信账号登录，请绑定手机号',
    phone: '手机号',
    verificationCode: '验证码',
    password: '设置密码',
    getCode: '获取验证码',
    seconds: '秒后重发',
    phonePlaceholder: '请输入手机号',
    codePlaceholder: '请输入验证码',
    passwordPlaceholder: '请设置密码',
    agreementPrefix: '我已阅读并同意',
    cancel: '取消',
    confirm: '立即绑定',
    success: '微信绑定成功',
    failure: '绑定失败，请重试',
    phoneRequired: '请输入手机号',
    phoneFormat: '请输入正确的手机号格式',
    codeRequired: '请输入验证码',
    passwordRequired: '请设置密码',
    agreementRequired: '请阅读并同意用户协议和隐私协议'
  }
}
