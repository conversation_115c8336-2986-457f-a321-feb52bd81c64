<template>
  <div class="comments-section">
    <h3 class="section-title">评论（{{ comments.length || 0 }}）</h3>
    
    <!-- 评论表单 -->
    <div class="comment-form">
      <div class="comment-avatar">
        <img :src="userAvatar" alt="用户头像">
      </div>
      <div class="comment-input-container">
        <textarea 
          v-model="commentText" 
          class="comment-input" 
          :placeholder="isLoggedIn ? '写下你的评论...' : '请先登录后评论'"
          :disabled="!isLoggedIn"
          @keydown.ctrl.enter="submitComment"
        ></textarea>
        <div class="comment-form-footer">
          <span class="comment-tips">Ctrl + Enter 快捷发送</span>
          <button 
            class="submit-button" 
            :disabled="!isLoggedIn || !commentText.trim() || isSubmitting"
            @click="submitComment"
          >
            {{ isSubmitting ? '发送中...' : '发表评论' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div class="comments-list" v-if="comments.length > 0">
      <div v-for="comment in comments" :key="comment.id" class="comment-item">
        <div class="comment-avatar">
          <img :src="getAvatarUrl(comment.avatar)" alt="评论者头像">
        </div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="commenter-name">{{ comment.username || '匿名用户' }}</span>
            <span class="comment-time">{{ formatCommentTime(comment.createTime) }}</span>
          </div>
          <p class="comment-text">{{ comment.content }}</p>
          <div class="comment-actions">
            <button class="action-button reply-button" @click="replyToComment(comment)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
              </svg>
              回复
            </button>
            <button 
              v-if="isOwner(comment)" 
              class="action-button delete-button" 
              @click="deleteComment(comment.id)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              </svg>
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 空评论状态 -->
    <div v-else class="empty-comments">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      </div>
      <p class="empty-text">暂无评论，快来发表第一条评论吧</p>
    </div>
    
    <!-- 回复弹框 -->
    <div v-if="showReplyForm" class="reply-form-container">
      <div class="reply-form-overlay" @click="cancelReply"></div>
      <div class="reply-form">
        <div class="reply-form-header">
          <h4>回复 {{ replyTo.username }}</h4>
          <button class="close-button" @click="cancelReply">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <textarea 
          v-model="replyText" 
          class="reply-input" 
          placeholder="写下你的回复..."
          ref="replyInput"
        ></textarea>
        <div class="reply-form-footer">
          <button class="cancel-button" @click="cancelReply">取消</button>
          <button 
            class="submit-button" 
            :disabled="!replyText.trim() || isSubmitting"
            @click="submitReply"
          >
            {{ isSubmitting ? '发送中...' : '回复' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useUserStore } from '../store/user';
import { ElMessage } from 'element-plus';
import request from '../utils/request';
import { getCommentList, postComment } from '../api/comment';

const props = defineProps({
  articleId: {
    type: [String, Number],
    required: true
  }
});

// 获取用户信息
const userStore = useUserStore();
const isLoggedIn = computed(() => !!userStore.token);
const userId = computed(() => userStore.userInfo?.userId || '');
const userAvatar = computed(() => {
  const avatar = userStore.userInfo?.avatar;
  if (!avatar) return '/image/default-avatar.png';
  if (avatar.startsWith('http')) return avatar;
  return `${import.meta.env.VITE_BASE_API}${avatar}`;
});

// 评论状态
const comments = ref([]);
const commentText = ref('');
const isSubmitting = ref(false);
const showReplyForm = ref(false);
const replyTo = ref({});
const replyText = ref('');
const replyInput = ref(null);

// 获取评论列表
const fetchComments = async () => {
  if (!props.articleId) {
    console.warn('文章ID无效，无法获取评论列表');
    return;
  }

  try {
    // 使用新的API函数获取评论列表
    const response = await getCommentList({
      targetId: props.articleId,
      targetType: 'article'
    });
    
    if (response.code === 200) {
      comments.value = response.data || [];
      console.log('获取到评论列表:', comments.value);
    } else {
      console.error('获取评论失败:', response.msg);
      if (response.code !== 401) {
        ElMessage.warning('获取评论失败，请稍后再试');
      }
    }
  } catch (error) {
    console.error('获取评论出错:', error);
  }
};

// 提交评论
const submitComment = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再评论');
    return;
  }
  
  if (!commentText.value.trim()) {
    ElMessage.warning('评论内容不能为空');
    return;
  }
  
  try {
    isSubmitting.value = true;
    const response = await addComment({
      articleId: props.articleId,
      content: commentText.value.trim()
    });
    
    if (response.code === 200) {
      ElMessage.success('评论发表成功');
      commentText.value = '';
      fetchComments(); // 刷新评论列表
    } else {
      ElMessage.error(response.msg || '评论发表失败');
    }
  } catch (error) {
    console.error('发表评论出错:', error);
    ElMessage.error('评论发表失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 回复评论
const replyToComment = (comment) => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再回复');
    return;
  }
  
  replyTo.value = comment;
  showReplyForm.value = true;
  
  // 在下一个渲染周期后聚焦输入框
  nextTick(() => {
    if (replyInput.value) {
      replyInput.value.focus();
    }
  });
};

// 提交回复
const submitReply = async () => {
  if (!replyText.value.trim()) {
    ElMessage.warning('回复内容不能为空');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 构建回复数据 - 使用完全重构的方式
    let replyData = {};
    
    // 1. 添加必要字段
    replyData.articleId = props.articleId;
    replyData.content = replyText.value.trim();
    
    // 2. 只有当回复目标ID存在且有意义时，才添加parentId
    if (replyTo.value.id && replyTo.value.id !== 0) {
      replyData.parentId = replyTo.value.id;
    }
    
    // 打印请求数据检查
    console.log("发送的回复数据:", replyData);
    
    const response = await addReply(replyData);
    
    if (response.code === 200) {
      ElMessage.success('回复发表成功');
      cancelReply();
      fetchComments(); // 刷新评论列表
    } else {
      ElMessage.error(response.msg || '回复发表失败');
    }
  } catch (error) {
    console.error('发表回复出错:', error);
    ElMessage.error('回复发表失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 取消回复
const cancelReply = () => {
  showReplyForm.value = false;
  replyTo.value = {};
  replyText.value = '';
};

// 删除评论
const deleteComment = async (commentId) => {
  try {
    const confirmed = await ElMessage.confirm('确定要删除此评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    if (confirmed) {
      const response = await removeComment(commentId);
      if (response.code === 200) {
        ElMessage.success('评论删除成功');
        fetchComments(); // 刷新评论列表
      } else {
        ElMessage.error(response.msg || '删除失败');
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论出错:', error);
      ElMessage.error('删除失败，请稍后重试');
    }
  }
};

// 判断评论是否为当前用户所发
const isOwner = (comment) => {
  return comment.userId === userId.value;
};

// 获取头像URL
const getAvatarUrl = (avatar) => {
  if (!avatar) return '/image/default-avatar.png';
  if (avatar.startsWith('http')) return avatar;
  return `${import.meta.env.VITE_BASE_API}${avatar}`;
};

// 格式化评论时间
const formatCommentTime = (timestamp) => {
  if (!timestamp) return '未知时间';
  
  const now = new Date();
  const commentTime = new Date(timestamp);
  const diffMs = now - commentTime;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  // 一分钟内
  if (diffSec < 60) {
    return '刚刚';
  }
  // 一小时内
  if (diffMin < 60) {
    return `${diffMin}分钟前`;
  }
  // 一天内
  if (diffHour < 24) {
    return `${diffHour}小时前`;
  }
  // 一周内
  if (diffDay < 7) {
    return `${diffDay}天前`;
  }
  
  // 超过一周显示具体日期
  const year = commentTime.getFullYear();
  const month = String(commentTime.getMonth() + 1).padStart(2, '0');
  const day = String(commentTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};



const addComment = (data) => {
  // 构建符合Comment类结构的请求体
  const commentData = {
    content: data.content,
    targetId: data.articleId,
    targetType: 'article' // 类型必须是文章
  };
  
  // 直接使用封装好的API函数
  return postComment(commentData);
};

const addReply = (data) => {
  // 构建符合Comment类结构的请求体
  const replyData = {
    content: data.content,
    targetId: data.articleId,
    targetType: 'article' // 类型必须是文章
  };
  
  // 只有当有父评论ID且不为0时才添加到请求参数中
  if (data.parentId && data.parentId !== 0) {
    replyData.parentId = data.parentId;
  }
  
  // 直接使用封装好的API函数
  return postComment(replyData);
};

const removeComment = (commentId) => {
  return request({
    url: `/comment/${commentId}`,
    method: 'delete'
  });
};

onMounted(() => {
  if (props.articleId) {
    fetchComments();
  }
});

watch(() => props.articleId, (newId) => {
  if (newId) {
    fetchComments();
  }
}, { immediate: true });
</script>

<style scoped>
.comments-section {
  margin-top: 30px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.section-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 500;
}

/* 评论表单 */
.comment-form {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.comment-input {
  width: 100%;
  min-height: 80px;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  resize: vertical;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  transition: all 0.3s;
}

.comment-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.comment-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.comment-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.comment-tips {
  font-size: 12px;
  color: #999;
}

.submit-button {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.submit-button:hover {
  background: #40a9ff;
}

.submit-button:disabled {
  background: #bfbfbf;
  cursor: not-allowed;
}

/* 评论列表 */
.comments-list {
  margin-top: 20px;
}

.comment-item {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.commenter-name {
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 8px;
  white-space: pre-line;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  padding: 4px 0;
  transition: all 0.2s;
}

.action-button:hover {
  color: #1890ff;
}

.action-button svg {
  width: 14px;
  height: 14px;
}

.delete-button:hover {
  color: #ff4d4f;
}

/* 空评论状态 */
.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
}

.empty-icon {
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

/* 回复弹框 */
.reply-form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.reply-form {
  position: relative;
  width: 90%;
  max-width: 500px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.reply-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.reply-form-header h4 {
  font-size: 16px;
  color: #333;
  margin: 0;
  font-weight: 500;
}

.close-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #666;
}

.reply-input {
  width: 100%;
  min-height: 100px;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  resize: vertical;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16px;
}

.reply-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.reply-form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  background: #f5f5f5;
  color: #666;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.cancel-button:hover {
  background: #e8e8e8;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .comments-section {
    padding: 16px;
    border-radius: 8px;
  }
  
  .comment-form {
    gap: 12px;
  }
  
  .comment-avatar {
    width: 32px;
    height: 32px;
  }
  
  .comment-input {
    min-height: 60px;
    padding: 10px 12px;
  }
  
  .submit-button, .cancel-button {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .reply-form {
    width: 95%;
    padding: 16px;
  }
}
</style> 