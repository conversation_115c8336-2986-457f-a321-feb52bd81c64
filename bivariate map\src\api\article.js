import request from '../utils/request';

/**
 * 获取文章列表
 * @param {Object} params 查询参数
 * @param {number} [params.pageNum=1] 页码
 * @param {number} [params.pageSize=10] 每页条数
 * @param {number} [params.tagId] 标签ID
 * @returns {Promise} 请求Promise
 */
export function getArticleList(params) {
  return request({
    url: '/article/list',
    method: 'get',
    params
  });
}

/**
 * 获取文章详情
 * @param {number|string} id 文章ID
 * @returns {Promise} 请求Promise
 */
export function getArticleDetail(id) {
  return request({
    url: `/article/info/${id}`,
    method: 'get'
  });
}

/**
 * 发布文章
 * @param {FormData} formData 表单数据，包含文章信息和图片
 * @returns {Promise} 请求Promise
 */
export function publishArticle(formData) {
  return request({
    url: '/article/publish',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 编辑文章
 * @param {FormData} formData 表单数据，包含文章信息和图片
 * @returns {Promise} 请求Promise
 */
export function updateArticle(formData) {
  return request({
    url: '/article/update',
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除文章
 * @param {number|string} articleId 文章ID
 * @param {number|string} userId 用户ID (可选)
 * @returns {Promise} 请求Promise
 */
export function deleteArticle(articleId, userId) {
  const params = { articleId };
  if (userId) {
    params.userId = userId;
  }
  
  return request({
    url: '/article',
    method: 'delete',
    params
  });
}

/**
 * 点赞文章
 * @param {number|string} id 文章ID
 * @returns {Promise} 请求Promise
 */
export function likeArticle(id) {
  return request({
    url: `/article/like/${id}`,
    method: 'post'
  });
}

/**
 * 收藏文章
 * @param {number|string} id 文章ID
 * @returns {Promise} 请求Promise
 */
export function collectArticle(id) {
  return request({
    url: `/article/collect/${id}`,
    method: 'post'
  });
}

/**
 * 取消收藏文章
 * @param {number|string} id 文章ID
 * @returns {Promise} 请求Promise
 */
export function uncollectArticle(id) {
  return request({
    url: `/article/uncollect/${id}`,
    method: 'post'
  });
}

/**
 * 获取标签列表
 * @returns {Promise} 请求Promise
 */
export function getTagList() {
  return request({
    url: '/article/tags',
    method: 'get'
  });
}

/**
 * 获取用户的文章列表
 * @param {Object} data 请求数据，包含用户ID、标题、分类等信息
 * @param {string} [data.sortField='createTime'] 排序字段
 * @param {string} [data.sortOrder='desc'] 排序方式，desc-降序，asc-升序
 * @returns {Promise} 请求Promise
 */
export function getUserArticleList(data) {
  // 默认按创建时间降序排列
  const params = {
    sortField: 'createTime',
    sortOrder: 'desc',
    ...data
  };

  return request({
    url: '/article/list',
    method: 'get',
    params
  });
}

/**
 * 添加点赞
 * @param {number|string} articleId 文章ID
 * @returns {Promise} 请求Promise
 */
export function addArticleLike(articleId) {
  return request({
    url: `/articleUserLike/add?articleId=${articleId}`,
    method: 'post'
  });
}

/**
 * 获取用户点赞的文章列表
 * @returns {Promise} 请求Promise
 */
export function getUserLikedArticles() {
  return request({
    url: '/articleUserLike/findByUserId',
    method: 'get'
  });
}

/**
 * 取消点赞
 * @param {number|string} articleId 文章ID
 * @returns {Promise} 请求Promise
 */
export function deleteArticleLike(articleId) {
  return request({
    url: `/articleUserLike/delete?articleId=${articleId}`,
    method: 'post'
  });
} 