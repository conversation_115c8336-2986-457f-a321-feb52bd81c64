import request from '@/utils/request'

// 创建支付订单
export const createWechatOrder = (data) => {
  return request({
    url: `/order/create`,
    method: 'post',
    data
  });
};
//生成二维码
export const createWechatQrcode = (orderNo) => {
  return request({
    url: `/wxPay/createWxPayQRCode/${orderNo}`,
    method: 'post'
  })
}
// 查询支付状态
export const queryPayStatus = (orderNo) => {
  return request({
    url: `/wxPay/queryOrderStatus/${orderNo}`,
    method: 'get'
  });
};
//订单失效
export const putinvalidateOrder = (orderNo) => {
  return request({
    url: `/wxPay/cancel?orderNo=${orderNo}`,
    method: 'put'
  })
}
// 获取用户支付记录
export const getUserPaymentRecords = (params) => {
  return request({
    url: `/order/list`,
    method: 'get',
    params
  })
}

//重新查询会员状态
export const getmembertype = ()=>{
  return request({
    url:'/userMember/findUserMemberType',
    method:'get'
  })
}

//获取用户会员过期时间
export const getUserMemberExpiryTime = ()=>{
  return request({
    url:'/userMember/findUserMemberInfo',
    method:'get'
  })
}

//获取用户会员信息（包含开通的站点信息）
export const getUserMemberInfo = ()=>{
  return request({
    url:'/userMember/findUserMemberInfo',
    method:'get'
  })
}

//获取指定用户ID的会员信息
export const getUserMemberTypeById = (userId)=>{
  return request({
    url:'/userMember/findUserMemberType',
    method:'get',
    params: {
      reqUserId: userId
    }
  })
}

export const getYnghan = ()=>{
  return request({
    url:'/corpPaymentInfo/list',
    method:'get'
  })
}