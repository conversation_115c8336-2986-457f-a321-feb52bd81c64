import * as THREE from 'three'
import { projection } from './projection'

export class Country {
  constructor(geometry, properties) {
    this.geoCoords = geometry
    this.properties = properties
    this.LAYER_HEIGHT = 0.8  // 调整每层高度，使整体更加扁平
    this.simplificationLevel = 1 // 简化级别 - 1表示全分辨率，>1表示按比例减少点数
  }

  // 添加简化几何体的方法
  simplifyCoordinates(coordinates, level) {
    if (level <= 1) return coordinates;
    
    // 简单的抽样简化 - 每level个点取一个
    return coordinates.filter((_, index) => index % level === 0);
  }

  createShape(layer = 0, distanceToCamera = 0) {
    const shapes = []
    
    // 基于与相机的距离选择合适的简化级别
    let simplificationFactor = this.simplificationLevel;
    if (distanceToCamera > 300) {
      simplificationFactor = 5; // 远距离使用更高的简化
    } else if (distanceToCamera > 200) {
      simplificationFactor = 3; // 中等距离使用中等简化
    } else if (distanceToCamera > 100) {
      simplificationFactor = 2; // 近距离使用轻微简化
    }
    
    const createShape = (coordinates) => {
      const shape = new THREE.Shape()
      
      // 简化坐标点
      const simplifiedCoords = this.simplifyCoordinates(coordinates, simplificationFactor);
      
      simplifiedCoords.forEach((point, index) => {
        const [x, y] = projection(point)
        
              // 添加1/6的缩放因子（原来的1/3再缩小一倍）
      const scaleFactor = 1/6;
      const scaledX = x * scaleFactor;
      const scaledY = y * scaleFactor;
      
      // 特殊处理俄罗斯等大型国家
      const isRussia = this.properties && (this.properties.NAME === "Russia" || this.properties.name === "Russia");
        
        if (index === 0) {
          shape.moveTo(scaledX, -scaledY)  // 使用缩放后的坐标
        } else {
          shape.lineTo(scaledX, -scaledY)
        }
      })
      
      shape.closePath()
      return shape
    }

    if (this.geoCoords.type === "MultiPolygon") {
      this.geoCoords.coordinates.forEach(poly => {
        shapes.push(createShape(poly[0]))
      })
    } else {
      this.geoCoords.coordinates.forEach(poly => {
        shapes.push(createShape(poly))
      })
    }

    // 针对不同距离优化挤出设置
    const extrudeSettings = {
      depth: this.LAYER_HEIGHT,  // 使用调整后的层高
      bevelEnabled: distanceToCamera < 200, // 远距离时关闭斜角
      bevelThickness: distanceToCamera < 200 ? 0.05 : 0,
      bevelSize: distanceToCamera < 200 ? 0.03 : 0,
      bevelOffset: 0,
      bevelSegments: distanceToCamera < 200 ? 1 : 0
    }

    const geometry = new THREE.ExtrudeGeometry(shapes, extrudeSettings)
    
    // 根据距离使用不同的材质
    let material;
    // 统一使用MeshBasicMaterial，不再根据距离区分材质类型
    material = new THREE.MeshBasicMaterial({
      transparent: false,  // 关闭透明，确保颜色纯净
      opacity: 1.0,        // 完全不透明
      color: 0x90C8F4      // 直接在这里设置颜色
    });

    const mesh = new THREE.Mesh(geometry, material)
    mesh.position.z = layer * this.LAYER_HEIGHT
    mesh.userData.layer = layer
    mesh.userData.properties = this.properties
    
    // 启用视锥剔除，不渲染视野外的物体
    mesh.frustumCulled = true;
    
    // 减少几何体内存占用
    geometry.dispose();
    
    return mesh
  }

  createLine(distanceToCamera = 0) {
    // 如果距离太远，返回一个空数组，不创建线
    if (distanceToCamera > 300) {
      return null;
    }
    
    const lines = []
    
    // 基于与相机的距离选择合适的简化级别
    let simplificationFactor = 1;
    if (distanceToCamera > 200) {
      simplificationFactor = 4;
    } else if (distanceToCamera > 100) {
      simplificationFactor = 2;
    }
    
    if (this.geoCoords.type === 'Polygon') {
      const coordinates = this.geoCoords.coordinates[0];
      // 简化坐标点
      const simplifiedCoords = this.simplifyCoordinates(coordinates, simplificationFactor);
      
      for (let i = 0; i < simplifiedCoords.length - 1; i++) {
        const [x1, y1] = projection(simplifiedCoords[i])
        const [x2, y2] = projection(simplifiedCoords[i + 1])
        
        // 添加1/6的缩放因子（原来的1/3再缩小一倍）
        const scaleFactor = 1/6;
        const scaledX1 = x1 * scaleFactor;
        const scaledY1 = y1 * scaleFactor;
        const scaledX2 = x2 * scaleFactor;
        const scaledY2 = y2 * scaleFactor;
        
        // 将Z坐标设为0，不再硬编码为0.02
        lines.push(scaledX1, -scaledY1, 0)  // 移除Z轴固定偏移
        lines.push(scaledX2, -scaledY2, 0)
      }
    } else if (this.geoCoords.type === 'MultiPolygon') {
      // 处理每个多边形的外层边界
      this.geoCoords.coordinates.forEach(polygon => {
        const coordinates = polygon[0];
        // 简化坐标点
        const simplifiedCoords = this.simplifyCoordinates(coordinates, simplificationFactor);
        
        for (let i = 0; i < simplifiedCoords.length - 1; i++) {
          // 直接使用原始经纬度坐标
          const [lng1, lat1] = simplifiedCoords[i]
          const [lng2, lat2] = simplifiedCoords[i + 1]
          // 转换为墨卡托投影
          const [x1, y1] = projection([lng1, lat1])
          const [x2, y2] = projection([lng2, lat2])
          
          // 添加1/6的缩放因子（原来的1/3再缩小一倍）
          const scaleFactor = 1/6;
          const scaledX1 = x1 * scaleFactor;
          const scaledY1 = y1 * scaleFactor;
          const scaledX2 = x2 * scaleFactor;
          const scaledY2 = y2 * scaleFactor;
          
          // 将Z坐标设为0，不再硬编码为0.02
          lines.push(scaledX1, -scaledY1, 0)  // 移除Z轴固定偏移
          lines.push(scaledX2, -scaledY2, 0)
        }
      })
    }

    // 如果没有线段，返回null
    if (lines.length === 0) {
      return null;
    }

    const geometry = new THREE.BufferGeometry()
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(lines, 3))

    const material = new THREE.LineBasicMaterial({
      color: 0xFFFFFF,  // 纯白色
      transparent: true,
      opacity: 0.9,     // 增加不透明度
      depthTest: false, // 禁用深度测试，避免透明度与深度测试冲突
      depthWrite: false, // 不写入深度缓冲区
      renderOrder: 1500  // 设置合理的渲染顺序，只需比地图顶层稍高
    })

    const line = new THREE.LineSegments(geometry, material);
    // 设置线条宽度
    line.material.linewidth = 1.5;
    // 确保线条在最上层显示
    line.renderOrder = 2000;
    // 禁用视锥剔除，确保始终渲染
    line.frustumCulled = false;

    return line;
  }
}

// 在 Earth3D.vue 中的 loadMapData 函数里修改边线位置：
const loadMapData = async () => {
  try {
    // ...existing code...
    features.features.forEach(feature => {
      const country = new Country(feature.geometry, feature.properties)
      const shapes = [0, 1, 2].map(layer => country.createShape(layer))
      const line = country.createLine()
      
      // 设置线的位置为第三层高度加上微小偏移
      const TOP_LAYER_HEIGHT = country.LAYER_HEIGHT * 2
      line.position.z = TOP_LAYER_HEIGHT + 0.001  // 确保贴合第三层表面
      line.renderOrder = 4
      line.material.opacity = 0.6
      line.material.linewidth = 1

      // ...existing code...
    })
    // ...existing code...
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}
