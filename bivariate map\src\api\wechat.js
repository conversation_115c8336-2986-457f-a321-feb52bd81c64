import request from '../utils/request'

// 获取微信登录二维码
export function getQrCode() {
  return request({
    url: '/wechat/login-url',
    method: 'get'
  })
}
export function getQrCodeByState(state) {
  return request({
    url: '/wechat/login-url?token=' + state + '&isBind=true',
    method: 'get'
  })
}


// 检查扫码状态
export function checkScanStatus(uuid) {
  return request({
    url: '/wechat/callback',
    method: 'get',
    params: { uuid }
  })
}

// 微信登录
export function wechatLogin(openId) {
  return request({
    url: '/wechat/loginByOpenId?openId=' + openId,

  })
}

