<template>
  <div class="create-post">
    <!-- 顶部标题栏 - 移除fixed定位 -->
    <header class="header">
      <div class="header-left">
        <img src="../../public/image/newMaptitlelogo.png" alt="New Map" class="logo-image">
      </div>
      <div class="header-center">
        <h1 class="title">{{ isEditMode ? t('createPost.editTitle') : t('createPost.title') }}</h1>
      </div>
      <div class="header-right">
        <!-- <button class="language-button" @click="toggleLanguage">
          {{ language === 'zh' ? 'EN' : '中文' }}
        </button> -->
        <button class="exit-button" @click="goBack">
          <span class="exit-icon">×</span>
          <span>{{ t('createPost.back') }}</span>
        </button>
      </div>
    </header>

    <!-- 加载中覆盖层 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ t('createPost.loading') }}</div>
      </div>
    </div>

    <!-- 发布表单 -->
    <div class="post-form">
      <!-- 标题输入 -->
      <div class="form-group" :class="{ 'focused': titleFocused }">
        <div class="form-label">
          <span class="label-icon">📝</span>
          {{ t('createPost.form.titleLabel') }}
        </div>
        <div class="form-input-container">
          <input v-model="postTitle" :placeholder="t('createPost.form.titlePlaceholder')" class="post-title-input"
            @focus="titleFocused = true" @blur="titleFocused = false" @click="isEditMode && $event.target.select()">
          <div class="character-count" :class="{ 'warning': postTitle.length > 15 }">
            {{ postTitle.length }}/20
          </div>
        </div>
      </div>

      <!-- 内容输入 -->
      <div class="form-group" :class="{ 'focused': contentFocused }">
        <div class="form-label">
          <span class="label-icon">📄</span>
          {{ t('createPost.form.contentLabel') }}
        </div>
        <div class="form-input-container">
          <textarea v-model="postContent" :placeholder="t('createPost.form.contentPlaceholder')"
            @input="adjustTextareaHeight" @focus="contentFocused = true" @blur="contentFocused = false"
            ref="postTextarea" class="post-textarea" @click="isEditMode && $event.target.select()"></textarea>
        </div>
      </div>

      <!-- 图片上传 - 不再强制要求 -->
      <div class="form-group">
        <div class="form-label">
          <span class="label-icon">🖼️</span>
          {{ t('createPost.form.uploadLabel') }}
          <span class="label-note">{{ t('createPost.form.uploadOptional') }}</span>
        </div>

        <div class="image-upload-area">
          <template v-for="(image, index) in 3">
            <div v-if="index < uploadedImages.length" :class="['image-upload-item', 'has-image',
              { 'uploading': index === uploadingIndex },
              { 'has-error': uploadedImages[index] && uploadedImages[index].hasError }]"
              :key="'uploaded-' + index">
              <template
                v-if="index === uploadingIndex || (uploadedImages[index] && uploadedImages[index].isConverting)">
                <div class="upload-loading">
                  <div class="loading-spinner"></div>
                </div>
                <div v-if="uploadedImages[index] && uploadedImages[index].isConverting" class="converting-text">{{
                  t('createPost.form.converting') }}</div>
              </template>
              <template v-else-if="uploadedImages[index] && uploadedImages[index].hasError">
                <div class="error-image-container">
                  <svg class="error-image" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <div class="error-message">{{ uploadedImages[index].errorMessage || t('createPost.form.uploadFailed') }}
                </div>
              </template>
              <img v-else :src="uploadedImages[index].preview" class="preview-image" alt="上传的图片"
                @click="toggleImageUpload(index)" @dblclick.stop="previewImage(uploadedImages[index])">
              <div class="image-actions">
                <button class="image-action-btn delete-btn" @click.stop="removeImage(index)" aria-label="删除图片">
                  <i class="el-icon-delete">×</i>
                </button>
              </div>
            </div>
            <div v-else class="image-upload-item" @click="toggleImageUpload(index)" :key="'new-' + index">
              <div class="upload-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
                <span class="upload-text">{{ t('createPost.form.addImage') }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 标签选择 -->
      <div class="form-group">
        <div class="form-label">
          <span class="label-icon">🏷️</span>
          {{ t('createPost.form.tagsLabel') }}
          <span class="label-note">{{ t('createPost.form.tagsOptional') }}</span>
          <span v-if="selectedTags.length > 0" class="selected-count">{{ t('createPost.form.tagsSelected', {
            count:
              selectedTags.length }) }}</span>
        </div>
        <div class="tags-container">
          <div v-for="(tag, index) in tagList" :key="index" class="tag-item"
            :class="{ 'active': selectedTags.includes(tag.name) }" @click="toggleTag(tag.name)">
            <span class="tag-text">{{ tag.name }}</span>
            <span v-if="selectedTags.includes(tag.name)" class="tag-check">✓</span>
          </div>
          <div v-if="tagList.length === 0" class="no-tags-message">{{ t('createPost.form.noTags') }}</div>
          <div v-else-if="selectedTags.length === 0" class="tag-selection-hint">{{ t('createPost.form.tagsHint') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <!-- 取消编辑按钮，仅在编辑模式下显示 -->
      <button v-if="isEditMode" class="cancel-btn" @click="cancelEdit">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
        <span>{{ t('createPost.actions.cancelEdit') }}</span>
      </button>

      <!-- 提交按钮 -->
      <button class="publish-btn" :class="{ 'active': isFormValid }" :disabled="!isFormValid" @click="handleSubmit">
        <span v-if="!publishing">{{ isEditMode ? t('createPost.actions.saveChanges') : t('createPost.actions.publish')
          }}</span>
        <span v-else class="publishing">
          <div class="publish-spinner"></div>
          {{ isEditMode ? t('createPost.actions.saving') : t('createPost.actions.publishing') }}
        </span>
      </button>
    </div>

    <!-- 水印预览 (隐藏，仅用于生成水印) -->
    <div class="watermark-container" ref="watermarkContainer" style="display: none;">
      <div class="watermark-content">
        <img src="../../public/image/newmap.png" alt="New Map" class="watermark-logo">
      </div>
    </div>

    <!-- 文件上传输入框 -->
    <input type="file" ref="fileInput" style="display: none" accept="image/*" @change="handleImageUpload">

    <!-- 图片预览 -->
    <div v-if="previewState.show" class="preview-container">
      <div class="preview-overlay" @click="closePreview"></div>
      <div class="preview-content">
        <img :src="previewState.currentImage" alt="图片预览">
        <button class="preview-close" @click="closePreview">×</button>
      </div>
    </div>

    <!-- 会员提示弹窗 -->
    <div v-if="showMembershipModal" class="modal-overlay" @click.self="showMembershipModal = false">
      <div class="membership-modal">
        <div class="modal-header">
          <h3>{{ t('createPost.membership.title') }}</h3>
          <button class="close-btn" @click="showMembershipModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>{{ t('createPost.membership.description1') }}</p>
          <p>{{ t('createPost.membership.description2') }}</p>
          <div class="benefits-list">
            <div class="benefit-item">{{ t('createPost.membership.benefits.posting') }}</div>
            <div class="benefit-item">{{ t('createPost.membership.benefits.vouchers') }}</div>
            <div class="benefit-item">{{ t('createPost.membership.benefits.support') }}</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn-secondary" @click="handleMembershipModal('cancel')">
            {{ t('createPost.membership.cancel') }}
          </button>
          <button class="btn-primary" @click="handleMembershipModal('confirm')">
            {{ t('createPost.membership.confirm') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 优惠券弹窗 -->
    <div v-if="showVoucherModal && voucherData" class="modal-overlay">
      <div class="voucher-modal">
        <div class="modal-header">
          <h3>{{ t('createPost.voucher.title') }}</h3>
        </div>
        <div class="modal-body">
          <div class="voucher-card">
            <div class="voucher-amount">
              <span class="currency">¥</span>
              <span class="value">{{ voucherData.data?.金额 || 0 }}</span>
            </div>
            <div class="voucher-info">
              <div class="voucher-title">{{ t('createPost.voucher.type') }}</div>
              <div class="voucher-validity">
                {{ t('createPost.voucher.validity', { days: voucherData.data?.有效期 || 1 }) }}
              </div>
              <div class="voucher-desc">
                {{ voucherData.data?.message || '' }}
              </div>
            </div>
          </div>
          <p class="congratulation">{{ t('createPost.voucher.success') }}</p>
        </div>
        <div class="modal-footer">
          <button class="btn-secondary" @click="handleVoucherModal('later')">
            {{ t('createPost.voucher.later') }}
          </button>
          <button class="btn-primary" @click="handleVoucherModal('use')">
            {{ t('createPost.voucher.useNow') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { publishArticle, getArticleDetail, updateArticle, getTag } from '@/api/SmallSquare';
import { getmembertype } from '@/api/pay';
import { getvoucher } from '@/api/voucher';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/user';
import { useI18n } from '@/composables/useI18n';

// Get the i18n composable
const { t, currentLocale } = useI18n();

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 判断是否为编辑模式
const articleId = ref(null);
const isEditMode = computed(() => !!articleId.value);

const postTitle = ref('');
const postContent = ref('');
const postTextarea = ref(null);
const fileInput = ref(null);
const uploadedImages = ref([]);
const selectedTags = ref([]);
const currentUploadIndex = ref(0);
const watermarkContainer = ref(null);
const originalImageIds = ref([]); // 存储原始图片ID，用于编辑时保留图片
const isConvertingImages = ref(false);

// 会员验证和优惠券相关状态
const showMembershipModal = ref(false); // 显示会员提示弹窗
const showVoucherModal = ref(false); // 显示优惠券弹窗
const voucherData = ref(null); // 优惠券数据
const memberType = ref(null); // 用户会员类型
const isMember = ref(false); // 是否为会员

// 新增状态
const titleFocused = ref(false);
const contentFocused = ref(false);
const uploadingIndex = ref(-1);
const publishing = ref(false);
const loading = ref(false); // 加载状态

// 添加图片预览状态
const previewState = ref({
  show: false,
  currentImage: null
});

// 标签数据
const tagList = ref([]);

// 表单验证 - 图片和标签都不再是必需的
const isFormValid = computed(() => {
  return postTitle.value.trim().length > 0 &&
    postContent.value.trim().length > 0; // 只需要标题和内容
});

// 调整文本框高度
const adjustTextareaHeight = () => {
  const textarea = postTextarea.value;
  if (!textarea) return;
  textarea.style.height = 'auto';
  textarea.style.height = textarea.scrollHeight + 'px';
};

// 切换标签选择
const toggleTag = (tagName) => {
  const index = selectedTags.value.indexOf(tagName);
  if (index === -1) {
    selectedTags.value.push(tagName);
  } else {
    selectedTags.value.splice(index, 1);
  }
};

// 处理返回按钮
const goBack = () => {
  // 检查来源路径，如果是从用户中心来的，则返回用户中心的咨询部分
  const fromUserCenter = route.query.from === 'userinfo';
  if (fromUserCenter) {
    router.push('/userinfo?activeTab=consult');
  } else {
    router.push('/small-square');
  }
};

// 取消编辑
const cancelEdit = () => {
  // 返回个人中心并默认选择"我发布的咨询"部分
  router.push('/userinfo?activeTab=consult');
};

// 图片上传相关
const toggleImageUpload = (index) => {
  if (uploadingIndex.value !== -1) return; // 如果正在上传，不允许新的上传

  // 检查是否超过最大图片数量
  if (index >= uploadedImages.value.length && uploadedImages.value.length >= 3) {
    ElMessage.warning(t('createPost.imageLimit'));
    return;
  }

  currentUploadIndex.value = index;
  fileInput.value?.click();
};

// 移除图片
const removeImage = (index) => {
  // 如果是原始图片，记录其ID以便在保存时通知后端删除
  if (uploadedImages.value[index].isOriginal) {
    const originalId = Number(uploadedImages.value[index].originalId);

    // 从originalImageIds列表中移除该ID
    const idIndex = originalImageIds.value.indexOf(originalId);
    if (idIndex !== -1) {
      originalImageIds.value.splice(idIndex, 1);
    } else {
      // 如果没有找到完全匹配的ID，尝试查找相同数值的ID
      const numericIdIndex = originalImageIds.value.findIndex(id => Number(id) === originalId);
      if (numericIdIndex !== -1) {
        originalImageIds.value.splice(numericIdIndex, 1);
      }
    }
  }

  uploadedImages.value.splice(index, 1);
};

// 处理表单提交
const handleSubmit = async () => {
  if (!isFormValid.value || publishing.value) return;

  console.log('=== 开始发布流程 ===');
  console.log('用户信息:', userStore.userInfo);
  console.log('会员状态:', { memberType: memberType.value, isMember: isMember.value });

  // 检查会员身份
  if (!isMember.value) {
    // 非会员，显示提示弹窗
    console.log('用户非会员，显示会员提示弹窗');
    showMembershipModal.value = true;
    return;
  }

  // 是会员，继续发布流程
  console.log('用户是会员，继续发布流程');
  if (isEditMode.value) {
    await updatePost();
  } else {
    await publishPost();
  }
};

// 处理图片上传
const handleImageUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 如果已经上传了3张图片，则不再上传
  if (uploadedImages.value.length >= 3) {
    ElMessage.warning('最多只能上传3张图片');
    return;
  }

  uploadingIndex.value = currentUploadIndex.value;

  try {
    // 读取图片并创建预览
    const preview = await readFileAsDataURL(file);

    // 添加水印
    const watermarked = await addWatermark(preview);

    // 使用requestAnimationFrame优化UI更新
    requestAnimationFrame(async () => {
      // 如果是替换已有图片
      if (currentUploadIndex.value < uploadedImages.value.length) {
        // 如果要替换的图片是原始图片，记录其ID以便在保存时通知后端删除
        if (uploadedImages.value[currentUploadIndex.value].isOriginal) {
          const originalId = Number(uploadedImages.value[currentUploadIndex.value].originalId);

          // 从originalImageIds列表中移除该ID
          const idIndex = originalImageIds.value.indexOf(originalId);
          if (idIndex !== -1) {
            originalImageIds.value.splice(idIndex, 1);
          } else {
            // 如果没有找到完全匹配的ID，尝试查找相同数值的ID
            const numericIdIndex = originalImageIds.value.findIndex(id => Number(id) === originalId);
            if (numericIdIndex !== -1) {
              originalImageIds.value.splice(numericIdIndex, 1);
            }
          }
        }

        // 使用nextTick确保DOM更新顺畅
        await nextTick();

        // 替换现有图片
        uploadedImages.value[currentUploadIndex.value] = {
          preview,
          watermarked,
          file,
          isOriginal: false,
          fileName: file.name
        };
      } else {
        // 添加新图片
        uploadedImages.value.push({
          preview,
          watermarked,
          file,
          isOriginal: false,
          fileName: file.name
        });
      }

      uploadingIndex.value = -1;
    });
  } catch (error) {
    ElMessage.error('图片处理失败，请重试');
    uploadingIndex.value = -1;
  }

  // 清空input，允许重复选择同一文件
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 读取文件为DataURL
const readFileAsDataURL = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(new Error('读取文件失败'));

    reader.readAsDataURL(file);
  });
};

// 发布新帖子
const publishPost = async () => {
  publishing.value = true;

  // 图片和标签现在都是可选的，不需要验证

  // 获取标签ID列表
  const tagIds = getTagIds();

  // 构建符合后端 AddArticleDto 的 dto 对象
  const dto = {
    title: postTitle.value,
    content: postContent.value,
    location: '', // 可选，如果有位置信息可以添加
    categoryId: 1, // 使用默认分类ID
    tagIds: tagIds.length > 0 ? tagIds : [], // 使用标签ID列表，即使为空也发送空数组
    originalImageIds: [] // 发布新文章时不需要原始图片ID
  };

  // 收集所有图片数据
  const images = uploadedImages.value
    .filter(img => img.watermarked && !img.hasError) // 过滤掉错误的图片
    .map(img => img.watermarked);

  try {
    // 调用API发布文章，传递符合新格式的参数
    const response = await publishArticle({ dto, images });

    if (response.code === 200) {
      ElMessage.success('发布成功');
      // 检查来源路径，如果是从用户中心来的，则返回用户中心的咨询部分
      const fromUserCenter = route.query.from === 'userinfo';
      if (fromUserCenter) {
        router.push('/userinfo?activeTab=consult');
      } else {
        router.back();
      }
    } else {
      ElMessage.error(response.msg || '发布失败');
    }
  } catch (error) {
    ElMessage.error('发布失败，请稍后重试');
  } finally {
    publishing.value = false;
  }
};

// 更新文章
const updatePost = async () => {
  publishing.value = true;

  try {
    // 图片和标签现在都是可选的，不需要验证

    // 获取标签ID列表
    const tagIds = getTagIds();

    // 注释：编辑模式下，所有图片（包括原始图片转换后的base64）都作为新图片发送给后端

    // 构建符合后端 AddArticleDto 的 dto 对象
    const dto = {
      id: articleId.value,
      title: postTitle.value,
      content: postContent.value,
      location: '', // 可选，如果有位置信息可以添加
      categoryId: 1, // 使用默认分类ID
      tagIds: tagIds.length > 0 ? tagIds : [], // 使用标签ID列表，即使为空也发送空数组
      originalImageIds: [] // 编辑模式下不需要传递原始图片ID，因为所有图片都作为新图片发送
    };

    // 收集所有图片的数据（包括转换后的原始图片和新上传的图片）
    const images = uploadedImages.value
      .filter(img => img.watermarked && !img.hasError) // 过滤掉错误的图片
      .map(img => img.watermarked);

    console.log('=== 更新文章调试信息 ===');
    console.log('总图片数量:', uploadedImages.value.length);
    console.log('发送给后端的图片数量:', images.length);
    console.log('========================');

    // 确保dto中的originalImageIds是数组类型
    if (!Array.isArray(dto.originalImageIds)) {
      dto.originalImageIds = dto.originalImageIds ? [dto.originalImageIds] : [];
    }

    const response = await updateArticle({ dto, images });

    if (response.code === 200) {
      ElMessage.success('更新成功');
      // 检查来源路径，如果是从用户中心来的，则返回用户中心的咨询部分
      const fromUserCenter = route.query.from === 'userinfo';
      if (fromUserCenter) {
        router.push('/userinfo?activeTab=consult');
      } else {
        router.back();
      }
    } else {
      ElMessage.error(response.msg || '更新失败');
    }
  } catch (error) {
    ElMessage.error('更新失败，请稍后重试');
  } finally {
    publishing.value = false;
  }
};

// 添加水印
const addWatermark = (imageDataUrl) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // 设置画布大小为图片大小
      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制原图
      ctx.drawImage(img, 0, 0);

      // 绘制水印
      if (watermarkContainer.value) {
        // 设置水印透明度
        ctx.globalAlpha = 0.8; // 设置透明度为80%

        // 获取水印元素
        const watermarkContent = watermarkContainer.value.querySelector('.watermark-content');

        // 将水印元素转为图片
        createWatermarkImage(watermarkContent).then(watermarkImg => {
          // 创建水印图案
          const pattern = createWatermarkPattern(ctx, watermarkImg, 30); // 30度角倾斜

          // 使用水印图案填充整个画布
          ctx.fillStyle = pattern;
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          


          // 返回带水印的图片
          resolve(canvas.toDataURL('image/jpeg', 0.95)); // 保持高质量
        });
      } else {
        // 如果水印容器不存在，添加简单的水印

        
        resolve(canvas.toDataURL('image/jpeg', 0.95)); // 保持高质量
      }
    };
    img.src = imageDataUrl;
  });
};

// 创建水印图像
const createWatermarkImage = (element) => {
  return new Promise((resolve) => {
    // 创建一个新的canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // 设置canvas大小
    canvas.width = 200;
    canvas.height = 80;

    // 使背景透明
    ctx.fillStyle = 'rgba(255, 255, 255, 0)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 尝试从DOM中直接获取新的水印图片
    const logoImgElement = element.querySelector('.watermark-logo');

    if (logoImgElement && logoImgElement.complete && logoImgElement.naturalWidth !== 0) {
      // 如果DOM中的图片已加载完成，直接使用
      // 绘制新的水印图片，居中显示
      const imgWidth = 180;
      const imgHeight = 60;
      const x = (canvas.width - imgWidth) / 2;
      const y = (canvas.height - imgHeight) / 2;
      ctx.drawImage(logoImgElement, x, y, imgWidth, imgHeight);

      // 创建水印图像
      const watermarkImg = new Image();
      watermarkImg.src = canvas.toDataURL('image/png');
      watermarkImg.onload = () => resolve(watermarkImg);
    } else {
      // 如果DOM中的图片未加载或不存在，加载新图片
      const logoImg = new Image();
      logoImg.onload = () => {
        // 绘制新的水印图片，居中显示
        const imgWidth = 180;
        const imgHeight = 60;
        const x = (canvas.width - imgWidth) / 2;
        const y = (canvas.height - imgHeight) / 2;
        ctx.drawImage(logoImg, x, y, imgWidth, imgHeight);

        // 创建水印图像
        const watermarkImg = new Image();
        watermarkImg.src = canvas.toDataURL('image/png');
        watermarkImg.onload = () => resolve(watermarkImg);
      };

      // 尝试多种可能的路径
      logoImg.src = '/image/newmap.png'; // 尝试绝对路径

      // 如果加载失败，使用备用方案
      logoImg.onerror = () => {
        // 再尝试一次相对路径
        logoImg.src = '../../public/image/newmap.png';

        // 如果再次失败，使用备用绘制方案
        logoImg.onerror = () => {
          // 绘制备用文字
          ctx.fillStyle = '#8a6df7'; // 紫色，与图片中的颜色匹配
          ctx.font = 'bold 24px Arial';
          ctx.textAlign = 'center';
          ctx.fillText('YIYI Map', canvas.width / 2, canvas.height / 2);

          // 创建水印图像
          const watermarkImg = new Image();
          watermarkImg.src = canvas.toDataURL('image/png');
          watermarkImg.onload = () => resolve(watermarkImg);
        };
      };
    }
  });
};



// 创建斜向水印图案
const createWatermarkPattern = (ctx, watermarkImg, angle) => {
  // 创建临时画布用于生成图案
  const patternCanvas = document.createElement('canvas');
  const patternCtx = patternCanvas.getContext('2d');

  // 设置图案画布大小，足够容纳旋转后的水印
  const size = Math.max(watermarkImg.width, watermarkImg.height) * 4;
  patternCanvas.width = size;
  patternCanvas.height = size;

  // 清除画布
  patternCtx.clearRect(0, 0, size, size);

  // 将坐标原点移到画布中心
  patternCtx.translate(size / 2, size / 2);

  // 旋转画布
  patternCtx.rotate((angle * Math.PI) / 180);

  // 绘制水印，密集度60%（间距为水印尺寸的1.67倍）
  const spacing = Math.max(watermarkImg.width, watermarkImg.height) * 1.67;

  // 计算需要绘制的水印数量，确保覆盖整个区域
  const count = Math.ceil(size / spacing) * 2;

  // 从左上角开始，绘制水印网格
  for (let x = -count; x <= count; x++) {
    for (let y = -count; y <= count; y++) {
      patternCtx.drawImage(
        watermarkImg,
        x * spacing - watermarkImg.width / 2,
        y * spacing - watermarkImg.height / 2,
        watermarkImg.width,
        watermarkImg.height
      );
    }
  }

  // 创建并返回图案
  return ctx.createPattern(patternCanvas, 'repeat');
};

// 预览图片
const previewImage = (image) => {
  if (!image) return;
  previewState.value.show = true;
  previewState.value.currentImage = image.preview;
};

// 关闭预览
const closePreview = () => {
  previewState.value.show = false;
  previewState.value.currentImage = null;
};

// 处理会员提示弹窗
const handleMembershipModal = async (action) => {
  if (action === 'confirm') {
    // 用户确认要成为会员，发放优惠券
    showMembershipModal.value = false;
    await issueCoupon();
  } else {
    // 用户取消
    showMembershipModal.value = false;
  }
};

// 处理优惠券弹窗
const handleVoucherModal = (action) => {
  if (action === 'use') {
    // 立即使用优惠券，跳转到会员页面
    showVoucherModal.value = false;
    router.push('/member');
  } else {
    // 稍后查看
    showVoucherModal.value = false;
  }
};

// 已经移除了加载分类的方法，使用fetchTagList代替

// 获取标签列表 - 与SmallSquare页面类似
const fetchTagList = async () => {
  try {
    const response = await getTag();
    if (response && response.code === 200 && response.rows && response.rows.length > 0) {
      tagList.value = response.rows;
      console.log('获取到的标签列表:', tagList.value);
    } else {
      console.warn('标签列表为空或获取失败');
      // 使用默认标签
      tagList.value = [
        { name: '铁路', tid: 1 },
        { name: '汽运', tid: 2 },
        { name: '地区', tid: 3 }
      ];
    }
  } catch (error) {
    console.error('获取标签列表失败:', error);
    // 使用默认标签
    tagList.value = [
      { name: '铁路', tid: 1 },
      { name: '汽运', tid: 2 },
      { name: '地区', tid: 3 }
    ];
  }
};

// 初始化
onMounted(async () => {
  // 检查是否为编辑模式
  if (route.query.id) {
    articleId.value = Number(route.query.id);
    loading.value = true;
    await loadArticleDetail(articleId.value);
    loading.value = false;
  }

  // 获取标签列表
  await fetchTagList();

  // 检查会员状态
  await checkMembershipStatus();

  // 调整文本框高度
  nextTick(() => {
    adjustTextareaHeight();
  });
});

// 获取标签ID列表
const getTagIds = () => {
  if (!tagList.value || tagList.value.length === 0) {
    return [];
  }

  // 获取标签ID列表
  return selectedTags.value.map(tagName => {
    // 查找标签对应的ID
    const tag = tagList.value.find(t => t.name === tagName);
    // 如果找到标签，使用tid
    if (tag) {
      return tag.tid;
    } else {
      return null;
    }
  }).filter(id => id !== null); // 过滤掉无效的ID
};

// 检查用户会员身份
const checkMembershipStatus = async () => {
  try {
    const response = await getmembertype();
    if (response.code === 200) {
      memberType.value = response.data;
      console.log('用户会员类型:', memberType.value);

      // 检查是否为会员（普通会员或超级会员）
      if (memberType.value === '普通会员' || memberType.value === '超级会员') {
        isMember.value = true;
      } else {
        isMember.value = false;
      }
    } else {
      console.error('获取会员类型失败:', response.msg);
      isMember.value = false;
    }
  } catch (error) {
    console.error('检查会员身份失败:', error);
    isMember.value = false;
  }
};

// 发放优惠券
const issueCoupon = async () => {
  try {
    const userId = userStore.userInfo?.userId;
    if (!userId) {
      ElMessage.error(t('createPost.imageErrors.userInfoError'));
      return;
    }

    console.log('开始发放优惠券，用户ID:', userId);

    const response = await getvoucher({
      userId: userId,
      issuanceSource: '小广场', // 发布资讯来源，根据接口文档应该是数字1
    });

    console.log('优惠券接口响应:', response);

    if (response.code === 200) {
      voucherData.value = response;
      showVoucherModal.value = true;
      console.log('优惠券发放成功:', response.data);
      ElMessage.success(t('createPost.voucher.success'));
    } else {
      console.error('优惠券发放失败:', response.msg);
      // 不再显示优惠券发放失败的错误提示
    }
  } catch (error) {
    console.error('发放优惠券失败:', error);
    // 不再显示优惠券发放失败的错误提示
  }
};

// 加载文章详情
const loadArticleDetail = async (id) => {
  try {
    const response = await getArticleDetail(id);

    if (response.code === 200) {
      const article = response.data;

      // 设置文章信息
      postTitle.value = article.title || '';
      postContent.value = article.content || '';

      // 确保已加载标签数据
      await fetchTagList();

      // 设置标签 - 现在不需要考虑分类
      if (article.tags && article.tags.length > 0 && tagList.value && tagList.value.length > 0) {
        // 提取文章中已选中的标签tid列表
        const selectedTagTids = article.tags.map(tag => tag.tid);

        // 根据tid匹配找到对应的标签名称
        const matchedTagNames = [];
        selectedTagTids.forEach(tid => {
          const foundTag = tagList.value.find(tag => tag.tid === tid);
          if (foundTag) {
            matchedTagNames.push(foundTag.name);
          }
        });

        // 设置选中的标签
        selectedTags.value = matchedTagNames;
      }

      // 设置图片 - 处理现有图片
      if (article.articleImgList && article.articleImgList.length > 0) {
        // 清空当前图片列表，确保不会有重复
        uploadedImages.value = [];

        // 保存原始图片ID
        originalImageIds.value = article.articleImgList
          .filter(img => img && img.id)
          .map(img => Number(img.id));

        isConvertingImages.value = true;

        // 初始化处理的图片计数
        let errorCount = 0;

        // 加载图片预览并转换为base64
        for (const img of article.articleImgList) {
          if (!img.id || !img.imgUrl) {
            continue;
          }

          // 构建完整的图片URL，确保包含baseUrl
          const baseURL = import.meta.env.VITE_BASE_API || '';
          const imageUrl = img.imgUrl.startsWith('http') ? img.imgUrl : baseURL + img.imgUrl;

          try {
            // 先添加一个占位对象，显示加载中状态
            const imageIndex = uploadedImages.value.length;
            uploadedImages.value.push({
              preview: imageUrl,
              watermarked: imageUrl,
              originalId: Number(img.id),
              isOriginal: true,
              isConverting: true
            });

            // 尝试转换图片，最多重试2次
            let base64Data = null;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts && !base64Data) {
              try {
                attempts++;
                base64Data = await convertImageUrlToBase64(imageUrl);
              } catch (err) {
                // 最后一次尝试失败，抛出错误
                if (attempts >= maxAttempts) {
                  throw new Error(t('createPost.imageErrors.conversionMaxRetries'));
                }

                // 短暂延迟后重试
                await new Promise(resolve => setTimeout(resolve, 1000));
              }
            }

            // 更新占位对象为转换后的数据
            uploadedImages.value[imageIndex] = {
              preview: base64Data || imageUrl, // 如果转换失败，使用原始URL
              watermarked: base64Data || imageUrl, // 关键：这里存储转换后的base64数据
              originalId: Number(img.id),
              isOriginal: true,  // 标记为原始图片
              fileName: `original-image-${img.id}.jpg`
            };

          } catch (error) {
            errorCount++;

            // 将占位图片标记为错误状态
            const errorIndex = uploadedImages.value.findIndex(
              item => item.isConverting && item.originalId === Number(img.id)
            );

            if (errorIndex !== -1) {
              uploadedImages.value[errorIndex] = {
                preview: null, // 不使用图片URL，改用SVG
                watermarked: null,
                originalId: Number(img.id),
                isOriginal: true,
                hasError: true,
                errorMessage: t('createPost.form.uploadFailed')
              };
            }

            ElMessage.warning(`${t('createPost.imageErrors.processingFailed')}: ${img.imgUrl}`);
          }
        }

        isConvertingImages.value = false;

        if (errorCount > 0) {
          ElMessage({
            type: 'warning',
            message: `${errorCount}${t('createPost.imageErrors.conversionFailed')}`,
            duration: 5000
          });
        }
      }

      // 调整文本框高度
      setTimeout(adjustTextareaHeight, 100);

    } else {
      throw new Error(response.msg || '获取文章详情失败');
    }
  } catch (error) {
    ElMessage.error('获取文章详情失败，请重试');
  }
};

// 图片加载优化
const imagePreloader = new Map(); // 存储预加载的图片

// 优化图片加载函数
const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    // 如果已经预加载过该图片，直接返回
    if (imagePreloader.has(src)) {
      resolve(imagePreloader.get(src));
      return;
    }

    const img = new Image();
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      imagePreloader.set(src, img); // 缓存已加载的图片
      resolve(img);
    };

    img.onerror = (err) => {
      reject(new Error(t('createPost.form.uploadFailed')));
    };

    img.src = src;
  });
};

// 优化转换图片到Base64的函数
const convertImageUrlToBase64 = async (imageUrl) => {
  try {
    // 预加载图片以确保快速渲染
    const img = await preloadImage(imageUrl);

    // 使用离屏Canvas优化渲染性能
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d', { alpha: false }); // 禁用alpha通道，提升性能

    // 设置画布大小为图片大小
    canvas.width = img.width;
    canvas.height = img.height;

    // 将上下文缓存应用GPU加速
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 绘制图片到画布
    ctx.drawImage(img, 0, 0);

    // 使用优化的toDataURL设置，减少数据量
    const base64Data = canvas.toDataURL('image/jpeg', 0.85);

    return base64Data;
  } catch (error) {
    throw error;
  }
};
</script>

<style scoped>
.create-post {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
  width: 180px;
  padding-left: 20px;
}

.logo-image {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: transform 0.3s;
}

.logo-image:hover {
  transform: scale(1.05);
}



.header-center {
  flex: 1;
  text-align: center;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 0 0 auto;
  width: 180px;
  padding-right: 20px;
}

.language-button {
  background: #f5f7fa;
  border: none;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  padding: 6px 16px;
  border-radius: 20px;
  transition: all 0.2s;
  white-space: nowrap;
  margin-right: 10px;
}

.language-button:hover {
  background: #eaeaea;
  color: #333;
}

.exit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  padding: 9px 18px;
  border-radius: 24px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.35);
  height: 40px;
  white-space: nowrap;
}

.exit-button:hover {
  background: linear-gradient(135deg, #5258ef, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.45);
}

.exit-icon {
  font-size: 18px;
  line-height: 1;
  font-weight: 300;
}

.post-form {
  padding: 10px;
  width: 98%;
  margin: 0 auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) {
  animation-delay: 0.1s;
}

.form-group:nth-child(2) {
  animation-delay: 0.2s;
}

.form-group:nth-child(3) {
  animation-delay: 0.3s;
}

.form-group:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.form-group.focused {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.25);
  border-color: rgba(102, 126, 234, 0.3);
}

.form-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.label-icon {
  font-size: 18px;
}

.label-note {
  font-size: 13px;
  font-weight: normal;
  color: #666;
  margin-left: 8px;
}

.label-required {
  color: #f56c6c;
  margin-left: 4px;
  font-weight: bold;
}

.selected-count {
  font-size: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: auto;
  animation: pulse 0.3s ease-out;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.form-input-container {
  position: relative;
  margin-bottom: 12px;
}

.post-title-input {
  width: 100%;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 16px;
  outline: none;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
}

.post-title-input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.character-count {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.character-count.warning {
  color: #ff6b6b;
  font-weight: 600;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {

  0%,
  100% {
    transform: translateY(-50%) translateX(0);
  }

  25% {
    transform: translateY(-50%) translateX(-2px);
  }

  75% {
    transform: translateY(-50%) translateX(2px);
  }
}

.form-tip {
  font-size: 13px;
  color: #666;
  margin-top: 8px;
}

.highlight {
  color: #ff6b6b;
  font-weight: 600;
}

.post-textarea {
  width: 100%;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 16px;
  min-height: 120px;
  resize: none;
  outline: none;
  box-sizing: border-box;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
}

.post-textarea:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.upload-tip {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.image-upload-area {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
  margin-bottom: 20px;
  width: 100%;
}

.image-upload-item {
  flex: 1;
  aspect-ratio: 1/1;
  border: 1px dashed #d1d5db;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 320px;
  /* 进一步增加高度 */
}

.image-upload-item:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.image-upload-item.has-image {
  border: none;
  background: transparent;
  position: relative;
}

.image-upload-item.has-image::after {

  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-upload-item.has-image:hover::after {
  opacity: 1;
}

.image-upload-item.uploading {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.upload-icon {
  color: #9ca3af;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  transform: scale(1.2);
}

.image-upload-item:hover .upload-icon {
  color: #667eea;
  transform: scale(1.05);
}

.upload-text {
  font-size: 18px;
  font-weight: 500;
  margin-top: 10px;
}

.upload-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 16px;
  z-index: 5;
  backdrop-filter: blur(3px);
  will-change: opacity;
  animation: fadeIn 0.2s ease;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 4px solid #e1e5e9;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: fastSpin 0.8s cubic-bezier(0.4, 0.1, 0.4, 0.9) infinite;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
  will-change: transform;
}

@keyframes fastSpin {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(90deg);
  }

  50% {
    transform: rotate(180deg);
  }

  75% {
    transform: rotate(270deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 16px;
  transition: transform 0.2s ease;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: center center;
  will-change: transform;
  backface-visibility: hidden;
}

.image-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 6px;
  z-index: 10;
}

.image-action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.image-action-btn:hover {
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(244, 63, 94, 0.9);
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.delete-btn:hover {
  background: #f43f5e;
}

.image-upload-item:hover .preview-image {
  transform: translate(-50%, -50%) scale(1.05);
}

.remove-image {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  z-index: 2;
  transition: all 0.3s ease;
  opacity: 0;
}

.image-upload-item:hover .remove-image {
  opacity: 1;
  transform: scale(1.1);
}

.remove-image:hover {
  background: rgba(255, 107, 107, 0.9);
  transform: scale(1.2);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tags-container.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.no-tags-message,
.no-tags-message {
  width: 100%;
  padding: 15px;
  text-align: center;
  color: #94a3b8;
  background-color: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  font-size: 14px;
}

.tag-selection-hint {
  width: 100%;
  padding: 15px;
  text-align: center;
  color: #e6a23c;
  background-color: #fdf6ec;
  border-radius: 8px;
  font-size: 14px;
  border: 1px dashed #f3d19e;
  margin-top: 10px;
}

.tag-item {
  padding: 12px 20px;
  border-radius: 25px;
  background: rgba(248, 250, 252, 0.8);
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
}

.tag-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.tag-item:hover::before {
  left: 100%;
}

.tag-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tag-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.tag-check {
  font-size: 12px;
  animation: checkIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkIn {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }

  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.bottom-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 20px;
  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: auto;
  width: 100%;
  box-sizing: border-box;
}

.publish-btn {
  width: auto;
  min-width: 200px;
  padding: 16px;
  border: none;
  border-radius: 16px;
  background: #e5e7eb;
  color: #9ca3af;
  font-weight: 600;
  font-size: 16px;
  cursor: not-allowed;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.cancel-btn {
  width: auto;
  min-width: 150px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  background: transparent;
  color: #64748b;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: #ef4444;
  transform: translateY(-2px);
}

.publish-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.publish-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  cursor: pointer;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.publish-btn.active:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
}

.publish-btn.active:hover::before {
  left: 100%;
}

.publishing {
  display: flex;
  align-items: center;
  gap: 8px;
}

.publish-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.action-links {
  display: flex;
  justify-content: center;
}

.action-link {
  font-size: 13px;
  color: #666;
}

.watermark-container {
  position: absolute;
  left: -9999px;
  top: -9999px;
}

.watermark-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.watermark-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.watermark-text {
  width: 80px;
  object-fit: contain;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .post-form {
    padding: 10px;
    width: 98%;
  }

  .form-group {
    padding: 15px;
    margin-bottom: 15px;
  }

  .image-upload-area {
    flex-direction: column;
  }

  .image-upload-item {
    height: 200px;
    margin-bottom: 10px;
  }

  .bottom-bar {
    padding: 16px;
  }
}

/* 响应式优化 */
@media (max-width: 480px) {
  .header {
    padding: 12px 16px;
  }

  .back-button span {
    display: none;
    /* 在小屏幕上只显示图标 */
  }

  .back-button {
    padding: 6px;
  }

  .title {
    font-size: 18px;
  }

  .placeholder {
    width: 32px;
  }

  .form-group {
    padding: 12px;
    border-radius: 12px;
  }

  .post-title-input,
  .post-textarea {
    padding: 12px 16px;
    font-size: 15px;
  }

  .image-upload-area {
    flex-direction: column;
  }

  .image-upload-item {
    height: 220px;
    margin-bottom: 10px;
  }

  .tag-item {
    padding: 10px 16px;
    font-size: 13px;
  }
}

.converting-text {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 6px;
  border-radius: 8px;
  margin: 0 10px;
  z-index: 10;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

/* 错误状态的图片 */
.image-upload-item.has-error {
  border: 2px solid #f87171;
}

.image-upload-item .error-message {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #ef4444;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 6px;
  border-radius: 4px;
  margin: 0 10px;
  z-index: 10;
}

.preview-image.error-image {
  opacity: 0.7;
  filter: grayscale(100%);
}

.error-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 113, 113, 0.1);
  border-radius: 16px;
  position: absolute;
  top: 0;
  left: 0;
}

.error-image {
  width: 60px;
  height: 60px;
  color: #f87171;
}

/* 图片预览 */
.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
}

.preview-content {
  position: relative;
  background: white;
  border-radius: 8px;
  width: 600px;
  height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  z-index: 1001;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.preview-content img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  cursor: pointer;
  border: none;
  z-index: 1002;
}

.preview-close:hover {
  background: rgba(0, 0, 0, 0.8);
}

@media (max-width: 768px) {
  .preview-content {
    width: 90%;
    height: auto;
    aspect-ratio: 1/1;
  }
}



/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

/* 会员提示弹窗样式 */
.membership-modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.membership-modal .modal-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.membership-modal .modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #666;
}

.membership-modal .modal-body {
  padding: 24px;
  text-align: center;
}

.membership-modal .modal-body p {
  margin: 0 0 16px;
  color: #666;
  line-height: 1.5;
}

.benefits-list {
  margin: 20px 0;
  text-align: left;
}

.benefit-item {
  padding: 8px 0;
  color: #555;
  font-size: 14px;
}

.membership-modal .modal-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 优惠券弹窗样式 */
.voucher-modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 420px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.voucher-modal .modal-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.voucher-modal .modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.voucher-modal .modal-body {
  padding: 24px;
  text-align: center;
}

.voucher-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.voucher-amount {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.voucher-amount .currency {
  font-size: 16px;
  font-weight: 500;
}

.voucher-amount .value {
  font-size: 32px;
  font-weight: bold;
}

.voucher-info {
  flex: 1;
  text-align: left;
}

.voucher-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.voucher-validity {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.voucher-desc {
  font-size: 12px;
  opacity: 0.8;
}

.congratulation {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.voucher-modal .modal-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-secondary:hover {
  background: #e8e8e8;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 加载覆盖层样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.5s ease;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #667eea;
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 水印网站文本样式 */
.watermark-website {
  color: #8a6df7;
  font-size: 20px;
  font-weight: bold;
  margin-left: 10px;
  font-family: Arial, sans-serif;
  line-height: 1;
  white-space: nowrap;
}
</style>