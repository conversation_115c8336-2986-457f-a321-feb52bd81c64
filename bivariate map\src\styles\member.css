/* 基础布局重置 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: auto;
}

/* 根容器样式 */
.min-h-screen {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(to bottom, #f9fafb, #f3f4f6);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 返回按钮样式 */
.back-button {
  position: absolute;
  left: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  top: 10px;
}

.back-button:hover {
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateX(-2px);
}

.back-icon {
  color: #4b5563;
}

/* 头部样式修改 */
.header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.header-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo-container {
  position:absolute;
  left:50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}

.crown-icon {
  color: #eab308;
  width: 32px;
  height: 32px;
}

.logo-text {
  margin-left: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
}

.right-actions {
  position: absolute;
  right: 1rem;
  display: flex;
  align-items: center;
}

.select-station-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #10b981;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.select-station-button:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

/* 主内容区域样式 */
.main-container {
  flex: 1;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  overflow-y: auto;
}

.section-container {
  padding: 1.5rem;
  margin-bottom: 2rem;
}

/* Member Status Card Styles */
.status-card {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

/* 普通会员金色样式 */
.status-card:not(.premium-card-status) {
  background: linear-gradient(to right, #ffffff, #fffbeb);
  border: 1px solid #fef3c7;
  box-shadow: 0 8px 24px rgba(234, 179, 8, 0.12);
}

.premium-card-status {
  background: linear-gradient(to right, #ffffff, #f9f4ff);
  border: 1px solid #e4d0ff;
  box-shadow: 0 8px 24px rgba(156, 39, 176, 0.12);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header-content {
  flex: 1;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.card-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.status-badge {
  display: inline-flex;
  padding: 0.25rem 0;
}

.badge-regular, .badge-premium {
  padding: 0.25rem 0.75rem;
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 普通会员徽章金色样式 */
.badge-regular {
  background: linear-gradient(135deg, #fef9c3, #fde047);
  color: #854d0e;
  box-shadow: 0 2px 6px rgba(234, 179, 8, 0.3);
}

.badge-premium {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
}

.card-body {
  padding: 1.5rem;
}

.member-status {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

/* 普通会员图标容器金色样式 */
.status-icon-container {
  flex-shrink: 0;
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: linear-gradient(135deg, #fef9c3, #fde047);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.25);
}

.premium-icon-bg {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.25);
}

.user-icon {
  color: #9ca3af;
}

/* 普通会员皇冠图标金色样式 */
.crown-icon-large {
  color: #eab308;
}

.crown-icon-large.premium {
  color: #ffeb3b;
}

/* 普通会员状态标题金色样式 */
.status-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

/* 普通会员文本金色样式 */
.status-card:not(.premium-card-status) .status-title {
  background: linear-gradient(135deg, #eab308, #facc15);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 800;
}

.premium-text {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 800;
}

.status-expiry {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* 普通会员到期日期金色样式 */
.status-card:not(.premium-card-status) .expiry-icon {
  color: #eab308;
}

.status-card:not(.premium-card-status) .expiry-date {
  color: #854d0e;
}

.benefits-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #4b5563;
}

.member-benefits {
  background-color: #f9fafb;
  border-radius: 0.75rem;
  padding: 1rem;
}

.benefits-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
}

.benefit-item-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  padding: 0.25rem 0;
}

.check-icon {
  margin-right: 0.75rem;
  color: #eab308;
  flex-shrink: 0;
}

.check-icon-premium {
  margin-right: 0.75rem;
  color: #e6c656;
  flex-shrink: 0;
}

.premium-benefit {
  color: #4a5568;
}

.no-benefits {
  background-color: #f9fafb;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
}

.status-subtitle {
  color: #6b7280;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.explore-button {
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.explore-button:hover {
  background-color: #2c5282;
  transform: translateY(-1px);
}

/* 会员套餐部分 */
.membership-section {
  margin-top: 3rem;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
}

.membership-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

@media (min-width: 640px) {
  .membership-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }
  
  .section-container {
    padding: 2rem;
  }
}

/* 普通会员卡片 */
.regular-card {
  position: relative;
  background: linear-gradient(135deg, #eab308, #facc15);
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(234, 179, 8, 0.2);
  overflow: hidden;
  border: 1px solid #fbbf24;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.regular-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  z-index: 1;
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 200%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.regular-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.regular-card:hover .card-shine {
  opacity: 1;
  animation: shine 1.5s infinite;
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 超级会员卡片 */
.premium-card {
  position: relative;
  background: linear-gradient(135deg, #1e2330, #252a3a);
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #33384d;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 添加白条 */
.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  z-index: 1;
}

.premium-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: translateY(-5px);
}

.card-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.card-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.membership-title {
  font-size: 1.25rem;
  font-weight: 600;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.025em;
}

.premium-title {
  background: linear-gradient(90deg, #e6c656, #f0d87a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tag-recommended {
  background: linear-gradient(90deg, #fef9c3, #fde047);
  color: #854d0e;
}

.tag-premium {
  background: linear-gradient(90deg, #e6c656, #f0d87a);
  color: #1e2330;
}

.price-container {
  margin-top: 1.5rem;
  position: relative;
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
}

.price-period {
  color: #c9d4ff;
  font-size: 0.875rem;
  margin-left: 0.25rem;
}

.premium-period {
  color: #a3a5b0;
}

.benefits-list {
  margin-top: 2rem;
  list-style: none;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  font-size: 0.9375rem;
  color: #e6eeff;
}

.card-actions {
  margin-top: 2.5rem;
}

.action-button {
  width: 100%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  letter-spacing: 0.025em;
}

.primary-button {
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  color: #0062ff;
  box-shadow: 0 4px 10px rgba(255, 255, 255, 0.3);
}

.primary-button:hover {
  background: linear-gradient(90deg, #f0f0f0, #ffffff);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 255, 255, 0.4);
}

.premium-button {
  background: linear-gradient(90deg, #e6c656, #f0d87a);
  color: #1e2330;
  box-shadow: 0 4px 10px rgba(230, 198, 86, 0.3);
}

.premium-button:hover {
  background: linear-gradient(90deg, #f0d87a, #e6c656);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(230, 198, 86, 0.4);
}

.phone-icon {
  margin-right: 0.5rem;
}

/* 模态框样式 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  z-index: 50;
  overflow-y: auto;
}

@media (min-width: 640px) {
  .modal-container {
    align-items: center;
    padding: 0;
  }
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  transition: opacity 0.3s;
}

.modal-content {
  position: relative;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
  margin: auto;
  background-color: white;
  border-radius: 1rem;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 支付弹窗特定样式 */
.payment-modal {
  width: 450px !important;
  max-width: 90vw;
  padding: 1.5rem;
}

/* 火车站选择弹窗特定样式 */
.station-selector-modal {
  width: 800px !important;
  max-width: 95vw;
  padding: 2rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.modal-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.close-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
  line-height: 0;
}

.close-button:hover {
  color: #111827;
}

.modal-body {
  padding: 0 1rem;
}

.modal-footer {
  margin-top: 2rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 1rem;
}

/* 站点选择器样式 */
.station-selector-content {
  margin-top: 1rem;
}

.search-section {
  margin-bottom: 2rem;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #fff;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #0062ff;
  box-shadow: 0 0 0 3px rgba(0, 98, 255, 0.1);
}

/* 热门站点样式 */
.hot-stations {
  margin-top: 1rem;
}

.hot-stations-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.hot-stations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
}

.hot-station-btn {
  padding: 0.5rem 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.hot-station-btn:hover {
  background-color: #0062ff;
  color: white;
  border-color: #0062ff;
}

.stations-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  max-height: 500px;
  overflow-y: auto;
}

@media (min-width: 640px) {
  .stations-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stations-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.station-card {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #fff;
}

.station-card:hover {
  border-color: #0062ff;
  box-shadow: 0 4px 12px rgba(0, 98, 255, 0.1);
  transform: translateY(-2px);
}

.station-selected {
  border-color: #0062ff !important;
  background-color: rgba(0, 98, 255, 0.05);
  box-shadow: 0 4px 12px rgba(0, 98, 255, 0.15);
}

.station-hot {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
}

.station-exit-port {
  border-color: #10b981;
  background: linear-gradient(135deg, #d1fae5, #34d399);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.station-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  color: #6b7280;
}

.station-selected .station-icon {
  justify-content: center;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  color: #6b7280;
}

.station-selected .station-icon {
  background-color: #0062ff;
  color: white;
}

.station-hot .station-icon {
  background-color: #f59e0b;
  color: white;
}

.station-exit-port .station-icon {
  background-color: #10b981;
  color: white;
}

.station-badges {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.badge {
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-hot {
  background-color: #f59e0b;
  color: white;
}

.badge-exit {
  background-color: #10b981;
  color: white;
}

.station-info {
  flex: 1;
  min-width: 0;
}

.station-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e2330;
  margin-bottom: 0.25rem;
}

.station-name-en {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.station-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.station-city {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.station-city svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.station-coordinates {
  font-size: 0.75rem;
  color: #9ca3af;
}

.coordinates-text {
  font-family: 'Monaco', 'Menlo', monospace;
}

.station-check {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  color: #0062ff;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #0062ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

/* 无结果状态 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.no-results-icon {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.no-results-text {
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.no-results-hint {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 支付详情样式 */
.payment-details {
  margin-top: 1.5rem;
}

.payment-summary {
  background-color: #f9fafb;
  padding: 1.25rem;
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.payment-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.payment-label {
  color: #4b5563;
  font-weight: 500;
}

.payment-value {
  color: #111827;
  font-weight: 600;
}

.payment-discount {
  color: #dc2626;
  font-weight: 600;
}

.payment-divider {
  border-top: 1px solid #e5e7eb;
  margin: 0.75rem 0;
}

.payment-total {
  font-weight: 600;
}

/* 优惠券选择区域样式 */
.coupon-section {
  margin-bottom: 1.5rem;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
}

.coupon-header:hover {
  background-color: #f3f4f6;
}

.coupon-title {
  font-weight: 500;
  color: #4b5563;
}

.coupon-selected {
  color: #7c3aed;
  font-weight: 500;
  flex: 1;
  text-align: right;
  margin-right: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coupon-arrow {
  color: #6b7280;
  transition: transform 0.2s;
}

.coupon-arrow-up {
  transform: rotate(180deg);
}

.coupon-list {
  margin-top: 0.5rem;
  border: 1px solid #e7e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  max-height: 300px;
  overflow-y: auto;
}

.coupon-item {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}

.coupon-item:last-child {
  border-bottom: none;
}

.coupon-item:hover {
  background-color: #f9fafb;
}

.coupon-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coupon-item-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.coupon-item-desc {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.coupon-item-expiry {
  font-size: 0.75rem;
  color: #9ca3af;
}

.coupon-item-amount {
  font-weight: 600;
  color: #dc2626;
  margin: 0 1rem;
}

.coupon-item-check {
  color: #7c3aed;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-method-section {
  margin: 2rem 0;
}

.payment-method-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 0.5rem;
}

.payment-method-option {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.75rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method-selected.payment-method-option {
  border-color: currentColor;
  background-color: rgba(124, 58, 237, 0.05);
}

.payment-method-name {
  font-weight: 500;
}

.wechat {
  color: #10b981;
}

.payment-method-selected .wechat {
  color: #10b981;
}

.alipay {
  color: #3b82f6;
}

.payment-method-selected .alipay {
  color: #3b82f6;
}

.card {
  color: #8b5cf6;
}

.payment-method-selected .card {
  color: #8b5cf6;
}

/* 次要按钮样式 */
.secondary-button {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.secondary-button:hover {
  background-color: #e5e7eb;
  color: #111827;
}

/* 车站购票支付弹窗样式 */
.ticket-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
}

.ticket-header {
  display: flex;
  align-items: center;
}

.ticket-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e7eb;
  border-radius: 0.5rem;
  margin-right: 1rem;
  color: #4b5563;
}

.ticket-station {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e2330;
  margin-bottom: 0.25rem;
}

.ticket-description {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0;
}

/* 按钮图标 */
.ticket-icon {
  margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .station-selector-modal {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .stations-grid {
    grid-template-columns: 1fr;
    padding: 0 1rem 1rem 1rem;
  }
  
  .search-section {
    margin: 1.5rem 1rem 1rem 1rem;
  }
  
  .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .header-content {
    gap: 0.75rem;
  }
  
  .header-icon {
    width: 40px;
    height: 40px;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .hot-stations-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 1rem;
}

.train-header-icon {
  width: 32px;
  height: 32px;
  color: #0062ff;
}

/* 超级会员权益列表 - 一行显示一个权益 */
.premium-card-status .benefits-list {
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

/* 补充恢复被删除的样式 */
.status-info {
  flex: 1;
}

.status-header {
  margin-bottom: 1rem;
}

.expiry-icon {
  color: #9ca3af;
}

.expiry-date {
  font-weight: 600;
  color: #4b5563;
}

/* 会员卡片样式 */
.regular-card {
  position: relative;
  background: linear-gradient(135deg, #0062ff, #0052d6);
  border-radius:  1rem;
  box-shadow: 0 10px 30px rgba(0, 98, 255, 0.2);
  overflow: hidden;
  border: 1px solid #0052d6;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.regular-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  z-index: 1;
}

/* 超级会员卡片 - 添加白条 */
.premium-card {
  position: relative;
  background: linear-gradient(135deg, #1e2330, #252a3a);
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #33384d;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 与普通会员卡片一致的动画效果 */
}

/* 添加白条 */
.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  z-index: 1;
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 200%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* 统一悬浮效果 */
.regular-card:hover,
.premium-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.regular-card:hover .card-shine,
.premium-card:hover .card-shine {
  opacity: 1;
  animation: shine 1.5s infinite;
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 超级会员卡片权益一行显示一条 */
.premium-card .benefits-list {
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

/* 调整权益项样式 */
.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  font-size: 0.9375rem;
  color: #e6eeff;
}

/* 超级会员卡片权益项样式优化 */
.premium-card .benefit-item {
 

  border-radius: 0.5rem;
  margin-bottom: 0.75rem;

  transition: all 0.2s ease;
}

/* .premium-card .benefit-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(4px);
} */