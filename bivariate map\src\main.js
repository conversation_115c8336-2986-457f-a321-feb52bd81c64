import { createApp } from 'vue'
import { createYmaps } from 'vue-yandex-maps'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import { useI18n } from './composables/useI18n'

// 确保router已经初始化后再导入权限控制
// 这里不再导入权限控制，而是在router/index.js中处理
import { ElMessage } from 'element-plus'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
// 导入图标修复CSS
import './assets/icon-fix.css'
// 导入移动设备缩放样式
import './styles/WorldMap.css'

const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const { t } = useI18n()
app.config.globalProperties.$t = t

app.use(createYmaps({
  apikey: '821339379ceb4ef290c0cf95503f079b',
  lang: 'EN_US',
}))

app.use(router)

app.use(createPinia())

// 将ElMessage挂载到全局
app.config.globalProperties.$message = ElMessage

app.use(ElementPlus)

app.mount('#app')
