<template>
  <div class="earth3d-container">
    <!-- 搜索框已由父组件控制，此处隐藏 -->
    <div class="top-search" v-show="false">
      <input v-model="stationName" placeholder="输入站点名称..." />
      <button @click="searchStation">搜索</button>
    </div>

    <!-- 国家名称显示 - 只在选中国家时显示 -->
    <transition name="fade">
      <div v-if="currentCountryName && CLICKED" class="country-name">{{ currentCountryName }}</div>
    </transition>

    <div ref="containerRef" class="render-container"></div>

    <!-- 加载状态指示器 - 改进版本，用v-show代替v-if避免DOM重建造成卡顿 -->
    <div v-show="isLoading" class="loading-overlay" style="opacity: 1;">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载地图...</div>
        <div class="loading-progress">
          <div class="loading-progress-bar" :style="{ width: loadingProgress + '%' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { Country } from '../utils/Country'
import { COUNTRY_NAMES } from '../constants/countryNames'
import * as topojson from 'topojson-client'
import { geoMercator, geoPath } from 'd3-geo'  // 仍然导入以备用
import { projection } from '../utils/projection'  // 直接使用共享的projection实例
import { getRoute } from '@/api/map'
import { getHotStations } from '@/data/hotStations'
import { useI18n } from '../composables/useI18n'
// 使用国际化
const { t, currentLocale } = useI18n();

// 计算当前是否为英文模式
const isEnglish = computed(() => currentLocale.value === 'en');

// 定义props（如果需要从父组件接收语言变化）
const props = defineProps({
  language: {
    type: String,
    default: 'zh-CN'
  }
});

// 添加搜索功能
const stationName = ref('');

// 存储最近一次处理的路线数据
let lastProcessedRoutes = [];

// 存储所有路线飞线的数组，用于后续清理
let routeFlightLines = [];

// 存储所有出境口岸标签的数组，用于后续清理
let exitPortLabels = [];

// 存储搜索起止站点标签
let endpointLabels = [];

// 搜索站点函数
const searchStation = async () => {
  if (!stationName.value) {
    return;
  }

  try {
    // 使用与平面地图完全相同的处理逻辑
    // 处理目的地，检查是否包含中英文格式 EnglishName(ChineseName)
    let endStationName = stationName.value;
    const destMatch = stationName.value.match(/^(.+?)\(([^)]+)\)$/);
    if (destMatch) {
      // 如果匹配到格式，提取英文部分
      endStationName = destMatch[1];
      console.log("检测到格式 EnglishName(ChineseName)，使用英文名搜索:", endStationName);
    }

    // 确定是否包含中文字符
    const isChinese = /[\u4e00-\u9fa5]/.test(endStationName);
    const data = isChinese
      ? { endStationNameZh: endStationName }
      : { endStationNameEn: endStationName };

    console.log('搜索站点:', stationName.value, '处理后:', endStationName, '语言判断:', isChinese ? '中文' : '英文', '搜索参数:', data);

    // 获取路线数据
    const response = await getRoute(data);

    // 检查返回数据是否为空
    if (!response || !response.rows || response.rows.length === 0) {
      console.warn('搜索站点返回数据为空');
      return;
    }

    console.log('获取到的路线数据:', response.rows);

    // 预处理数据 - 确保所有站点经纬度值都是数字且经纬度正确
    const processedRoutes = response.rows.map(route => {
      // 深拷贝以避免修改原始数据
      const processedRoute = { ...route };

      // 确保经纬度值是数字
      processedRoute.startStationLatitude = parseFloat(route.startStationLatitude);
      processedRoute.startStationLongitude = parseFloat(route.startStationLongitude);
      processedRoute.endStationLatitude = parseFloat(route.endStationLatitude);
      processedRoute.endStationLongitude = parseFloat(route.endStationLongitude);

      // 检查并修正可能的经纬度交换问题
      if (Math.abs(processedRoute.startStationLatitude) > 90) {
        // 如果纬度超出范围，可能是经纬度值交换了
        console.warn('检测到可能的经纬度交换，尝试修正:', {
          before: {
            lat: processedRoute.startStationLatitude,
            lng: processedRoute.startStationLongitude
          }
        });

        // 临时保存
        const tempLat = processedRoute.startStationLatitude;
        processedRoute.startStationLatitude = processedRoute.startStationLongitude;
        processedRoute.startStationLongitude = tempLat;

        console.log('修正后:', {
          after: {
            lat: processedRoute.startStationLatitude,
            lng: processedRoute.startStationLongitude
          }
        });
      }

      if (Math.abs(processedRoute.endStationLatitude) > 90) {
        // 同样修正终点的经纬度
        console.warn('检测到可能的经纬度交换，尝试修正:', {
          before: {
            lat: processedRoute.endStationLatitude,
            lng: processedRoute.endStationLongitude
          }
        });

        // 临时保存
        const tempLat = processedRoute.endStationLatitude;
        processedRoute.endStationLatitude = processedRoute.endStationLongitude;
        processedRoute.endStationLongitude = tempLat;

        console.log('修正后:', {
          after: {
            lat: processedRoute.endStationLatitude,
            lng: processedRoute.endStationLongitude
          }
        });
      }

      return processedRoute;
    });

    // 检查数据格式
    processedRoutes.forEach((route, index) => {
      console.log(`路线${index + 1}经纬度:`, {
        start: { lat: route.startStationLatitude, lng: route.startStationLongitude },
        end: { lat: route.endStationLatitude, lng: route.endStationLongitude }
      });
    });
    
    // 如果有数据，使用处理过的数据创建飞线图
    lastProcessedRoutes = processedRoutes;
    createRouteFlightLines(processedRoutes);

  } catch (error) {
    console.error('搜索站点出错:', error);
  }
};

// 供父组件调用的搜索接口
const searchIn3D = async (keyword) => {
  if (!keyword) return;
  console.log('searchIn3D: 开始搜索', keyword);

  stationName.value = keyword;
  await searchStation();

  // 隐藏热门城市光柱
  hideCityBeams();

  console.log('searchIn3D: 搜索完成，路线数量:', lastProcessedRoutes.length);
};

// 创建飞线图函数
const createRouteFlightLines = (routesData) => {
  // 清理之前的飞线
  clearRouteFlightLines();

  // 创建新的 flightGroup
  flightGroup = new THREE.Group();
  mapScene.add(flightGroup);

  // 清理旧的端点标签
  endpointLabels.forEach(label => {
    if (label.parent) label.parent.remove(label);
    if (label.element && label.element.parentNode) {
      label.element.parentNode.removeChild(label.element);
    }
  });
  endpointLabels = [];

  if (!Array.isArray(routesData)) {
    console.error('路线数据格式错误，无法创建飞线图');
    return;
  }

  // 添加数据限制：只显示前3个飞线
  const maxFlightLines = 3;
  const limitedRoutesData = routesData.slice(0, maxFlightLines);
  
  console.log(`原始路线数据数量: ${routesData.length}`);
  console.log(`限制后显示数量: ${limitedRoutesData.length}`);
  
  if (routesData.length > maxFlightLines) {
    console.log(`已隐藏 ${routesData.length - maxFlightLines} 条飞线路径`);
  }

  // 飞线索引计数器，用于管理所有飞线段
  let flightLineIndex = 0;

  // 为每条路线创建飞线（使用限制后的数据）
  limitedRoutesData.forEach((route, routeIndex) => {
    try {
      // 寻找出境口岸
      const exitPort = route.viaStations?.find(station => station.isExitPort === 1);
      
      if (!exitPort) {
        console.warn(`路线 "${route.name}" 未找到出境口岸，创建直接飞线`);
        // 创建直接从起点到终点的飞线
        createDirectFlightLine(route, routeIndex, flightLineIndex);
        flightLineIndex++;
        return;
      }

      console.log(`路线 "${route.name}" 找到出境口岸: ${exitPort.stationNameZh} (${exitPort.stationNameEn})`);

      // 准备起点、出境口岸、终点的坐标数据
      const startPoint = {
        lat: parseFloat(route.startStationLatitude),
        lng: parseFloat(route.startStationLongitude),
        name: isEnglish.value ? route.startStationNameEn : route.startStationNameZh
      };

      const exitPortPoint = {
        lat: parseFloat(exitPort.latitude),
        lng: parseFloat(exitPort.longitude),
        name: isEnglish.value ? exitPort.stationNameEn : exitPort.stationNameZh
      };

      // 在计算 exitPortPoint 之后，和 start/end 一起做校正：
      let { lat: epLat, lng: epLng } = exitPortPoint;
      if (Math.abs(epLat) > 90 && Math.abs(epLng) <= 90) {
        [epLat, epLng] = [epLng, epLat];
      }
      exitPortPoint.lat = epLat;
      exitPortPoint.lng = epLng;

      const endPoint = {
        lat: parseFloat(route.endStationLatitude),
        lng: parseFloat(route.endStationLongitude),
        name: isEnglish.value ? route.endStationNameEn : route.endStationNameZh
      };

      // 输出调试信息
      console.log(`路线 ${routeIndex + 1} "${route.name}" 分段信息:`, {
        起点: { lat: startPoint.lat, lng: startPoint.lng, name: startPoint.name },
        出境口岸: { lat: exitPortPoint.lat, lng: exitPortPoint.lng, name: exitPortPoint.name },
        终点: { lat: endPoint.lat, lng: endPoint.lng, name: endPoint.name }
      });

      // 创建起点标签
      createEndpointLabel(startPoint, true);

      // 创建终点标签
      createEndpointLabel(endPoint, false);

      // 创建出境口岸标签
      createExitPortLabel(exitPortPoint, route.name);

      // 为不同段设置不同颜色
      const domesticColor = new THREE.Color(0xFF0000);  // 蓝色 - 国内段
      const internationalColor = new THREE.Color(0xFF0000); // 蓝色 - 国际段

      // 第一段：起点 → 出境口岸 (国内段)
      console.log(`创建国内段飞线: ${startPoint.name} → ${exitPortPoint.name}`);
      const domesticLine = createFlightLine(startPoint, exitPortPoint, domesticColor, flightLineIndex, 0.0);
      
      if (domesticLine) {
        setupFlightLine(domesticLine, flightLineIndex, `${route.name}-国内段`);
        routeFlightLines.push(domesticLine);
        flightGroup.add(domesticLine);
        flightLineIndex++;
      }

      // 第二段：出境口岸 → 终点 (国际段)
      console.log(`创建国际段飞线: ${exitPortPoint.name} → ${endPoint.name}`);
      const internationalLine = createFlightLine(exitPortPoint, endPoint, internationalColor, flightLineIndex, 1.0);
      
      if (internationalLine) {
        setupFlightLine(internationalLine, flightLineIndex, `${route.name}-国际段`);
        routeFlightLines.push(internationalLine);
        flightGroup.add(internationalLine);
        flightLineIndex++;
      }

    } catch (error) {
      console.error(`创建路线 ${routeIndex + 1} "${route.name}" 的飞线时出错:`, error);
    }
  });

  // 调整 flightGroup 的整体位置到第三层地图
  flightGroup.position.z = BASE_Z + LAYER_HEIGHT * 0.1-1.6; // 进一步降低高度

  // 强制进行一次完整渲染
  renderNeedsUpdate = true;
};

// 创建直接飞线函数（没有出境口岸的情况）
const createDirectFlightLine = (route, routeIndex, flightLineIndex) => {
  try {
    // 准备起点和终点的坐标数据
    const startPoint = {
      lat: parseFloat(route.startStationLatitude),
      lng: parseFloat(route.startStationLongitude),
      name: isEnglish.value ? route.startStationNameEn : route.startStationNameZh
    };

    const endPoint = {
      lat: parseFloat(route.endStationLatitude),
      lng: parseFloat(route.endStationLongitude),
      name: isEnglish.value ? route.endStationNameEn : route.endStationNameZh
    };

    console.log(`创建直接飞线: ${startPoint.name} → ${endPoint.name}`);

    // 创建起点和终点标签
    createEndpointLabel(startPoint, true);
    createEndpointLabel(endPoint, false);

    // 设置飞线颜色
    const flightColor = new THREE.Color(0xFF0000);  // 红色，与其他飞线一致

    // 创建直接飞线
    const directLine = createFlightLine(startPoint, endPoint, flightColor, flightLineIndex, 0.0);

    if (directLine) {
      setupFlightLine(directLine, flightLineIndex, `${route.name}-直接线路`);
      routeFlightLines.push(directLine);
      flightGroup.add(directLine);
      console.log(`直接飞线创建成功: ${route.name}`);
    } else {
      console.error(`直接飞线创建失败: ${route.name}`);
    }

  } catch (error) {
    console.error(`创建直接飞线时出错:`, error);
  }
};

// 设置飞线的通用属性
const setupFlightLine = (flightLine, index, lineName) => {
  // 关键设置：强制更新世界矩阵和禁用深度测试
  flightLine.updateMatrixWorld(true);
  flightLine.visible = true;
  flightLine.renderOrder = 10000;

  // 禁用深度测试 - 这是确保飞线可见的核心
  if (flightLine.material) {
    flightLine.material.depthTest = false;
    flightLine.material.needsUpdate = true;
  }

  // 特殊处理，使飞线始终可见
  flightLine.userData.alwaysVisible = true;
  flightLine.userData.isFlightLine = true;
  flightLine.userData.creationTime = Date.now();
  flightLine.userData.growthAnimating = true;

  // 设置初始生长进度为0
  if (flightLine.material && flightLine.material.uniforms && flightLine.material.uniforms.growthProgress) {
    flightLine.material.uniforms.growthProgress.value = 0.0;
  }

  // 命名线条以便后续识别
  flightLine.name = lineName || `flightLine-${index}`;
};

// 创建出境口岸标签函数
const createExitPortLabel = (exitPortPoint, routeName) => {
  if (
    !exitPortPoint ||
    exitPortPoint.lat == null ||
    exitPortPoint.lng == null ||
    isNaN(exitPortPoint.lat) ||
    isNaN(exitPortPoint.lng)
  ) {
    return null;
  }
  
  try {
    const [x, y] = projection([exitPortPoint.lng, exitPortPoint.lat]);
    const scaleFactor = 1 / 6;
    const scaledX = x * scaleFactor;
    const scaledY = -y * scaleFactor;
    const labelHeight = BASE_Z + LAYER_HEIGHT * 1 + 2;
    const labelDiv = document.createElement('div');
    labelDiv.className = 'exit-port-label';
    // 根据当前语言选择显示的名称
    labelDiv.textContent = ` ${isEnglish.value ? exitPortPoint.nameEn || exitPortPoint.name : exitPortPoint.name}`;
    const label = new CSS2DObject(labelDiv);
    let labelX = scaledX;
    let labelY = scaledY;
    let labelZ = labelHeight;
    
    if (mapGroup && mapGroup.position) {
      labelX += mapGroup.position.x;
      labelY += mapGroup.position.y;
      labelZ += mapGroup.position.z;
    }
    
    label.position.set(labelX, labelY, labelZ);
    mapScene.add(label);
    exitPortLabels.push(label);
    
    console.log(`已创建出境口岸标签: ${exitPortPoint.name} at (${labelX.toFixed(2)}, ${labelY.toFixed(2)}, ${labelZ.toFixed(2)})`);
    
    return label;
  } catch (error) {
    console.error('创建出境口岸标签时出错:', error);
    return null;
  }
};

// 判断值是否在纬度范围内
const isInLatRange = (val) => {
  return val >= -90 && val <= 90;
};

// 判断值是否在经度范围内
const isInLngRange = (val) => {
  return val >= -180 && val <= 180;
};

// 清理飞线函数 - 确保每次都完全清理
const clearRouteFlightLines = () => {
  // 移除所有飞线及相关对象
  if (flightGroup) {
    mapScene.remove(flightGroup);
    flightGroup = null;
  }
  
  routeFlightLines.forEach(line => {
    if (line.geometry) line.geometry.dispose();
    if (line.material) line.material.dispose();
    if (line.parent) line.parent.remove(line);
  });

  // 重置飞线数组
  routeFlightLines = [];

  // 清理所有出境口岸标签
  exitPortLabels.forEach(label => {
    if (label.parent) {
      label.parent.remove(label);
    }
    if (label.element && label.element.parentNode) {
      label.element.parentNode.removeChild(label.element);
    }
  });

  // 重置出境口岸标签数组
  exitPortLabels = [];

  // 清理端点标签
  endpointLabels.forEach(l=>{if(l.parent)l.parent.remove(l); if(l.element&&l.element.parentNode) l.element.parentNode.removeChild(l.element);});
  endpointLabels=[];

  console.log('已清理所有飞线和出境口岸标签');

  // 触发渲染更新
  renderNeedsUpdate = true;
};

// 验证坐标是否有效，如果经纬度顺序错误则自动修复
const isValidCoordinate = (lat, lng) => {
  if (Math.abs(lat) <= 90 && Math.abs(lng) <= 180) {
    // 经纬度正常，直接返回有效
    return true;
  } else if (Math.abs(lng) <= 90 && Math.abs(lat) <= 180) {
    // 经纬度可能颠倒，但值本身有效
    return true;
  }
  // 经纬度值无效
  return false;
};

// 添加光柱相关常量
const PILLAR_HEIGHT = 1.5; // 光柱高度 - 进一步增加高度
const PILLAR_RADIUS = 0.12; // 光柱半径 - 进一步增加半径
const PILLAR_SEGMENTS = 16; // 光柱分段数

// 创建底部光柱的函数
const createPillar = (position, color) => {
  // 创建圆柱体几何体 - 注意Y轴是圆柱体的默认向上方向
  const geometry = new THREE.CylinderGeometry(
    PILLAR_RADIUS, // 顶部半径与飞线保持一致
    PILLAR_RADIUS * 1.2, // 底部略大一点
    PILLAR_HEIGHT,
    PILLAR_SEGMENTS,
    1,
    true // 开放端点
  );

  // 旋转几何体，使其Z轴向上（垂直于地图表面）
  geometry.rotateX(Math.PI / 2);

  // 创建发光材质
  const material = new THREE.ShaderMaterial({
    uniforms: {
      color: { value: new THREE.Color(0xFFFFFF) }, // 强制使用白色
      time: { value: 0 },
    },
    vertexShader: `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 color;
      uniform float time;
      varying vec2 vUv;
      
      void main() {
        // 使用v坐标作为高度渐变
        float heightGradient = smoothstep(0.0, 0.95, vUv.y);
        
        // 使用纯白色
        vec3 baseColor = vec3(1.0, 1.0, 1.0);
        
        // 添加流动效果，但只在底部显示
        float flow = fract(vUv.y - time * 0.5);
        float glow = smoothstep(0.0, 0.2, flow) * smoothstep(0.8, 0.6, flow) * 0.3;
        
        // 在底部添加一些亮度变化
        float bottomGlow = (1.0 - heightGradient) * 0.3;
        
        // 合并效果，保持颜色纯白
        vec3 finalColor = baseColor + vec3(glow + bottomGlow);
        
        // 透明度渐变，顶部完全不透明
        float alpha = mix(0.6, 1.0, heightGradient);
        
        gl_FragColor = vec4(finalColor, alpha);
      }
    `,
    transparent: true,
    side: THREE.DoubleSide,
    depthWrite: false,
    depthTest: false,
    blending: THREE.AdditiveBlending
  });

  const pillar = new THREE.Mesh(geometry, material);
  
  // 设置光柱位置，确保与地面对齐
  pillar.position.copy(position);
  pillar.position.z += PILLAR_HEIGHT / 2;
  
  // 设置渲染顺序确保光柱可见
  pillar.renderOrder = 9500;
  
  // 添加更新函数
  pillar.onBeforeRender = function() {
    if (this.material.uniforms && this.material.uniforms.time) {
      this.material.uniforms.time.value = (Date.now() % 1000) / 1000;
    }
  };

  return pillar;
};

// 创建单条飞线函数
const createFlightLine = (startPoint, endPoint, color, index, growthOffset = 0.0) => {
  if (!startPoint || !endPoint) {
    console.error('飞线起点或终点无效:', { startPoint, endPoint });
    return null;
  }

  try {
    // 确保经纬度在正确的范围内
    let validStartLat = startPoint.lat;
    let validStartLng = startPoint.lng;
    let validEndLat = endPoint.lat;
    let validEndLng = endPoint.lng;

    // 检查并修正经纬度范围
    if (!isInLatRange(validStartLat) || !isInLngRange(validStartLng) ||
      !isInLatRange(validEndLat) || !isInLngRange(validEndLng)) {
      console.warn('检测到经纬度范围异常，尝试修正:', {
        startOriginal: { lat: startPoint.lat, lng: startPoint.lng },
        endOriginal: { lat: endPoint.lat, lng: endPoint.lng }
      });

      // 如果经纬度值看起来交换了，则交换回来
      if (Math.abs(validStartLat) > 90 || Math.abs(validEndLat) > 90) {
        if (Math.abs(validStartLat) > 90 && Math.abs(validStartLng) <= 90) {
          const temp = validStartLat;
          validStartLat = validStartLng;
          validStartLng = temp;
        }

        if (Math.abs(validEndLat) > 90 && Math.abs(validEndLng) <= 90) {
          const temp = validEndLat;
          validEndLat = validEndLng;
          validEndLng = temp;
        }
      }

      console.log('修正后的经纬度:', {
        startFixed: { lat: validStartLat, lng: validStartLng },
        endFixed: { lat: validEndLat, lng: validEndLng }
      });
    }

    // 将地理坐标转换为地图坐标 - 确保经纬度顺序正确
    // 注意：projection函数期望的顺序是[lng, lat]
    console.log('投影前的坐标:', [validStartLng, validStartLat], [validEndLng, validEndLat]);
    const [startX, startY] = projection([validStartLng, validStartLat]);
    const [endX, endY] = projection([validEndLng, validEndLat]);
    console.log('投影后的坐标:', { startX, startY, endX, endY });

    // 应用缩放比例
    const scaleFactor = 1 / 6;
    const scaledStartX = startX * scaleFactor;
    const scaledStartY = -startY * scaleFactor; // Y坐标需要取反
    const scaledEndX = endX * scaleFactor;
    const scaledEndY = -endY * scaleFactor; // Y坐标需要取反

    // 设置起点和终点高度为原来的光柱底部高度
    const startHeight = BASE_Z + LAYER_HEIGHT * 1; // 保持原来的高度不变
    const endHeight = BASE_Z + LAYER_HEIGHT * 1; // 保持原来的高度不变

    // 计算控制点（弧线高度）
    const distance = Math.sqrt(
      Math.pow(scaledEndX - scaledStartX, 2) +
      Math.pow(scaledEndY - scaledStartY, 2)
    );

    // 控制点高度与距离成正比，确保曲线更圆滑地连接边界
    const arcHeight = distance * 0.4; // 减小弧度高度，使飞线更贴近地面

    // 创建起点、终点和控制点
    const start = new THREE.Vector3(scaledStartX, scaledStartY, startHeight);
    const end = new THREE.Vector3(scaledEndX, scaledEndY, endHeight);

    // 根据地图偏移调整位置
    if (mapGroup && mapGroup.position) {
      start.x += mapGroup.position.x;
      start.y += mapGroup.position.y;
      start.z += mapGroup.position.z;

      end.x += mapGroup.position.x;
      end.y += mapGroup.position.y;
      end.z += mapGroup.position.z;
    }

    // 控制点位于起点和终点的中间，但高度更高
    const controlPoint = new THREE.Vector3(
      (start.x + end.x) / 2,
      (start.y + end.y) / 2,
      Math.max(start.z, end.z) + arcHeight
    );

    // 创建起点和终点的延伸点，确保曲线能完全覆盖到国界线
    // 计算方向矢量
    const startDirection = new THREE.Vector3().subVectors(controlPoint, start).normalize();
    const endDirection = new THREE.Vector3().subVectors(controlPoint, end).normalize();

    // 大幅延伸起点和终点，使飞线在地图底部延长
    const extensionFactor = 0.25; // 增加延伸因子，延长更多
    const extendedStart = start.clone().sub(startDirection.clone().multiplyScalar(extensionFactor));
    const extendedEnd = end.clone().sub(endDirection.clone().multiplyScalar(extensionFactor));

    // 创建二次贝塞尔曲线，使用延伸的点
    const baseCurve = new THREE.QuadraticBezierCurve3(
      extendedStart,
      controlPoint,
      extendedEnd
    );

    // 曲线分段数 - 距离越远分段越多
    const segments = Math.max(20, Math.min(100, Math.floor(distance * 50)));

    // 创建管道几何体，优化飞线粗细
    const tubeRadius = 0.15; // 飞线管道半径 - 调整为1.5，非常明显
    const tubeSegments = 12; // 减少分段数以提高性能
    const tubeClosed = false; // 不闭合端点

    // 创建基础管道几何体
    const baseGeometry = new THREE.TubeGeometry(
      baseCurve,
      segments,
      tubeRadius,
      tubeSegments,
      tubeClosed
    );

    // 使用管道几何体
    const lineGeometry = baseGeometry;

    // 创建材质
    const material = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: color },
        time: { value: 0 },
        length: { value: baseCurve.getLength() },
        viewPosition: { value: camera.position.clone() },
        growthProgress: { value: 0.0 },
        growthOffset: { value: growthOffset }
      },
      vertexShader: `
        uniform vec3 viewPosition;
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewDir;
        varying float vGrowthVisible;
        uniform float growthProgress;
        uniform float growthOffset;
        
        void main() {
          vUv = uv;
          vPosition = position;
          vNormal = normalize(normalMatrix * normal);
          
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vViewDir = normalize(viewPosition - worldPosition.xyz);
          
          // 根据growthOffset计算生长可见性
          vGrowthVisible = ((uv.x + growthOffset) <= growthProgress) ? 1.0 : 0.0;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float time;
        uniform float length;
        uniform float growthProgress;
        uniform float growthOffset;
        
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewDir;
        varying float vGrowthVisible;
        
        void main() {
          if (vGrowthVisible < 0.5) {
            discard;
          }
          
          float viewAngle = abs(dot(vNormal, vViewDir));
          
          // 使用u纹理坐标加上时间作为流动效果的基础
          float flowPos = vUv.x * 2.0 - time * 0.5;
          float flow = fract(flowPos);
          
          // 生成多条流线 - 调整为80%有颜色，20%白色
          float line1 = smoothstep(0.0, 0.6, flow) * smoothstep(1.0, 0.8, flow) * 0.8;
          float line2 = smoothstep(0.1, 0.7, flow) * smoothstep(0.9, 0.75, flow) * 0.6;
          
          // 合并流线效果
          float flowEffect = line1 + line2;
          
          // 边缘发光效果
          float edgeGlow = smoothstep(0.0, 0.4, vUv.y) * smoothstep(1.0, 0.6, vUv.y);
          
          // 颜色混合 - 加深基础颜色并增加蓝色动画效果
          vec3 deepColor = color.rgb * 0.8; // 加深基础颜色
          vec3 brightColor = vec3(0.3, 0.6, 1.0); // 蓝色动画效果
          float brightFactor = 1.0 - viewAngle * 0.3;
          vec3 finalColor = mix(deepColor, brightColor, flowEffect * edgeGlow) * brightFactor;
          
          // 生长前沿效果 - 使用更亮的蓝色
          float growthEdge = smoothstep((growthProgress - growthOffset) - 0.03, (growthProgress - growthOffset), vUv.x);
          finalColor += growthEdge * vec3(0.4, 0.7, 1.0) * 1.5; // 增加亮度
          
          // 增加整体亮度和饱和度
          float alpha = 0.85 + flowEffect * 0.15; // 增加基础透明度
          
          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      depthTest: false,
      blending: THREE.AdditiveBlending
    });

    // 创建管道网格
    const line = new THREE.Mesh(lineGeometry, material);
    line.renderOrder = 9000; // 显著提高渲染顺序，确保最高优先级
    line.frustumCulled = false; // 禁用视锥体剔除，确保始终渲染

    // 标记为飞线，用于区分国界线
    line.userData.isFlightLine = true;

    // 关键：对飞线禁用深度测试，这不会影响国界线
    if (line.material) {
      line.material.depthTest = false; // 禁用深度测试，确保飞线不被其他对象遮挡
      line.material.depthWrite = false; // 不写入深度缓冲区
      line.material.transparent = true; // 启用透明度
      line.material.needsUpdate = true; // 强制材质更新
    }

    // 在每次渲染前更新飞线状态
    line.onBeforeRender = function (renderer, scene, camera) {
      // 更新飞线的世界矩阵，确保正确的位置计算
      this.updateMatrixWorld(true);

      // 确保材质始终面向相机
      if (this.material.uniforms && this.material.uniforms.viewPosition) {
        this.material.uniforms.viewPosition.value.copy(camera.position);
      }

      // 强制可见性标志为真
      this.visible = true;
    };
    line.userData = {
      routeIndex: index,
      startPoint: startPoint,
      endPoint: endPoint,
      time: 0          // 用于动画
    };

    // 命名线条以便后续识别
    line.name = `flightLine-${index}`;

    // 创建起点和终点的光柱
    const startPillar = createPillar(start, color);
    const endPillar = createPillar(end, color);
    
    // 将光柱添加到场景
    flightGroup.add(startPillar);
    flightGroup.add(endPillar);
    
    // 记录光柱以便后续清理
    routeFlightLines.push(startPillar);
    routeFlightLines.push(endPillar);

    // 修改起点和终点的高度，使其从光柱顶部开始
    const startTop = start.clone();
    const endTop = end.clone();
    startTop.z += PILLAR_HEIGHT - 0.02; // 略微降低起点，确保与光柱顶部无缝连接
    endTop.z += PILLAR_HEIGHT - 0.02; // 略微降低终点，确保与光柱顶部无缝连接

    // 调整控制点，使曲线从光柱顶部平滑延伸
    const flightControlPoint = new THREE.Vector3(
      (startTop.x + endTop.x) / 2,
      (startTop.y + endTop.y) / 2,
      Math.max(startTop.z, endTop.z) + arcHeight * 0.8 // 稍微降低控制点高度，使曲线更平滑
    );

    // 创建飞线曲线
    const flightCurve = new THREE.QuadraticBezierCurve3(
      startTop,
      flightControlPoint,
      endTop
    );

    // 创建飞线几何体
    const flightGeometry = new THREE.TubeGeometry(
      flightCurve,
      segments,
      tubeRadius,
      tubeSegments,
      tubeClosed
    );

    // 创建飞线材质
    const flightMaterial = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: color },
        time: { value: 0 },
        length: { value: flightCurve.getLength() },
        viewPosition: { value: camera.position.clone() },
        growthProgress: { value: 0.0 },
        growthOffset: { value: growthOffset }
      },
      vertexShader: `
        uniform vec3 viewPosition;
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewDir;
        varying float vGrowthVisible;
        uniform float growthProgress;
        uniform float growthOffset;
        
        void main() {
          vUv = uv;
          vPosition = position;
          vNormal = normalize(normalMatrix * normal);
          
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vViewDir = normalize(viewPosition - worldPosition.xyz);
          
          // 根据growthOffset计算生长可见性
          vGrowthVisible = ((uv.x + growthOffset) <= growthProgress) ? 1.0 : 0.0;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float time;
        uniform float length;
        uniform float growthProgress;
        uniform float growthOffset;
        
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewDir;
        varying float vGrowthVisible;
        
        void main() {
          // 如果在当前生长进度之外，则完全透明
          if (vGrowthVisible < 0.5) {
            discard;
          }
          
          float viewAngle = abs(dot(vNormal, vViewDir));
          
          // 使用u纹理坐标加上时间作为流动效果的基础
          float flowPos = vUv.x * 2.0 - time * 0.5;
          float flow = fract(flowPos);
          
          // 生成多条流线 - 第二段与第一段保持一致
          float line1 = smoothstep(0.0, 0.6, flow) * smoothstep(1.0, 0.8, flow) * 0.8;
          float line2 = smoothstep(0.1, 0.7, flow) * smoothstep(0.9, 0.75, flow) * 0.6;
          
          // 合并流线效果
          float flowEffect = line1 + line2;
          
          // 边缘发光效果
          float edgeGlow = smoothstep(0.0, 0.4, vUv.y) * smoothstep(1.0, 0.6, vUv.y);
          
          // 颜色混合
          vec3 deepColor = color.rgb * 0.7;  // 基础颜色
          vec3 brightColor = vec3(0.4, 0.6, 1.0);  // 明亮的蓝色
          float brightFactor = 1.0 - viewAngle * 0.3;
          vec3 finalColor = mix(deepColor, brightColor, flowEffect * edgeGlow) * brightFactor;
          
          // 生长前沿效果
          float growthEdge = smoothstep((growthProgress - growthOffset) - 0.03, (growthProgress - growthOffset), vUv.x);
          finalColor += growthEdge * vec3(0.5, 0.7, 1.0) * 1.5;  // 更亮的蓝色前沿
          
          // 透明度
          float alpha = 0.75 + flowEffect * 0.1;
          
          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      depthTest: false,
      blending: THREE.AdditiveBlending
    });

    // 创建飞线网格
    const flightLine = new THREE.Mesh(flightGeometry, flightMaterial);
    
    // 设置飞线属性
    flightLine.renderOrder = 9000;
    flightLine.frustumCulled = false;
    flightLine.userData = {
      isFlightLine: true,
      routeIndex: index,
      startPoint: startPoint,
      endPoint: endPoint,
      time: 0
    };
    
    // 设置材质属性
    if (flightLine.material) {
      flightLine.material.depthTest = false;
      flightLine.material.depthWrite = false;
      flightLine.material.transparent = true;
      flightLine.material.needsUpdate = true;
    }

    // 添加到场景
    flightGroup.add(flightLine);
    routeFlightLines.push(flightLine);

    return flightLine;
  } catch (error) {
    console.error('创建飞线时出错:', error);
    return null;
  }
};

// 修改飞线动画更新函数，添加相机和其他对象的存在性检查
const updateFlightLines = (deltaTime) => {
  if (!routeFlightLines || routeFlightLines.length === 0) return;

  routeFlightLines.forEach(line => {
    if (line && line.material && line.material.uniforms) {
      // 更新时间动画
      if (line.material.uniforms.time) {
        line.material.uniforms.time.value += deltaTime;
      }

      // 更新生长动画
      if (line.material.uniforms.growthProgress) {
        const progress = line.material.uniforms.growthProgress.value;
        const MAX_SEGMENTS = 2.0;  // 总段数：国内段 + 国际段
        const GROWTH_SPEED = 0.5;  // 控制生长速度

        // 不管是哪一段，都更新动画
        if (progress < MAX_SEGMENTS) {
          const newProgress = Math.min(MAX_SEGMENTS, progress + deltaTime * GROWTH_SPEED);
          line.material.uniforms.growthProgress.value = newProgress;
          renderNeedsUpdate = true;
        }
      }
    }
  });
};

// 修改animate函数，添加安全检查
const animate = () => {
  // 如果组件已卸载但animate仍被调用，直接返回
  if (!containerRef.value || !scene) {
    return;
  }

  requestAnimationFrame(animate);

  // 帧率限制，减少渲染频率以提高性能
  frameCounter++;

  // 检查是否有飞线存在，如果有，始终确保渲染
  const hasFlightLines = routeFlightLines && routeFlightLines.length > 0;

  // 如果有飞线，确保渲染；否则按照性能限制进行渲染
  if (hasFlightLines ||
    !PERFORMANCE.RENDER_LIMIT_ENABLED ||
    frameCounter % PERFORMANCE.FRAME_THROTTLE === 0 ||
    isHighPerformanceMode ||
    renderNeedsUpdate) {
    // 继续渲染
  } else {
    return; // 跳过某些帧的渲染，但优先级较高的更新不跳过
  }

  // 帧率监控和性能调整
  const now = performance.now();
  frameCount++;
  const elapsedTime = (now - lastTime) / 1000;

  if (elapsedTime >= frameUpdateInterval) {
    const fps = Math.round(frameCount / elapsedTime);
    if (dynamicQuality) {
      adjustQuality(fps);
    }
    frameCount = 0;
    lastTime = now;
  }

  // 添加控制器存在检查
  if (controls) {
    controls.update();
  }

  // 安全地更新飞线动画
  if (camera && routeFlightLines.length > 0) {
    updateFlightLines(1 / 60); // 使用固定的帧率进行动画更新
  }

  // 更新城市光柱脉冲效果
  if (window.updateCityPulses) {
    window.updateCityPulses(1 / 60);
  }

  // 性能优化：只有场景有变化或粒子系统活跃时才渲染
  const shouldRender =
    (controls && controls.changed) ||
    particleSystem ||
    isRendering ||
    renderNeedsUpdate;

  if (shouldRender && renderer && camera) {
    // 前置渲染处理：额外的相机位置限制
    if (camera) {
      // 确保相机不会进入地图底部
      camera.position.z = clamp(camera.position.z, 1, Infinity);

      // 如果使用了轨道控制器，确保目标点在合理范围
      if (controls && controls.target) {
        controls.target.z = clamp(controls.target.z, 0, Infinity);
      }
    }

    // 确保每帧都正确设置背景颜色
    if (scene) {
      scene.background = new THREE.Color(COLORS.background);
    }

    if (renderer) {
      renderer.setClearColor(COLORS.background);
      renderer.autoClear = false;
      renderer.clear();

      // 根据场景存在情况进行渲染
      if (particleScene) {
        renderer.render(particleScene, camera);
        renderer.clearDepth();
      }

      if (mapScene) {
        renderer.render(mapScene, camera);
      }

      // 渲染CSS2D标签
      if (labelRenderer) {
        labelRenderer.render(mapScene, camera);
      }
    }

    isRendering = false; // 设置为false，等待控制器或粒子变化时再次渲染
    renderNeedsUpdate = false; // 重置渲染更新标志
  }
};

// 在第1040行将第二个animate函数改名为animate2，这样就不会有冲突
const animate2 = () => {
  requestAnimationFrame(animate)

  // 帧率限制，减少渲染频率以提高性能
  frameCounter++;

  // 检查是否有飞线存在，如果有，始终确保渲染
  const hasFlightLines = routeFlightLines && routeFlightLines.length > 0;

  // 如果有飞线，确保渲染；否则按照性能限制进行渲染
  if (hasFlightLines ||
    !PERFORMANCE.RENDER_LIMIT_ENABLED ||
    frameCounter % PERFORMANCE.FRAME_THROTTLE === 0 ||
    isHighPerformanceMode ||
    renderNeedsUpdate) {
    // 继续渲染
  } else {
    return; // 跳过某些帧的渲染，但优先级较高的更新不跳过
  }

  // 帧率监控和性能调整
  const now = performance.now();
  frameCount++;
  const elapsedTime = (now - lastTime) / 1000;

  if (elapsedTime >= frameUpdateInterval) {
    const fps = Math.round(frameCount / elapsedTime);
    if (dynamicQuality) {
      adjustQuality(fps);
    }
    frameCount = 0;
    lastTime = now;
  }

  // 添加控制器存在检查
  if (controls) {
    controls.update();
  }

  // 安全地更新飞线动画
  if (camera && routeFlightLines.length > 0) {
    updateFlightLines(1 / 60); // 使用固定的帧率进行动画更新
  }

  // 更新城市光柱脉冲效果
  if (window.updateCityPulses) {
    window.updateCityPulses(1 / 60);
  }

  // 性能优化：只有场景有变化或粒子系统活跃时才渲染
  const shouldRender =
    (controls && controls.changed) ||
    particleSystem ||
    isRendering ||
    renderNeedsUpdate;

  if (shouldRender && renderer && camera) {
    // 前置渲染处理：额外的相机位置限制
    if (camera) {
      // 确保相机不会进入地图底部
      camera.position.z = clamp(camera.position.z, 1, Infinity);

      // 如果使用了轨道控制器，确保目标点在合理范围
      if (controls && controls.target) {
        controls.target.z = clamp(controls.target.z, 0, Infinity);
      }
    }

    // 确保每帧都正确设置背景颜色
    if (scene) {
      scene.background = new THREE.Color(COLORS.background);
    }

    if (renderer) {
      renderer.setClearColor(COLORS.background);
      renderer.autoClear = false;
      renderer.clear();

      // 根据场景存在情况进行渲染
      if (particleScene) {
        renderer.render(particleScene, camera);
        renderer.clearDepth();
      }

      if (mapScene) {
        renderer.render(mapScene, camera);
      }

      // 渲染CSS2D标签
      if (labelRenderer) {
        labelRenderer.render(mapScene, camera);
      }
    }

    isRendering = false; // 设置为false，等待控制器或粒子变化时再次渲染
    renderNeedsUpdate = false; // 重置渲染更新标志
  }
}

// 创建路线端点标记函数
const createRoutePointMarker = (position, name, color, size = 0.2) => {
  // 创建端点球体标记
  const geometry = new THREE.SphereGeometry(size, 16, 16);
  const material = new THREE.MeshBasicMaterial({
    color: color,
    transparent: true,
    opacity: 0.8
  });

  const sphere = new THREE.Mesh(geometry, material);
  sphere.position.copy(position);

  mapScene.add(sphere);
  routeFlightLines.push(sphere); // 添加到飞线集合中以便后续清理

  // 添加站点名称标签
  const labelDiv = document.createElement('div');
  labelDiv.className = 'route-label';
  labelDiv.textContent = name;
  labelDiv.style.color = '#FFFFFF';
  labelDiv.style.backgroundColor = `rgba(${color.r * 255}, ${color.g * 255}, ${color.b * 255}, 0.7)`;
  labelDiv.style.padding = '2px 5px';
  labelDiv.style.borderRadius = '3px';
  labelDiv.style.fontSize = '12px';

  const label = new CSS2DObject(labelDiv);
  label.position.set(0, 0, 0.5); // 稍微偏移标签

  sphere.add(label);

  return sphere;
};

// 添加热门城市数据
let HOT_CITIES = []; // 热门城市列表将由接口动态填充

// 动态加载热门站点数据
const loadHotCities = async () => {
  try {
    const stations = await getHotStations();
    if (Array.isArray(stations)) {
      // 统一字段，方便后续使用
      HOT_CITIES = stations.map(s => ({
        name: s.nameEn || s.name || '',
        nameZh: s.name || s.nameZh || '',
        latitude: parseFloat(s.lat ?? s.latitude),
        longitude: parseFloat(s.lng ?? s.longitude)
      }));
      console.log('已加载热门站点:', HOT_CITIES);
    }
  } catch (err) {
    console.error('加载热门站点失败:', err);
  }
};

// 光柱效果相关常量
const BEAM_HEIGHT = 4.0; // 光柱高度
const BEAM_RADIUS = 0.125; // 光柱半径，减小一半
const BEAM_COLOR = 0x41F8FF; // 科技感蓝色
const LABEL_OFFSET_Y = 0.5; // 标签Y轴偏移

// 1. 首先定义所有常量
const RADIUS = 320  // 进一步增大粒子系统半径，扩大覆盖范围
const BASE_Z = 1.1  // 地图的起始高度设为1.1
const LAYER_HEIGHT = 1.63  // 调整层高，确保总高度到达6 (1.1 + 3层 * 1.63 ≈ 6)
const TOP_Z = LAYER_HEIGHT * 3   // 顶层高度 = 6
const MARKER_Z = LAYER_HEIGHT + 0.0005 // 比国界线高0.0005
const PARTICLE_Z = 0.3  // 将粒子系统Z轴位置调低到0.3，确保在地图下方

// 科技风配色方案
const COLORS = {
  background: 0xE2F0FD,      // 修改为浅蓝色背景 #E2F0FD
  hover: 0xD8DDDF,          // 悬停色 - 浅灰色略带白色
  selected: 0x9EDFFF,       // 选中色 - 浅蓝色略带白色
  layers: {
    0: 0x90C8F4,           // 最底层 - #0F425A
    1: 0x90C8F4,           // 中间层 - #165A7A
    2: 0x90C8F4            // 最上层 - #31C6FF
  }
}

// 替换原有的 LAYER_COLORS
const LAYER_COLORS = COLORS.layers

// 核心状态
const containerRef = ref(null)
const currentCountryName = ref('')
const isLoading = ref(true) // 添加加载状态
const loadingProgress = ref(0) // 添加加载进度变量

// Three.js 实例
let scene, camera, renderer, controls
let mapScene, particleScene, starScene
let raycastObjs = []
let lineObjs = []
let particleSystem
let mapGroup; // 添加全局变量引用地图组
let cityBeams = []; // 存储城市光柱对象
let cityLabels = []; // 存储城市标签
let labelRenderer; // CSS2D标签渲染器

// 添加性能检测函数
const detectPerformanceTier = () => {
  const tier = navigator.hardwareConcurrency || 4;
  // 简单用 CPU 核数估算：核数越少，粒子越少
  return Math.min(Math.max(tier, 2), 8);
};

// 动态调整粒子数量
const tier = detectPerformanceTier();
const PARTICLE_COUNT = Math.floor(5000 * (tier / 4));
const STAR_COUNT = Math.floor(2000 * (tier / 4));

// 添加射线和鼠标状态
const raycaster = new THREE.Raycaster()
const mouse = new THREE.Vector2()
let INTERSECTED = null
let CLICKED = null

// 添加高亮效果函数
function highlightCountry(shapes, isHovered, isSelected, isLargeCountry = false) {
  if (!shapes || shapes.length === 0) return;

  // 为大型国家设置更强的高亮效果
  const hoverColor = isLargeCountry ? new THREE.Color(0xE8F0FF) : new THREE.Color(COLORS.hover);

  shapes.forEach((shape) => {
    if (!shape) return;

    // 检查是否是国界线
    const isBorderLine = shape.userData && shape.userData.isBorderLine;

    if (isBorderLine) {
      // 处理国界线 - 保持原有的显示状态，不进行高亮处理
      // 国界线始终保持原来的白色显示，不参与高亮效果
      // 这样可以避免选中国家时国界线消失的问题
      return; // 直接跳过国界线的处理
    } else {
      // 处理国家形状
      const layerIndex = shape.userData ? shape.userData.layerIndex || 0 : 0;

      // 重置到默认状态
      if (!isHovered && !isSelected) {
        const newMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(LAYER_COLORS[layerIndex]),
          transparent: false,
          opacity: 1.0
        });
        if (shape.material) shape.material.dispose();
        shape.material = newMaterial;
      }
      // 应用悬停效果
      else if (isHovered && !isSelected) {
        const newMaterial = new THREE.MeshBasicMaterial({
          color: hoverColor,
          transparent: false,
          opacity: 1.0
        });
        if (shape.material) shape.material.dispose();
        shape.material = newMaterial;
      }
      // 应用选中效果
      else if (isSelected) {
        const newMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(COLORS.selected),
          transparent: false,
          opacity: 1.0
        });
        if (shape.material) shape.material.dispose();
        shape.material = newMaterial;
        shape.renderOrder = 2000 + layerIndex;
      }
    }
  });

  // 强制触发一次渲染更新
  renderNeedsUpdate = true;
}

// 添加性能监控变量
let lastTime = 0;
let frameCount = 0;
let frameCounter = 0; // 添加帧计数器用于帧率控制
const FRAME_THROTTLE = 2; // 控制每N帧渲染一次
let frameUpdateInterval = 1; // 每秒更新一次
let dynamicQuality = true;   // 动态质量调整
let currentFPS = 60;         // 当前帧率估计
let isHighPerformanceMode = false; // 添加高性能模式标记
let renderNeedsUpdate = true;      // 添加渲染更新标记，控制选择性渲染
let isRendering = true;      // 渲染状态标志
let updateStep = 1;          // 初始化粒子更新步长变量

// 动态性能调整函数
const adjustQuality = (fps) => {
  currentFPS = fps;

  // 保存当前材质引用，以便后续恢复
  if (!window._savedMaterials && particleSystem) {
    window._savedMaterials = {
      particleOpacity: particleSystem.material.opacity,
      starCount: STAR_COUNT,
      particleCount: PARTICLE_COUNT,
      pixelRatio: renderer ? renderer.getPixelRatio() : 1
    };
  }

  // 如果帧率低于30，逐步降低特效质量
  if (fps < 30 && particleSystem) {
    // 降低粒子系统透明度，减少混合计算
    particleSystem.material.opacity = Math.max(0.5, particleSystem.material.opacity - 0.1);

    // 如果帧率严重下降，可以更激进地降低质量
    if (fps < 20) {
      particleSystem.material.opacity = 0.3;
      // 减少每帧更新的粒子数量
      updateStep = 5;

      // 降低渲染分辨率
      if (renderer && window._savedMaterials) {
        const currentRatio = renderer.getPixelRatio();
        if (currentRatio > 1) {
          renderer.setPixelRatio(Math.max(1, currentRatio - 0.25));
        }
      }
    }
  } else if (fps > 45 && window._savedMaterials && particleSystem) {
    // 帧率良好时恢复原有质量
    particleSystem.material.opacity = Math.min(
      window._savedMaterials.particleOpacity,
      particleSystem.material.opacity + 0.05
    );
    updateStep = 1;

    // 恢复渲染分辨率
    if (renderer && window._savedMaterials && fps > 55) {
      const savedRatio = window._savedMaterials.pixelRatio;
      const currentRatio = renderer.getPixelRatio();
      if (currentRatio < savedRatio) {
        renderer.setPixelRatio(Math.min(savedRatio, currentRatio + 0.25));
      }
    }
  }
};

// 添加高性能模式切换函数
const togglePerformanceMode = () => {
  isHighPerformanceMode = !isHighPerformanceMode;

  if (isHighPerformanceMode) {
    // 高性能模式：降低效果，提高性能
    if (particleSystem) {
      particleSystem.material.opacity = 0.5;
    }
    // 减少动画计算并降低渲染质量
    renderer.setPixelRatio(1.0);
    updateStep = 4; // 设置较大的更新步长，减少计算量
  } else {
    // 标准模式：恢复效果
    if (particleSystem && window._savedMaterials) {
      particleSystem.material.opacity = window._savedMaterials.particleOpacity;
    }
    // 恢复原有设置
    renderer.setPixelRatio(window.devicePixelRatio);
    updateStep = 1; // 恢复默认更新步长
  }

  // 强制一次完整渲染
  renderNeedsUpdate = true;
}

// 添加星空初始化函数 - 调整星空尺寸
const initStarfield = () => {
  // 不再创建星空
  // 保留函数但不创建星空元素
  console.log('星空效果已禁用');
}

// 修改光照设置，简化以提高性能
const setupLights = () => {
  // 我们现在使用MeshBasicMaterial不受光照影响，所以不需要光照
  // 但仍保留一个微弱的环境光，以便其他需要光照的元素（如城市光柱）可见
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.8)
  mapScene.add(ambientLight)
}

// 修改控制器设置
const setupControls = () => {
  controls.enableDamping = true
  controls.dampingFactor = 0.1
  controls.rotateSpeed = 0.5  // 降低旋转速度，提供更平滑的体验
  controls.enableZoom = true
  controls.zoomSpeed = 1.2    // 调整缩放速度
  controls.enablePan = false  // 关闭平移功能
  controls.enableRotate = true
  controls.minDistance = 30   // 减少最小距离 - 控制最大缩放程度
  controls.maxDistance = 210   // 减少最大距离 - 控制最小缩放程度

  // 设置垂直旋转限制，防止从底部查看地图
  // 极角范围：0 = 正上方（天顶），PI/2 = 水平视角，PI = 正下方
  controls.minPolarAngle = 0;           // 最小极角，0表示可以从正上方查看
  controls.maxPolarAngle = Math.PI / 2;   // 最大极角，限制在水平面以上，无法从下方查看

  controls.target.set(0, 0, 0)
  controls.update()

  // 添加相机位置限制，确保不会穿透地图底面
  controls.addEventListener('change', () => {
    // 确保相机始终在地图上方，不会穿透底面
    camera.position.z = clamp(camera.position.z, 1, Infinity);

    // 限制目标点高度，确保不会看向地图底面
    // 这里限制target的z值大于等于0，避免相机向下看
    controls.target.z = clamp(controls.target.z, 0, Infinity);

    // 当相机和目标点都在极低的位置，强制提升相机高度
    if (camera.position.z < 5 && controls.target.z < 1) {
      camera.position.z = 5; // 保持最小高度
    }

    renderNeedsUpdate = true; // 控制器变化时触发渲染
  });
}

// 添加性能优化代码
let lastFrameTime = 0;

// 添加钳制函数，确保值在最小和最大范围内
const clamp = (value, min, max) => {
  return Math.max(min, Math.min(max, value));
};

// 监听语言变化，更新相关UI
watch(() => currentLocale.value, (newLocale) => {
  console.log('语言切换为:', newLocale);
  
  // 如果当前有选中的国家，更新其名称显示
  if (CLICKED) {
    const countryName = CLICKED.userData?.properties?.NAME;
    if (countryName) {
      updateCountryName(countryName);
      // 确保切换语言后国家仍然保持高亮
      highlightCountry(CLICKED.userData.relatedShapes, false, true);
    }
  }

  // 更新城市光柱标签 (直接使用 cityLabels 数组)
  cityLabels.forEach((labelObj, index) => {
    if (!labelObj || !labelObj.element) return;
    const city = HOT_CITIES[index];
    if (!city) return;
    const labelText = isEnglish.value ? city.name : city.nameZh;
    labelObj.element.textContent = labelText;
  });

  // 强制更新渲染
  renderNeedsUpdate = true;
}, { immediate: false });

// 更新国家名称显示
const updateCountryName = (name) => {
  // 检查name是否有效
  if (!name) {
    console.error('国家名称无效:', name);
    currentCountryName.value = '';
    return;
  }

  try {
    if (isEnglish.value) {
      currentCountryName.value = name;
    } else {
      // 检查COUNTRY_NAMES中是否存在对应的中文名称
      if (COUNTRY_NAMES[name]) {
        currentCountryName.value = COUNTRY_NAMES[name];
      } else {
        console.warn(`未找到国家 "${name}" 的中文名称，使用原名`);
        currentCountryName.value = name;
      }
    }
  } catch (error) {
    console.error('更新名称时出错:', error);
    // 使用英文名作为后备
    currentCountryName.value = name;
  }
}

// 修改鼠标移动处理，禁用悬停高亮效果
const onMouseMove = (event) => {
  event.preventDefault();

  // 计算标准化设备坐标
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

  // 初始化射线投射器，但不执行高亮逻辑
  raycaster.setFromCamera(mouse, camera);

  // 禁用原有的悬停高亮效果，只保留射线检测以供其他功能使用
  // 不再调用highlightCountry函数来处理悬停效果
};

// 修改点击处理，禁用国家高亮效果
const onMouseClick = (event) => {
  event.preventDefault();

  // 计算鼠标位置
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

  // 初始化射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 保留点击检测功能，并重新启用高亮效果
  if (mapGroup) {
    const intersects = raycaster.intersectObjects(raycastObjs, false);

    if (intersects.length > 0) {
      // 先清除之前选中国家的高亮效果
      if (CLICKED && CLICKED.userData && CLICKED.userData.relatedShapes) {
        highlightCountry(CLICKED.userData.relatedShapes, false, false);
      }

      // 更新选中对象
      CLICKED = intersects[0].object;

      // 检查CLICKED是否有userData和properties
      if (!CLICKED.userData || !CLICKED.userData.properties) {
        console.error('选中的对象缺少userData或properties属性:', CLICKED);
          return;
        }

      // 提取国家名称，确保存在
      const countryName = CLICKED.userData.properties.NAME;
      if (!countryName) {
        console.error('国家名称不存在:', CLICKED.userData.properties);
        return;
      }

      // 使用updateCountryName函数更新名称
      updateCountryName(countryName);

      // 应用高亮效果
      if (CLICKED.userData.relatedShapes) {
        // 检查是否为大国家，如果是则使用特殊高亮
        const isLargeCountry = CLICKED.userData.properties.NAME === "Russia" ||
          CLICKED.userData.properties.NAME === "Canada" ||
          CLICKED.userData.properties.NAME === "United States of America";

        // 确保国界线对象被正确传递
        const shapes = CLICKED.userData.relatedShapes;
        const borderLine = CLICKED.userData.borderLine;
        
        // 不再将国界线加入 shapes，以避免高亮逻辑影响国界线
        highlightCountry(shapes, false, true, isLargeCountry);
      }
    } else {
      // 清除选中状态和高亮效果
      if (CLICKED && CLICKED.userData && CLICKED.userData.relatedShapes) {
        highlightCountry(CLICKED.userData.relatedShapes, false, false);
      }
      CLICKED = null;
      currentCountryName.value = '';
    }

    // 强制渲染更新
    renderNeedsUpdate = true;
  }
}

// 添加脉冲控制变量
let pulseProgress = 0  // 脉冲进度，0-1
let pulseSpeed = 0.16  // 进一步降低脉冲速度，使高亮效果更易于观察
let isPaused = false   // 是否暂停
let pauseTimer = 0     // 暂停计时器
let pulseWidth = 0.1  // 脉冲宽度 - 按要求调整为更窄的宽度
let pulseFullyDisappeared = false // 标记脉冲是否完全消失

// 创建圆形纹理的函数
const createCircleTexture = () => {
  // 创建一个小型画布
  const canvas = document.createElement('canvas');
  const size = 64; // 纹理大小
  canvas.width = size;
  canvas.height = size;

  // 获取2D上下文
  const ctx = canvas.getContext('2d');

  // 清除画布并设置透明背景
  ctx.clearRect(0, 0, size, size);

  // 创建径向渐变 - 从中心到边缘
  const gradient = ctx.createRadialGradient(
    size / 2, size / 2, 0,      // 内圆圆心和半径
    size / 2, size / 2, size / 2 // 外圆圆心和半径
  );

  // 设置渐变颜色 - 中心白色，边缘透明
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
  gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.8)');
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

  // 填充圆形
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();

  // 创建纹理
  const texture = new THREE.CanvasTexture(canvas);
  texture.needsUpdate = true;

  return texture;
};

// 添加初始化粒子系统函数 - 调整粒子尺寸和分布
const initParticleSystem = () => {
  const geometry = new THREE.BufferGeometry()
  const positions = new Float32Array(PARTICLE_COUNT * 3)
  const colors = new Float32Array(PARTICLE_COUNT * 3)
  const sizes = new Float32Array(PARTICLE_COUNT)
  const randoms = new Float32Array(PARTICLE_COUNT) // 添加随机偏移值

  // 计算粒子系统的真实中心位置
  const particleCenter = new THREE.Vector3(0, 0, 0); // 粒子系统的中心点

  // 性能优化：预计算随机值
  const randomValues = [];
  for (let i = 0; i < PARTICLE_COUNT; i++) {
    randomValues.push({
      r: RADIUS * (1 - Math.pow(Math.random(), 1.5)),
      theta: Math.random() * Math.PI * 2,
      random: Math.random() * Math.PI * 2
    });
  }

  for (let i = 0; i < PARTICLE_COUNT; i++) {
    // 使用预计算的随机值
    const r = randomValues[i].r;
    const theta = randomValues[i].theta;

    positions[i * 3] = r * Math.cos(theta)
    positions[i * 3 + 1] = r * Math.sin(theta)
    // 将粒子的高度设置在地图第三层下面
    positions[i * 3 + 2] = PARTICLE_Z

    const u = r / RADIUS
    sizes[i] = THREE.MathUtils.lerp(8, 1.5, u) // 调整粒子尺寸以适应新地图
    // 调整基础颜色 - 整体提亮并更偏向蓝色
    colors[i * 3] = 0.2 * (1 - u)      // 降低红色分量
    colors[i * 3 + 1] = 0.35 * (1 - u)  // 降低绿色分量
    colors[i * 3 + 2] = 0.8 * (1 - u)   // 提高蓝色分量

    // 随机偏移，使脉冲效果更自然
    randoms[i] = randomValues[i].random;
  }

  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
  geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))
  geometry.setAttribute('random', new THREE.BufferAttribute(randoms, 1)) // 存储随机偏移

  // 使用更简单的材质减轻GPU负担，并确保粒子为圆形
  const material = new THREE.PointsMaterial({
    vertexColors: true,
    transparent: true,
    opacity: 0.9, // 保持较高透明度，使粒子明显
    depthWrite: false,
    blending: THREE.AdditiveBlending,
    sizeAttenuation: true,
    map: createCircleTexture(), // 添加圆形纹理使粒子呈现圆形
    alphaTest: 0.1 // 添加透明度测试以确保圆形边缘清晰
  })

  particleSystem = new THREE.Points(geometry, material)

  // 确保粒子系统位于正确位置
  particleSystem.position.set(0, 0, 0);
  particleScene.add(particleSystem)

  // 存储粒子系统的位置，以便地图对齐
  window.particleSystemPosition = particleSystem.position.clone();
}

// 使用从projection.js导入的共享projection实例
// 这里不再重新定义，确保与Country.js使用相同的投影

// 保留宽高变量以便其他地方使用
const WIDTH = window.innerWidth
const HEIGHT = window.innerHeight

// 添加 setupProjection 函数定义 - 仅用于分析地图边界，不再修改projection实例
const setupProjection = (geojson) => {
  // 仅用于信息目的，不再修改全局projection
  const bounds = geoPath().bounds(geojson)
  const centerLng = (bounds[1][0] + bounds[0][0]) / 2
  const centerLat = (bounds[1][1] + bounds[0][1]) / 2

  // 不再重新设置projection，保持与Country.js共享的projection实例
}

// 添加地图位置状态
const mapPosition = ref(null);

// 模拟进度条更新函数
const simulateLoadingProgress = () => {
  // 设置初始进度
  loadingProgress.value = 10;

  // 创建快速增加到50%的定时器
  setTimeout(() => {
    loadingProgress.value = 50;
  }, 500);

  // 慢速增加到80%
  setTimeout(() => {
    loadingProgress.value = 80;
  }, 1500);

  // 最终进度在加载完成时通过completeLoading处理
};

// 加载地图数据时保存mapGroup引用
const loadMapData = async () => {
  try {
    isLoading.value = true; // 确保加载状态为true
    simulateLoadingProgress(); // 开始模拟进度更新

    // 修复地图数据加载路径 - 使用更可靠的相对路径
    const response = await fetch("./data/custom.geo (2).json")

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP错误 ${response.status}: ${response.statusText}`);
    }

    const geojson = await response.json()

    // 加载中国省级地图数据
    const chinaProvincesResponse = await fetch("./data/china-provinces.geojson")

    // 检查响应状态
    if (!chinaProvincesResponse.ok) {
      throw new Error(`HTTP错误 ${chinaProvincesResponse.status}: ${chinaProvincesResponse.statusText}`);
    }

    const chinaProvincesGeoJson = await chinaProvincesResponse.json()

    // 预先处理数据，修复缺少NAME的问题并限制国家数量
    let filteredFeatures = geojson.features.filter(feature => {
      // 排除无效的特征
      if (!feature || !feature.geometry) {
        console.warn('过滤掉无效特征(无geometry):', feature);
        return false;
      }

      // 确保properties存在
      if (!feature.properties) {
        feature.properties = {};
      }

      // 检查是否有NAME属性
      if (!feature.properties.NAME && feature.properties.name) {
        // 有些数据可能用小写的name
        feature.properties.NAME = feature.properties.name;
      }

      // 检查是否有NAME属性
      if (!feature.properties.NAME && feature.properties.ADMIN) {
        // 有些数据可能用ADMIN字段
        feature.properties.NAME = feature.properties.ADMIN;
      }

      return true;
    });

    // 预处理中国省级数据，统一属性格式
    const chinaProvinceFeatures = chinaProvincesGeoJson.features.map(feature => {
      // 确保properties存在
      if (!feature.properties) {
        feature.properties = {};
      }

      // 统一使用NAME作为属性名
      if (!feature.properties.NAME && feature.properties.name) {
        feature.properties.NAME = feature.properties.name;
      }

      // 标记为中国省份，以便特殊处理
      feature.properties.isChineseProvince = true;

      return feature;
    });

    // 性能优化：限制特征数量
    if (PERFORMANCE.MAX_VISIBLE_COUNTRIES > 0 && filteredFeatures.length > PERFORMANCE.MAX_VISIBLE_COUNTRIES) {
      // 保留具有NAME属性的较大特征
      filteredFeatures.sort((a, b) => {
        // 首先按是否有NAME属性排序
        const aHasName = a.properties.NAME ? 1 : 0;
        const bHasName = b.properties.NAME ? 1 : 0;

        if (aHasName !== bHasName) return bHasName - aHasName;

        // 然后按几何顶点数量排序，更大/更重要的国家通常有更多顶点
        const aSize = a.geometry.coordinates ? JSON.stringify(a.geometry.coordinates).length : 0;
        const bSize = b.geometry.coordinates ? JSON.stringify(b.geometry.coordinates).length : 0;

        return bSize - aSize;
      });

      filteredFeatures = filteredFeatures.slice(0, PERFORMANCE.MAX_VISIBLE_COUNTRIES);
    }

    // 在世界地图中移除中国，因为我们将使用更详细的省级数据
    filteredFeatures = filteredFeatures.filter(feature => {
      return !feature.properties.NAME ||
        (feature.properties.NAME !== "China" &&
          feature.properties.NAME !== "中国" &&
          feature.properties.name !== "China");
    });

    // 合并世界地图和中国省级数据
    geojson.features = [...filteredFeatures, ...chinaProvinceFeatures];

    // 注意：不再在这里重新定义projection，使用从projection.js导入的共享实例

    // 创建一个组来包含所有地图元素，便于整体移动
    mapGroup = new THREE.Group();
    mapScene.add(mapGroup);

    // 清空射线检测对象数组，避免重复添加
    raycastObjs = [];
    lineObjs = [];  // 重置国界线对象数组

    // 用于累计地图所有顶点位置的数组
    const allVertices = [];

    // 创建一个地图基准高度，确保地图在粒子系统上方
    const MAP_BASE_HEIGHT = 1.0; // 地图基准高度，位于粒子系统上方

    // 创建一个映射表，用于关联国家和它们的国界线
    const countryLineMap = new Map();
    // 性能优化：使用实例化渲染相似形状国家
    let instancedMeshes = {};

    // 添加大型国家特殊处理函数
    const isLargeCountry = (feature) => {
      // 俄罗斯和加拿大等大型国家需要特殊处理
      return feature && feature.properties && (
        feature.properties.NAME === "Russia" ||
        feature.properties.name === "Russia" ||
        feature.properties.NAME === "Canada" ||
        feature.properties.NAME === "United States of America"
      );
    };

    // 完整的加载过程
    geojson.features.forEach(feature => {
      // 确保feature.properties存在且有NAME属性
      if (!feature.properties) {
        feature.properties = {};  // 如果没有属性，创建一个空对象
      }

      if (!feature.properties.NAME) {
        // 对于缺少NAME的特征，生成一个临时标识
        feature.properties.NAME = `未命名区域_${Math.random().toString(36).substring(2, 10)}`;
      }

      // 检查是否为大型国家，如俄罗斯
      const isSpecialCountry = isLargeCountry(feature);

      // 检查是否为中国省份
      const isChineseProvince = feature.properties.isChineseProvince === true;

      try {
        // 假设中等距离（稍后根据相机距离动态更新）
        const distanceToCamera = 150;

        const country = new Country(feature.geometry, feature.properties)
        const shapes = [0, 1, 2].map(layer => country.createShape(layer, distanceToCamera))
        const line = country.createLine(distanceToCamera)

        // 确保形状被添加到组
        shapes.forEach((shape, index) => {
          if (shape && shape.geometry) {
            shape.geometry.computeVertexNormals()
            shape.userData = {
              properties: feature.properties,
              relatedShapes: shapes,
              layerIndex: index,
              borderLine: line,  // 添加国界线引用
              isChineseProvince: isChineseProvince // 标记是否为中国省份
            }

            // 设置材质颜色
            shape.material.color.setHex(LAYER_COLORS[index])
            // 关闭透明，确保颜色纯净
            shape.material.transparent = false
            shape.material.opacity = 1.0
            shape.material.depthTest = true
            shape.material.depthWrite = true
            shape.material.needsUpdate = true

            // 所有国家使用相同的渲染顺序
            shape.renderOrder = 1000 + index;

            // 如果是中国省份，调整渲染顺序以确保显示在其他国家之上
            if (isChineseProvince) {
              shape.renderOrder = 1500 + index; // 提高中国省份的渲染顺序
            }

            mapGroup.add(shape)  // 添加到组而不是直接添加到场景

            // 收集所有顶点坐标用于计算中心点
            if (shape.geometry && shape.geometry.attributes && shape.geometry.attributes.position) {
              const positions = shape.geometry.attributes.position.array;
              for (let i = 0; i < positions.length; i += 3) {
                allVertices.push(new THREE.Vector3(
                  positions[i],
                  positions[i + 1],
                  positions[i + 2]
                ));
              }
            }

            if (index === 2) {
              // 检查是否为大型国家如俄罗斯，设置更高的渲染顺序和更大的射线检测优先级
              if (isSpecialCountry) {
                shape.renderOrder = 1100 + index; // 提高大型国家的渲染顺序

                // 将大型国家添加到射线检测列表的最前面
                raycastObjs.unshift(shape);
              } else if (isChineseProvince) {
                shape.renderOrder = 1500 + index; // 提高中国省份的渲染顺序

                // 将中国省份添加到射线检测列表
                raycastObjs.push(shape);
              } else {
                // 其他国家正常添加到射线检测列表
                raycastObjs.push(shape);
              }

              // 将最顶层形状与国界线关联
              if (line) {
                countryLineMap.set(shape.id, line);
              }
            }
          }
        })

        // 设置边界线 - 确保边界线位于国家顶层上方
        if (line) {
          // 计算第三层的真实顶部高度，包括斜角效果
          const THIRD_LAYER_HEIGHT = country.LAYER_HEIGHT * 3;  // 修正为第三层的真实高度
          // 根据与相机的距离决定是否启用斜角及其厚度
          const BEVEL = distanceToCamera < 200 ? 0.05 : 0;
          // 将国界线放在真正的顶部，加上极小的偏移，移除BASE_Z因为地图meshes没有包含它
          line.position.z = THIRD_LAYER_HEIGHT + BEVEL + 0.001;

          // 为国界线标记类型，区分飞线
          line.userData.isBorderLine = true;

          // 设置渲染顺序比地图层高
          line.renderOrder = 3000;

          // 如果是中国省份边界，提高渲染顺序
          if (isChineseProvince) {
            line.renderOrder = 3500;
            line.userData.isChineseProvinceBorder = true;
          }

          // 确保禁用深度测试
          line.material.depthTest = false;
          line.material.depthWrite = false;
          line.material.transparent = true;
          line.material.opacity = 0.9;
          line.material.linewidth = 1.5;
          line.material.needsUpdate = true;

          // 对大型国家如俄罗斯加强边界线效果
          if (isSpecialCountry) {
            // 保持相同的Z轴位置，只增加透明度和线宽
            line.renderOrder = 3600;
            // 增加边界线不透明度和宽度
            line.material.color.setHex(0xFFFFFF); // 保持纯白色
            line.material.opacity = 0.9;  // 更明显的不透明度
            line.material.linewidth = 2.0; // 更粗的线宽
            line.material.needsUpdate = true;
          } else if (isChineseProvince) {
            // 中国省份边界线使用稍细的线条
            line.material.color.setHex(0xFFFFFF); // 保持纯白色
            line.material.opacity = 0.7;  // 适中的不透明度
            line.material.linewidth = 1.0; // 稍细的线宽
            line.material.needsUpdate = true;
          } else {
            // 统一使用相同的渲染优先级
            line.renderOrder = 3000;
            line.material.color.setHex(0xFFFFFF) // 纯白色
            line.material.opacity = 0.8 // 适中的不透明度
            line.material.linewidth = 1.2 // 适中的线宽
            line.material.needsUpdate = true;
          }

          // 禁用视锥剔除，确保始终渲染
          line.frustumCulled = false;

          line.userData = {
            countryId: shapes[2] ? shapes[2].id : null,  // 关联对应的国家ID
            isHighlighted: false,  // 用于跟踪高亮状态
            isChineseProvinceBorder: isChineseProvince // 标记是否为中国省份边界
          }
          mapGroup.add(line)         // 添加到组而不是直接添加到场景
          lineObjs.push(line)
        }
      } catch (error) {
        console.error('处理特征时出错:', feature.properties.NAME, error);
      }
    })

    // 确保已经收集了顶点数据
    if (allVertices.length > 0) {
      // 计算所有顶点的平均位置作为中心点
      const sumPosition = allVertices.reduce((sum, vertex) => {
        sum.x += vertex.x;
        sum.y += vertex.y;
        sum.z += vertex.z;
        return sum;
      }, new THREE.Vector3(0, 0, 0));

      const vertexCenter = new THREE.Vector3(
        sumPosition.x / allVertices.length,
        sumPosition.y / allVertices.length,
        sumPosition.z / allVertices.length
      );

      // 应用偏移，使地图与粒子系统中心对齐
      // 首先将地图中心点移到原点，然后应用一个小的偏移以便更精确对齐
      mapGroup.position.set(
        -vertexCenter.x,
        -vertexCenter.y,
        -vertexCenter.z + MAP_BASE_HEIGHT  // 添加基准高度，将地图向上移动
      );

      mapPosition.value = mapGroup.position.clone();
    } else {
      // 备用方案：使用包围盒计算中心
      const box = new THREE.Box3().setFromObject(mapGroup);
      const mapCenter = new THREE.Vector3();
      box.getCenter(mapCenter);

      mapGroup.position.set(-mapCenter.x, -mapCenter.y, -mapCenter.z + MAP_BASE_HEIGHT);
    }


    // 修改为平滑的加载完成动画
    const completeLoading = () => {
      // 确保进度至少达到80%
      if (loadingProgress.value < 80) {
        loadingProgress.value = 80;
      }

      // 平滑完成进度条
      const finalizeProgress = () => {
        const progressInterval = setInterval(() => {
          // 缓慢增加最后阶段
          if (loadingProgress.value < 100) {
            // 每次增加的量逐渐变小，实现缓动效果
            const increment = Math.max(1, (100 - loadingProgress.value) / 5);
            loadingProgress.value = Math.min(100, loadingProgress.value + increment);
          } else {
            clearInterval(progressInterval);

            // 进度条到100%后，等待一小段时间再隐藏加载界面
            setTimeout(() => {
              // 执行淡出效果 - 使用平滑淡出动画
              const loadingElement = document.querySelector('.loading-overlay');
              if (loadingElement) {
                // 先确保过渡属性已设置好
                loadingElement.style.transition = 'opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1)';
                // 延迟一帧再执行渐变，确保浏览器能正确处理动画
                requestAnimationFrame(() => {
                  // 下一帧再添加透明度
                  loadingElement.style.opacity = '0';
                });
              }

              // 等待淡出动画完成后再隐藏元素
              setTimeout(() => {
                isLoading.value = false;
                // 再次确认场景背景颜色设置正确
                if (scene) {
                  scene.background = new THREE.Color(COLORS.background);
                }
                // 确保渲染器清除颜色正确
                if (renderer) {
                  renderer.setClearColor(COLORS.background);
                }
                // 地图加载完成后创建城市光柱
                createCityBeams();
                renderNeedsUpdate = true; // 强制重新渲染一次
              }, 500);
            }, 300);
          }
        }, 40);
      };

      // 启动最终进度完成过程
      finalizeProgress();
    };

    // 启动加载进度完成过程
    completeLoading();

  } catch (error) {
    console.error('Failed to load map data:', error)
    isLoading.value = false; // 出错时也需要关闭加载状态
  }
}

// 修改相机设置
const initScene = () => {
  scene = new THREE.Scene()
  scene.background = new THREE.Color(COLORS.background)  // 使用浅灰色背景

  mapScene = new THREE.Scene()
  particleScene = new THREE.Scene()
  // 仍然创建starScene但不添加内容，保持代码结构一致
  starScene = new THREE.Scene()

  camera = new THREE.PerspectiveCamera(
    30,  // 减小视野角度，让画面更平
    window.innerWidth / window.innerHeight,
    1,
    5000  // 增加远剪切面距离，防止远处物体被裁剪
  )
  // 调整相机位置，适应缩小后的地图
  camera.position.set(0, -150, 150)  // 缩小相机距离，使其更靠近地图
  camera.up.set(0, 0, 1) // 调整上方向为Z轴，适应赤道视角
  camera.lookAt(0, 0, 0)

  renderer = new THREE.WebGLRenderer({
    alpha: false, // 关闭alpha通道以提高性能，因为我们有实色背景
    antialias: true,
    logarithmicDepthBuffer: true,  // 启用对数深度缓冲，提高深度精度
    powerPreference: "high-performance", // 提示系统使用高性能GPU
    preserveDrawingBuffer: false, // 关闭无用功能提高性能
    stencil: false // 不需要模板缓冲区
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  // 设置合适的像素比，限制最大值为2，避免高分辨率设备过度消耗GPU
  const pixelRatio = Math.min(window.devicePixelRatio, 2);
  renderer.setPixelRatio(pixelRatio)
  // 关闭阴影，因为我们不需要
  renderer.shadowMap.enabled = false;
  containerRef.value.appendChild(renderer.domElement)

  // 初始化CSS2D标签渲染器
  labelRenderer = new CSS2DRenderer();
  labelRenderer.setSize(window.innerWidth, window.innerHeight);
  labelRenderer.domElement.style.position = 'absolute';
  labelRenderer.domElement.style.top = '0px';
  labelRenderer.domElement.style.pointerEvents = 'none';
  containerRef.value.appendChild(labelRenderer.domElement);

  controls = new OrbitControls(camera, renderer.domElement)
  setupControls()

  // 初始调整相机以显示完整地图
  setTimeout(() => {
    controls.reset() // 重置控制器
    // 调整相机位置
    camera.position.set(0, -150, 150) // 调整为更近的位置和高度
    camera.lookAt(0, 0, 0)
    controls.update()
    renderNeedsUpdate = true; // 确保在位置调整后触发一次渲染
  }, 100)

  // 添加光源
  setupLights()

  // 添加星空
  initStarfield()

  // 添加粒子系统
  initParticleSystem()

  // 加载地图数据
  loadMapData().catch(err => {
    console.error("加载地图数据失败:", err)
  })

  // 添加事件监听 - 简化版
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('click', onMouseClick)

  // 添加重置视图的监听器
  window.addEventListener('keydown', (event) => {
    if (event.code === 'Space') {
      controls.reset()
      camera.position.set(0, -150, 150)
      camera.up.set(0, 0, 1)
      camera.lookAt(0, 0, 0)
      controls.update()
      renderNeedsUpdate = true
    }
  })
}

// 修改窗口调整函数
const onWindowResize = () => {
  if (camera && renderer) {
    camera.aspect = window.innerWidth / window.innerHeight
    camera.updateProjectionMatrix()
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(window.devicePixelRatio) // 更新像素比

    // 更新标签渲染器尺寸
    if (labelRenderer) {
      labelRenderer.setSize(window.innerWidth, window.innerHeight);
    }
  }
}

// 在开始加载前预热加载动画，避免进入时的卡顿
const preloadAnimation = () => {
  // 强制CPU/GPU提前处理动画，减少首次渲染时的卡顿
  // 创建一个临时的动画元素并执行动画，促使浏览器预热渲染管线
  const tempEl = document.createElement('div');
  tempEl.style.cssText = `
    position: absolute;
    width: 10px; 
    height: 10px; 
    opacity: 0.01; 
    z-index: -1;
    animation: spin 0.5s linear infinite;
  `;
  document.body.appendChild(tempEl);

  // 预先设置加载进度到20%，给用户立即反馈
  loadingProgress.value = 20;

  // 100ms后开始逐步增加到40%，模拟早期加载
  setTimeout(() => {
    loadingProgress.value = 40;
  }, 100);

  // 稍后清理临时元素
  setTimeout(() => {
    document.body.removeChild(tempEl);
  }, 1000);
};

// 生命周期钩子
onMounted(async () => {
  // 先加载热门站点数据
  await loadHotCities();
  // 立即预热动画
  preloadAnimation();
  
  // 初始化场景 - 这可能耗时较长
  requestAnimationFrame(() => {
    initScene();

    // 启动动画循环
    animate();

    // 添加窗口大小调整事件监听
    window.addEventListener('resize', onWindowResize);
  });

  // 页面加载完成后，强制更新渲染
  setTimeout(() => {
    renderNeedsUpdate = true;
  }, 2000);
})

onUnmounted(() => {
  try {
    // 清理所有定时器
    if (pauseTimer) {
      clearTimeout(pauseTimer);
    }

    // 清理射线检测相关对象
    raycastObjs = [];
    lineObjs = [];

    // 清理飞线
    try {
      clearRouteFlightLines();
    } catch (e) {
      console.warn('清理飞线失败:', e);
    }

    // 安全清理所有场景
    try {
      disposeScene(scene);
      disposeScene(mapScene);
      disposeScene(particleScene);
      disposeScene(starScene);
    } catch (error) {
      console.warn('场景清理时出错:', error);
    }

    // 清理城市光柱资源
    try {
      cityBeams.forEach(beam => {
        if (beam.parent) beam.parent.remove(beam);
        if (beam.geometry) beam.geometry.dispose();
        if (beam.material) beam.material.dispose();
        beam.children.forEach(child => {
          try {
            if (child.geometry) child.geometry.dispose();
            if (child.material) child.material.dispose();
          } catch (e) {
            console.warn('清理城市光柱子对象失败:', e);
          }
        });
      });
    } catch (error) {
      console.warn('城市光柱清理时出错:', error);
    }
    cityBeams = [];
    cityLabels = [];

    // 清理渲染器
    if (renderer) {
      try {
        renderer.dispose();
        renderer.forceContextLoss();
        renderer = null;
      } catch (error) {
        console.warn('渲染器清理时出错:', error);
      }
    }

    // 清理标签渲染器
    if (labelRenderer) {
      try {
        if (labelRenderer.domElement && labelRenderer.domElement.parentNode) {
          labelRenderer.domElement.parentNode.removeChild(labelRenderer.domElement);
        }
        labelRenderer = null;
      } catch (error) {
        console.warn('标签渲染器清理时出错:', error);
      }
    }
    // 清理控制器
    if (controls) {
      try {
        controls.dispose();
        controls = null;
      } catch (error) {
        console.warn('控制器清理时出错:', error);
      }
    }

    // 清理事件监听
    try {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('click', onMouseClick);
      window.removeEventListener('keydown', event => { });
      window.removeEventListener('resize', onWindowResize);
    } catch (error) {
      console.warn('事件监听器清理时出错:', error);
    }

    // 清理引用
    camera = null;
    scene = null;
    mapScene = null;
    particleScene = null;
    starScene = null;
    mapGroup = null;
    particleSystem = null;

    // 清理可能存在的内存缓存
    if (window._savedMaterials) {
      window._savedMaterials = null;
    }
    if (window._throttleTimeout) {
      clearTimeout(window._throttleTimeout);
      window._throttleTimeout = null;
    }
    if (window.particleSystemPosition) {
      window.particleSystemPosition = null;
    }
    if (window.updateCityPulses) {
      window.updateCityPulses = null;
    }

    console.log('Earth3D组件资源已安全清理');
  } catch (e) {
    console.error('Earth3D组件清理过程中发生异常:', e);
  }
});

// 创建城市光柱的函数，使用实例化渲染
const createCityBeams = () => {
  if (!mapGroup) return;

  // 清除之前的光柱和标签
  cityBeams.forEach(beam => {
    mapScene.remove(beam);
    if (beam.geometry) beam.geometry.dispose();
    if (beam.material) beam.material.dispose();
  });

  cityLabels.forEach(label => {
    if (label.parent) label.parent.remove(label);
  });

  cityBeams = [];
  cityLabels = [];

  const MAP_BASE_HEIGHT = 1.0; // 地图基准高度

  // 为脉冲效果添加新的数组
  let cityPulses = [];

  // 脉冲圆环参数
  const PULSE_COLOR = 0x41F8FF; // 使用与光柱相同的蓝色
  const PULSE_RINGS = 3; // 3层脉冲圈
  const PULSE_MAX_RADIUS = 1.0; // 最大脉冲半径
  const PULSE_MIN_RADIUS = 0.2; // 最小脉冲半径
  const PULSE_ANIMATION_SPEED = 0.5; // 脉冲动画速度

  // 为每个城市创建光柱和标签
  HOT_CITIES.forEach(city => {
    try {
      // 获取经纬度坐标
      const lng = city.longitude;
      const lat = city.latitude;

      // 确保经纬度在正确的范围内
      let validLng = lng;
      let validLat = lat;

      // 检查并修正经纬度范围
      if (!isInLatRange(validLat) || !isInLngRange(validLng)) {
        console.warn(`城市 ${city.name} 经纬度超出范围，尝试修正:`, { lat, lng });

        // 如果经纬度值看起来交换了，则交换回来
        if (Math.abs(validLat) > 90 && Math.abs(validLng) <= 90) {
          const temp = validLat;
          validLat = validLng;
          validLng = temp;
        }

        console.log(`城市 ${city.name} 经纬度修正后:`, { lat: validLat, lng: validLng });
      }

      // 完全匹配飞线处理方式
      console.log(`城市 ${city.name} 投影前坐标:`, [validLng, validLat]);

      // 将地理坐标转换为地图坐标 - 确保经纬度顺序正确
      // 注意：projection函数期望的顺序是[lng, lat]
      const [x, y] = projection([validLng, validLat]);
      console.log(`城市 ${city.name} 投影后坐标:`, { x, y });

      // 应用缩放比例 - 完全匹配飞线处理
      const scaleFactor = 1 / 6;
      const scaledX = x * scaleFactor;
      const scaledY = -y * scaleFactor; // Y坐标需要取反

      // 设置光柱高度 - 和国界线完全一样：country.LAYER_HEIGHT * 3 + BEVEL + 0.001
      const THIRD_LAYER_HEIGHT = 1.4 * 3;  // country.LAYER_HEIGHT * 3
      const BEVEL = 0.05; // 与国界线使用相同的BEVEL值
      const TOP_LAYER_Z = THIRD_LAYER_HEIGHT + BEVEL + 0.001; // 国界线的确切Z坐标

      // 添加调试日志
      console.log(`城市 ${city.name} 最终坐标:`, {
        经纬度: { lng: validLng, lat: validLat },
        投影后: { x, y },
        缩放后: { scaledX, scaledY },
        高度计算: {
          LAYER_HEIGHT: LAYER_HEIGHT,
          TOP_LAYER_Z: TOP_LAYER_Z,
          最终高度: TOP_LAYER_Z
        },
        地图偏移: mapGroup.position ?
          { x: mapGroup.position.x, y: mapGroup.position.y, z: mapGroup.position.z } :
          { x: 0, y: 0, z: 0 },
        最终位置: {
          x: scaledX + (mapGroup.position ? mapGroup.position.x : 0),
          y: scaledY + (mapGroup.position ? mapGroup.position.y : 0),
          z: TOP_LAYER_Z + (mapGroup.position ? mapGroup.position.z : 0)
        }
      });

      // 创建光柱组
      const beamGroup = new THREE.Group();

      // 创建一个简单的圆柱体光柱
      const beamGeometry = new THREE.CylinderGeometry(
        BEAM_RADIUS,      // 顶部半径
        BEAM_RADIUS,      // 底部半径 - 确保上下等宽
        BEAM_HEIGHT,      // 高度
        12,               // 分段数
        1,                // 高度分段
        false             // 闭合顶部和底部
      );

      // 创建发光材质
      const beamMaterial = new THREE.MeshBasicMaterial({
        color: BEAM_COLOR,
        transparent: true,
        opacity: 0.7,
        side: THREE.DoubleSide
      });

      // 创建光柱网格
      const beam = new THREE.Mesh(beamGeometry, beamMaterial);

      // 旋转光柱使其垂直于地面
      beam.rotation.x = Math.PI / 2;

      // 添加城市名称标签
      const labelText = isEnglish.value ? city.name : city.nameZh;
      const labelDiv = document.createElement('div');
      labelDiv.className = 'city-label';
      labelDiv.textContent = labelText;

      const label = new CSS2DObject(labelDiv);
      // 将标签位置调整到光柱的顶部
      label.position.set(0, BEAM_HEIGHT / 2 + LABEL_OFFSET_Y, 0);
      beam.add(label);

      // 添加脉冲圆环效果
      for (let i = 0; i < PULSE_RINGS; i++) {
        // 创建脉冲圆环几何体
        const pulseGeometry = new THREE.RingGeometry(
          PULSE_MIN_RADIUS, // 内半径
          PULSE_MIN_RADIUS + 0.1, // 外半径，初始状态很窄
          32, // 分段数
          1  // 径向分段
        );

        // 创建脉冲材质 - 使用蓝色发光效果
        const pulseMaterial = new THREE.MeshBasicMaterial({
          color: PULSE_COLOR,
          transparent: true,
          opacity: 0.7,
          side: THREE.DoubleSide,
          blending: THREE.AdditiveBlending // 使用加法混合增强发光效果
        });

        // 创建脉冲网格
        const pulse = new THREE.Mesh(pulseGeometry, pulseMaterial);

        // 将脉冲定位在光柱底部
        pulse.rotation.x = -Math.PI / 2; // 旋转使其平行于地面
        pulse.position.y = -BEAM_HEIGHT / 2; // 定位在光柱底部

        // 为每个脉冲设置不同的初始状态，错开动画
        pulse.userData.phase = i * (1 / PULSE_RINGS);
        pulse.userData.speed = PULSE_ANIMATION_SPEED;

        // 添加到光柱
        beam.add(pulse);

        // 记录脉冲以便动画更新
        cityPulses.push(pulse);
      }

      // 将光柱添加到组
      beamGroup.add(beam);

      // 设置光柱组位置 - 使用与国界线完全相同的Z坐标
      beamGroup.position.set(scaledX, scaledY, TOP_LAYER_Z);

      // 应用地图的整体位移，确保与地图对齐
      if (mapGroup && mapGroup.position) {
        beamGroup.position.x += mapGroup.position.x;
        beamGroup.position.y += mapGroup.position.y;
        beamGroup.position.z += mapGroup.position.z;
      }

      // 添加renderOrder确保可见性，设置比飞线更高以确保在地图顶层显示
      beamGroup.renderOrder = 12000; // 设置为更高的优先级，高于飞线(10000)
      beam.renderOrder = 12000; // 单独设置光柱的renderOrder
      beamGroup.traverse(obj => {
        if (obj.material) {
          obj.material.depthTest = false; // 禁用深度测试，确保始终可见
          obj.material.depthWrite = false; // 不写入深度缓冲区
          obj.material.needsUpdate = true;
          obj.renderOrder = 12000; // 为组内所有元素设置高优先级
        }
      });

      // 存储引用
      cityBeams.push(beamGroup);
      cityLabels.push(label);

      // 添加到场景
      mapScene.add(beamGroup);
    } catch (error) {
      console.error(`创建城市光柱时出错: ${city.name}`, error);
    }
  });

  // 添加光柱脉冲更新函数
  window.updateCityPulses = (deltaTime) => {
    cityPulses.forEach(pulse => {
      if (!pulse.userData) return;

      // 更新脉冲阶段
      pulse.userData.phase += deltaTime * pulse.userData.speed;
      if (pulse.userData.phase > 1) {
        pulse.userData.phase = 0;
      }

      // 计算当前脉冲半径和不透明度
      const progress = pulse.userData.phase;
      const currentRadius = PULSE_MIN_RADIUS + (PULSE_MAX_RADIUS - PULSE_MIN_RADIUS) * progress;
      const opacity = Math.max(0, 1 - progress); // 随着扩大逐渐变透明

      // 更新几何体
      if (pulse.geometry) {
        pulse.geometry.dispose();
        pulse.geometry = new THREE.RingGeometry(
          currentRadius * 0.9, // 内半径
          currentRadius,       // 外半径
          32,                  // 分段数
          1                    // 径向分段
        );
      }

      // 更新不透明度
      if (pulse.material) {
        pulse.material.opacity = opacity * 0.8; // 调整最大不透明度
        pulse.material.needsUpdate = true;
      }
    });

    // 确保渲染更新
    renderNeedsUpdate = true;
  };

  // 标记需要渲染更新  
  renderNeedsUpdate = true;
};

// 添加一个全局函数用于重建城市光柱，便于调试
window.rebuildCityBeams = () => {
  console.log('重建城市光柱...');
  createCityBeams();
  console.log('城市光柱重建完成');
  renderNeedsUpdate = true;
};

// 添加一个全局函数用于比较城市位置和飞线位置
window.comparePositions = () => {
  console.log('比较城市光柱和飞线位置:');
  // 打印城市光柱位置
  console.log('城市光柱位置:');
  cityBeams.forEach((beam, index) => {
    const city = HOT_CITIES[index];
    if (city && beam && beam.position) {
      console.log(`${city.name}: x=${beam.position.x.toFixed(4)}, y=${beam.position.y.toFixed(4)}, z=${beam.position.z.toFixed(4)}`);
    }
  });

  // 打印飞线位置
  console.log('飞线位置:');
  routeFlightLines.forEach(line => {
    if (line && line.userData && line.userData.startPoint) {
      const startPoint = line.userData.startPoint;
      const endPoint = line.userData.endPoint;
      console.log(`飞线 ${line.name}:`, {
        起点名称: startPoint.name,
        终点名称: endPoint.name,
        起点位置: line.geometry.parameters
          ? `x=${line.geometry.parameters.path.points[0].x.toFixed(4)}, y=${line.geometry.parameters.path.points[0].y.toFixed(4)}, z=${line.geometry.parameters.path.points[0].z.toFixed(4)}`
          : '无法获取'
      });
    }
  });

  // 如果城市名称和飞线起终点名称匹配，进行直接比较
  console.log('匹配的城市和飞线位置比较:');
  cityBeams.forEach((beam, index) => {
    const city = HOT_CITIES[index];
    if (!city || !beam || !beam.position) return;

    routeFlightLines.forEach(line => {
      if (!line || !line.userData || !line.userData.startPoint) return;

      const startPoint = line.userData.startPoint;
      const endPoint = line.userData.endPoint;

      // 检查城市名称是否与飞线起点或终点匹配
      if ((startPoint.name && startPoint.name.includes(city.name)) ||
        (startPoint.name && startPoint.name.includes(city.nameZh)) ||
        (city.name && city.name.includes(startPoint.name)) ||
        (city.nameZh && city.nameZh.includes(startPoint.name))) {

        console.log(`匹配 - 城市:${city.name} 和飞线起点:${startPoint.name}`);
        console.log('城市光柱位置:', `x=${beam.position.x.toFixed(4)}, y=${beam.position.y.toFixed(4)}, z=${beam.position.z.toFixed(4)}`);
        console.log('飞线起点位置:', line.geometry.parameters
          ? `x=${line.geometry.parameters.path.points[0].x.toFixed(4)}, y=${line.geometry.parameters.path.points[0].y.toFixed(4)}, z=${line.geometry.parameters.path.points[0].z.toFixed(4)}`
          : '无法获取');
      }

      // 同样检查终点
      if ((endPoint.name && endPoint.name.includes(city.name)) ||
        (endPoint.name && endPoint.name.includes(city.nameZh)) ||
        (city.name && city.name.includes(endPoint.name)) ||
        (city.nameZh && city.nameZh.includes(endPoint.name))) {

        console.log(`匹配 - 城市:${city.name} 和飞线终点:${endPoint.name}`);
        console.log('城市光柱位置:', `x=${beam.position.x.toFixed(4)}, y=${beam.position.y.toFixed(4)}, z=${beam.position.z.toFixed(4)}`);
        console.log('飞线终点位置:', line.geometry.parameters
          ? `x=${line.geometry.parameters.path.points[line.geometry.parameters.path.points.length - 1].x.toFixed(4)}, y=${line.geometry.parameters.path.points[line.geometry.parameters.path.points.length - 1].y.toFixed(4)}, z=${line.geometry.parameters.path.points[line.geometry.parameters.path.points.length - 1].z.toFixed(4)}`
          : '无法获取');
      }
    });
  });
};

// 添加城市光柱高度调整函数，方便在控制台调整
window.adjustBeamHeight = (heightOffset = 0) => {
  console.log(`调整城市光柱高度，偏移量: ${heightOffset}`);

  // 直接使用与国界线相同的高度计算：country.LAYER_HEIGHT * 3 + BEVEL + 0.001
  const THIRD_LAYER_HEIGHT = 0.8 * 3;  // country.LAYER_HEIGHT * 3
  const BEVEL = 0.05;
  const TOP_LAYER_Z = THIRD_LAYER_HEIGHT + BEVEL + 0.001; // 国界线的确切Z坐标
  const newHeight = TOP_LAYER_Z + heightOffset;

  console.log(`国界线高度: ${TOP_LAYER_Z.toFixed(4)}, 调整后高度: ${newHeight.toFixed(4)}`);

  // 调整所有城市光柱的高度
  cityBeams.forEach((beam, index) => {
    const city = HOT_CITIES[index];
    if (city && beam && beam.position) {
      // 保存原始X和Y坐标
      const originalX = beam.position.x;
      const originalY = beam.position.y;

      // 计算新的Z坐标
      let newZ = newHeight;

      // 如果有mapGroup，加上其Z偏移
      if (mapGroup && mapGroup.position) {
        newZ += mapGroup.position.z;
      }

      // 设置新位置
      beam.position.set(originalX, originalY, newZ);

      console.log(`${city.name} 高度已调整到 z=${beam.position.z.toFixed(4)}`);
    }
  });

  // 强制更新渲染
  renderNeedsUpdate = true;

  return `所有城市光柱高度已调整，新高度: ${newHeight.toFixed(4)}`;
};

// 添加重置到与国界线完全相同高度的快捷函数
window.resetBeamHeight = () => {
  return window.adjustBeamHeight(0);
};

// 添加性能优化参数
const PERFORMANCE = {
  USE_LOW_POLY: true,        // 使用低多边形模型
  FRUSTUM_CULLING: true,     // 启用视锥体剔除
  USE_INSTANCED_MESH: true,  // 使用实例化渲染
  MAX_VISIBLE_COUNTRIES: 100, // 最大可见国家数
  LOD_LEVELS: {
    NEAR: 100,               // 近距离阈值
    MEDIUM: 200,             // 中等距离阈值
    FAR: 300                 // 远距离阈值
  },
  MAX_PARTICLES: Math.min(5000, PARTICLE_COUNT), // 限制粒子数量
  RENDER_LIMIT_ENABLED: true, // 启用渲染限制
  FRAME_THROTTLE: 2          // 帧率限制
};

// 修改disposeScene函数，安全处理材质属性
const disposeScene = (scene) => {
  if (!scene) return;

  try {
    scene.traverse(object => {
      try {
        if (object.geometry) {
          object.geometry.dispose();
        }

        if (object.material) {
          // 如果是材质数组，逐个处理
          if (Array.isArray(object.material)) {
            object.material.forEach(material => {
              if (!material) return;

              // 安全地处理材质属性
              Object.keys(material).forEach(prop => {
                try {
                  // 只对有dispose方法的属性调用dispose
                  if (material[prop] && typeof material[prop].dispose === 'function') {
                    material[prop].dispose();
                  }
                } catch (propError) {
                  console.warn(`处理材质属性 ${prop} 时出错:`, propError);
                }
              });

              // 安全调用材质的dispose方法
              if (typeof material.dispose === 'function') {
                material.dispose();
              }
            });
          } else {
            // 处理单个材质
            if (object.material) {
              Object.keys(object.material).forEach(prop => {
                try {
                  // 只对有dispose方法的属性调用dispose
                  if (object.material[prop] && typeof object.material[prop].dispose === 'function') {
                    object.material[prop].dispose();
                  }
                } catch (propError) {
                  console.warn(`处理材质属性 ${prop} 时出错:`, propError);
                }
              });

              // 安全调用材质的dispose方法
              if (typeof object.material.dispose === 'function') {
                object.material.dispose();
              }
            }
          }
        }
      } catch (objectError) {
        console.warn('处理场景对象时出错:', objectError);
      }
    });
  } catch (sceneError) {
    console.error('遍历场景时出错:', sceneError);
  }
};

// 创建起点/终点标签
const createEndpointLabel = (point, isStart=true) => {
  if(!point||isNaN(point.lat)||isNaN(point.lng)) return null;
  try{
    const [x,y]=projection([point.lng,point.lat]);
    const scaleFactor=1/6;
    const scaledX=x*scaleFactor;
    const scaledY=-y*scaleFactor;
    const labelHeight=BASE_Z+LAYER_HEIGHT*1+2.5;
    const labelDiv=document.createElement('div');
    labelDiv.className = isStart ? 'start-point-label' : 'end-point-label';
    // 根据当前语言选择显示的名称
    labelDiv.textContent = ` ${isEnglish.value ? point.nameEn || point.name : point.name}`;
    const label=new CSS2DObject(labelDiv);
    let labelX=scaledX,labelY=scaledY,labelZ=labelHeight;
    if(mapGroup&&mapGroup.position){labelX+=mapGroup.position.x;labelY+=mapGroup.position.y;labelZ+=mapGroup.position.z;}
    label.position.set(labelX,labelY,labelZ);
    mapScene.add(label);
    endpointLabels.push(label);
    console.log(`创建${isStart ? '起点' : '终点'}标签: ${point.name} at (${labelX.toFixed(2)}, ${labelY.toFixed(2)}, ${labelZ.toFixed(2)})`);
    return label;
  }catch(e){console.error('创建端点标签失败',e);return null;}
};

const hideCityBeams=()=>{cityBeams.forEach(b=>b.visible=false);};
const showCityBeams=()=>{cityBeams.forEach(b=>b.visible=true);};

// 暴露给父组件的重置函数
function reset3D() {
  clearRouteFlightLines();
  showCityBeams();
}

// 添加路线选择函数
const selectRoute = async (index, routeId) => {
  console.log('3D组件收到路线选择:', index, '路线ID:', routeId);
  
  // 如果有lastProcessedRoutes数据，使用对应索引的路线
  if (lastProcessedRoutes && lastProcessedRoutes[index]) {
    const selectedRoute = lastProcessedRoutes[index];
    
    // 清理当前显示的路线
    clearRouteFlightLines();
    
    // 创建新的飞线
    createRouteFlightLines([selectedRoute]);
    
    // 如果需要，可以使用routeId进行额外的处理
    if (routeId) {
      console.log('处理特定路线ID:', routeId);
      // 这里可以添加与routeId相关的特殊处理逻辑
    }
  }
};

// 向外暴露方法，便于父组件调用
defineExpose({ searchIn3D, reset3D, selectRoute });

// 监听语言变化
watch(currentLocale, () => {
  // 如果有已处理的路线数据，重新创建飞线和标签
  if (lastProcessedRoutes.length > 0) {
    // 清理当前显示的路线和标签
    clearRouteFlightLines();
    // 重新创建飞线和标签
    createRouteFlightLines(lastProcessedRoutes);
  }
  
  // 更新城市光柱标签
  updateCityLabels();
});

// 添加更新城市标签的函数
const updateCityLabels = () => {
  cityBeams.forEach((beam, index) => {
    const city = HOT_CITIES[index];
    if (!city || !beam) return;
    
    // 更新标签文本
    beam.children.forEach(child => {
      if (child instanceof CSS2DObject) {
        const labelDiv = child.element;
        if (labelDiv && labelDiv.className === 'city-label') {
          labelDiv.textContent = isEnglish.value ? city.name : city.nameZh;
        }
      }
    });
  });
};

// 在文件开头添加 flightGroup 声明
let flightGroup = null;

</script>

<style scoped>
/* 添加全局背景颜色样式，确保在任何情况下背景都是正确的颜色 */
:deep(body),
:deep(html) {
  background-color: #E2F0FD !important;
  margin: 0;
  padding: 0;
  height: 100%;
}

.earth3d-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-color: #E2F0FD;
  overflow: hidden;
  /* 防止滚动条出现 */
}

.render-container {
  width: 100%;
  height: 100%;
}



.country-name {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333333;
  background: rgba(241, 244, 243, 0.85);
  padding: 10px 20px;
  border-radius: 8px;
  min-width: 180px;
  text-align: center;
  border: 1px solid #41F8FF;
  box-shadow: 0 0 15px rgba(65, 248, 255, 0.5);
  transition: all 0.3s ease;
}

/* 城市标签样式 */
.city-label {
  color: #FFFFFF;
  padding: 3px 8px;
  font-size: 0.8rem;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #41F8FF;
  border-radius: 4px;
  pointer-events: none;
  white-space: nowrap;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(65, 248, 255, 0.5);
}

/* 出境口岸标签样式 */
.exit-port-label {
  color: #FFFFFF !important;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%) !important;
  padding: 8px 12px !important;
  border-radius: 20px !important;
  font-size: 13px !important;
  font-weight: bold !important;
  border: 2px solid #FFD700 !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
  white-space: nowrap !important;
  pointer-events: none !important;
  transform: translate(-50%, -100%) !important;
  z-index: 1000 !important;
  animation: pulse-glow 2s ease-in-out infinite !important;
}

/* 起点标签样式 */
.start-point-label {
  color: #FFFFFF !important;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  padding: 8px 12px !important;
  border-radius: 20px !important;
  font-size: 13px !important;
  font-weight: bold !important;
  border: 2px solid #81C784 !important;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
  white-space: nowrap !important;
  pointer-events: none !important;
  transform: translate(-50%, -100%) !important;
  z-index: 1000 !important;
  animation: pulse-glow 2s ease-in-out infinite !important;
}

/* 终点标签样式 */
.end-point-label {
  color: #FFFFFF !important;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
  padding: 8px 12px !important;
  border-radius: 20px !important;
  font-size: 13px !important;
  font-weight: bold !important;
  border: 2px solid #64B5F6 !important;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
  white-space: nowrap !important;
  pointer-events: none !important;
  transform: translate(-50%, -100%) !important;
  z-index: 1000 !important;
  animation: pulse-glow 2s ease-in-out infinite !important;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    transform: translate(-50%, -100%) scale(1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
    transform: translate(-50%, -100%) scale(1.05);
  }
}

/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 改进加载状态指示器样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #E2F0FD;
  /* 更新为与主背景匹配的颜色 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* 确保在最上层 */
  opacity: 1;
  transition: opacity 0.5s ease;
  will-change: opacity;
  /* 优化性能 */
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  border: 4px solid #3C8AF0;
  /* 更改为蓝色系，使其在浅色背景下更明显 */
  border-top: 4px solid transparent;
  border-radius: 50%;
  width: 60px;
  /* 增大尺寸 */
  height: 60px;
  /* 增大尺寸 */
  animation: spin 1.2s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  /* 改进动画效果 */
  animation-delay: -0.2s;
  /* 添加负延迟，使动画立即开始并已进行一段时间 */
  will-change: transform;
  /* 优化动画性能 */
  box-shadow: 0 0 15px rgba(60, 138, 240, 0.4);
  /* 更新阴影颜色 */
  backface-visibility: hidden;
  /* 优化渲染性能 */
  transform: translateZ(0);
  /* 启用GPU加速 */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
    border-width: 5px;
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20px;
  font-size: 1.25rem;
  font-weight: bold;
  color: #333333;
  /* 更新文字颜色与浅色背景匹配 */
  text-shadow: none;
  /* 移除文字阴影 */
  letter-spacing: 1px;
}

.loading-progress {
  margin-top: 30px;
  width: 240px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3C8AF0 0%, #5AA9F5 100%);
  /* 更新为蓝色渐变 */
  border-radius: 3px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(60, 138, 240, 0.3);
  /* 更新阴影颜色 */
}

/* 添加迷你搜索样式 */
.mini-search {
  position: fixed !important;
  top: auto !important;
  bottom: 20px !important;
  left: 20px !important;
  z-index: 900;
  display: flex;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  overflow: hidden;
  background: rgba(16, 30, 62, 0.8);
  pointer-events: auto;
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
}

.mini-search input {
  padding: 6px 10px;
  width: 150px;
  border: none;
  border-right: 1px solid rgba(65, 248, 255, 0.3);
  outline: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.mini-search input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.mini-search button {
  background: rgba(49, 198, 255, 0.8);
  color: white;
  border: none;
  padding: 6px 10px;
  cursor: pointer;
  min-width: 60px;
}

.mini-search button:hover {
  background: rgba(60, 233, 246, 1);
}

/* 顶部搜索框样式 */
.top-search {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 900;
  display: flex;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.35);
  border-radius: 8px;
  overflow: hidden;
  background: rgba(16, 30, 62, 0.85);
  backdrop-filter: blur(8px);
  pointer-events: auto;
  margin: 0 !important;
  padding: 0 !important;
  border: 1px solid rgba(65, 248, 255, 0.5);
}

.top-search input {
  padding: 10px 16px;
  width: 220px;
  border: none;
  outline: none;
  font-size: 15px;
  background: rgba(255, 255, 255, 0.12);
  color: white;
  border-right: 1px solid rgba(65, 248, 255, 0.3);
  transition: all 0.3s ease;
}

.top-search input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.top-search input:focus {
  background: rgba(255, 255, 255, 0.18);
  width: 240px;
}

.top-search button {
  background: rgba(49, 198, 255, 0.7);
  color: white;
  border: none;
  padding: 10px 18px;
  cursor: pointer;
  min-width: 80px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.top-search button:hover {
  background: rgba(60, 233, 246, 0.9);
}

.top-search button:active {
  transform: scale(0.97);
}
</style>



