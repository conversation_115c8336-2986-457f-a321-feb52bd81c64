<template>
  <div class="floating-menu">
    <div class="menu-container">
      <!-- 分享按钮 -->
      <div class="menu-button">
        <Aiavater style="width: 100%"></Aiavater>
      </div>

      <div class="menu-items">
        <!-- 小程序 -->
        <div
          class="menu-item"
          ref="miniprogramItem"
        >
          <div 
            class="icon-circle"
            @mouseenter="handleMenuItemEnter('miniprogram')"
            @mouseleave="handleMenuItemLeave('miniprogram')"
          >
<svg t="1752559953982" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1540" width="200" height="20"><path d="M621.105 489.791c40.778-32.161 66.87-81.92 66.87-137.747 0-96.726-78.643-175.37-175.37-175.37s-175.37 78.643-175.37 175.37c0 55.827 26.214 105.586 66.87 137.747-36.895 14.563-70.755 36.773-99.638 65.658-55.584 55.584-86.167 129.494-86.167 208.138 0 16.262 0 33.618 0.122 49.637s13.107 29.006 29.127 29.006h530.235c16.02 0 29.127-12.986 29.127-29.006v-49.637c0-78.643-30.583-152.554-86.167-208.138-28.884-28.884-62.745-51.094-99.638-65.658zM395.491 352.044c0-64.565 52.55-117.115 117.115-117.115s117.115 52.55 117.115 117.115-52.55 117.115-117.115 117.115c-64.565 0.122-117.115-52.55-117.115-117.115z m353.167 412.148v19.782H276.557v-20.389c0-130.222 105.95-236.051 236.051-236.051 130.222 0 236.051 105.828 236.051 236.051v0.607z" fill="#333333" p-id="1541"></path><path d="M871.477 545.982c-21.482-28.763-49.274-52.55-80.828-69.42 26.457-26.7 41.748-62.988 41.748-101.945 0-32.161-10.316-62.501-29.733-87.988-18.811-24.515-45.511-42.841-75.124-51.337-12.865-3.641-26.336 3.762-29.977 16.626s3.762 26.336 16.626 29.977c41.021 11.773 69.783 49.881 69.783 92.721 0 36.53-20.268 69.42-52.793 86.047-8.738 4.491-13.957 13.836-13.229 23.666 0.85 9.83 7.524 18.205 16.869 21.117 81.556 25.607 136.291 100.246 136.291 185.685v40.778c0 13.349 10.801 24.272 24.151 24.394h0.122c13.349 0 24.272-10.801 24.272-24.151v-40.899c-0.122-52.793-16.748-103.037-48.181-145.271z m-586.062-40.414c9.467-2.913 16.142-11.286 16.869-21.117 0.85-9.83-4.369-19.175-13.229-23.666-32.526-16.626-52.793-49.516-52.793-86.047 0-42.841 28.641-80.949 69.783-92.721 12.865-3.762 20.268-17.113 16.626-29.977-3.762-12.865-17.113-20.268-29.977-16.626-29.613 8.496-56.313 26.821-75.124 51.337-19.419 25.486-29.733 55.827-29.733 87.988 0 38.958 15.292 75.245 41.748 101.945-31.434 16.869-59.346 40.656-80.828 69.42-31.554 42.235-48.181 92.479-48.181 145.271v40.899c0 13.349 10.923 24.151 24.272 24.151h0.122c13.349 0 24.272-10.923 24.151-24.394v-40.778c0-85.561 54.855-160.199 136.291-185.685z" fill="#333333" p-id="1542"></path></svg>
          </div>
          <span class="item-text">{{ t("right.miniProgram") }}</span>
          <!-- 小程序二维码 -->
          <div 
            class="qr-popup" 
            v-show="activeQR === 'miniprogram'"
            @mouseenter="handleQREnter('miniprogram')" 
            @mouseleave="handleQRLeave('miniprogram')"
            ref="miniprogramQR"
          >
            <img :src="WechatMiniprogram" alt="公众号" />
            <p>{{ t("right.gonzhao") }}</p>
          </div>
        </div>

        <!-- 微信社群 -->
        <div
          class="menu-item"
          ref="wechatItem"
        >
          <div 
            class="icon-circle"
            @mouseenter="handleMenuItemEnter('wechat')"
            @mouseleave="handleMenuItemLeave('wechat')"
          >
      <svg t="1752560044856" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3433" width="200" height="20"><path d="M938.666667 603.114667c0-58.602667-26.794667-113.493333-75.605334-154.453334-43.52-36.608-99.925333-57.984-160.32-61.312-13.269333-60.416-50.88-115.328-106.24-154.88C540.693333 192.576 469.76 170.666667 396.714667 170.666667c-82.282667 0-159.829333 27.136-218.410667 76.288C118.4 297.365333 85.333333 364.693333 85.333333 436.608c0 37.12 8.746667 73.088 26.133334 106.88a265.130667 265.130667 0 0 0 62.336 78.954667l-21.376 118.144a29.098667 29.098667 0 0 0 28.714666 34.154666c3.349333 0 6.698667-0.512 9.92-1.792l195.370667-70.506666a591.957333 591.957333 0 0 0 67.733333-5.12 182.592 182.592 0 0 0 42.88 57.856c44.970667 40.682667 111.296 62.186667 197.333334 64 0.490667 0 0.874667 0 1.28 0.106666l142.933333 33.28a29.034667 29.034667 0 0 0 25.749333-6.4c7.104-6.144 10.709333-15.232 9.941334-24.448l-6.442667-68.48C913.045333 713.344 938.666667 659.2 938.666667 603.136zM381.013333 646.4l-4.757333 0.128-159.424 57.6 14.677333-80.768a39.744 39.744 0 0 0-13.909333-37.76c-48.554667-39.68-75.349333-92.522667-75.349333-148.693333 0-115.584 114.24-209.770667 254.741333-209.770667 119.893333 0 222.037333 67.84 248.32 162.922667-50.773333 7.168-97.642667 27.392-135.125333 58.88-48.661333 40.96-75.584 95.850667-75.584 154.453333 0 13.696 1.024 27.008 3.093333 39.68-18.026667 1.792-36.970667 3.072-56.661333 3.328z m441.237334 71.68a31.146667 31.146667 0 0 0-11.861334 27.648L814.528 789.333333l-105.6-24.426666c-4.266667-1.28-8.64-1.792-13.013333-1.792-133.824-2.688-204.650667-58.112-204.650667-160 0-88.170667 87.701333-159.978667 195.498667-159.978667s195.242667 71.808 195.242666 160c0 43.626667-21.248 84.458667-59.754666 114.922667z m-569.386667-326.101333c0 21.205333 17.322667 38.4 38.656 38.4s38.634667-17.194667 38.634667-38.4-17.28-38.4-38.634667-38.4c-21.333333 0-38.634667 17.194667-38.634667 38.4z m204.8 3.84c0 21.205333 17.28 38.4 38.613333 38.4s38.656-17.194667 38.656-38.4-17.301333-38.4-38.634666-38.4c-21.333333 0-38.656 17.194667-38.656 38.4z m110.741333 180.458666c0 17.664 14.421333 32 32.213334 32 17.770667 0 32.192-14.336 32.192-32 0-17.685333-14.421333-32-32.213334-32-17.770667 0-32.192 14.314667-32.192 32z m144.234667 0c0 17.664 14.421333 32 32.213333 32 17.770667 0 32.192-14.336 32.192-32 0-17.685333-14.421333-32-32.213333-32-17.770667 0-32.192 14.314667-32.192 32z" p-id="3434"></path></svg>
          </div>
          <span class="item-text">{{ t("right.wechat") }}</span>
          <div 
            class="qr-popup" 
            v-show="activeQR === 'wechat'"
            @mouseenter="handleQREnter('wechat')" 
            @mouseleave="handleQRLeave('wechat')"
            ref="wechatQR"
          >
            <img :src="WechatGroup" alt="微信社群二维码" />
            <p>{{ t("right.weixinshequn") }}</p>
          </div>
        </div>

        <!-- 对话咨询 - 更新为使用 openCustomerServiceChat 方法 -->
          <div class="menu-item">
  <div 
    class="icon-circle"
    @click="openCustomerServiceChat"
  >
    <svg t="1752048881313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1509" data-spm-anchor-id="a313x.search_index.0.i0.26dc3a81VWVfv0" width="32" height="32"><path d="M515.530406 770.231319c4.366448 0 8.812713-0.267083 13.284561-0.800225 42.889833-5.14723 79.310324-35.124985 92.75452-76.437903 1.01205-3.093455-0.452301-6.40078-3.40761-7.760754-2.928702-1.359974-6.469342-0.186242-8.119935 2.586918-0.159636 0.267083-17.198707 27.79095-83.542718 36.752042-9.930163 1.333368-19.594267 2.02717-28.752858 2.02717-46.483684-0.026606-67.196425-17.548678-67.382667-17.709337-2.342348-2.080382-5.910617-2.133593-8.306176-0.106424-2.422166 2.02717-2.981914 5.547343-1.331321 8.241708C440.960037 749.82864 477.273081 770.231319 515.530406 770.231319zM872.252901 359.078145c-0.586354 0-1.118474 0.106424-1.677199 0.106424C831.705416 190.893678 683.175591 65.355497 505.09473 65.355497c-182.979428 0-334.916862 132.473127-368.595916 307.724547-32.745798 5.894244-57.665351 34.671661-57.665351 69.477374l0 139.300626c0 39.045271 31.309076 70.703295 69.965491 70.703295 21.804608 0 41.052998-10.29446 53.858652-26.110681 30.934546 81.82254 93.922113 147.803277 173.627433 182.610014 0.191358-0.415462 1.700735-3.348258 3.514033-5.685489 1.257643-1.620917 2.660596-2.952238 3.868097-2.952238 1.251503 0 2.396583 0.453325 3.381004 1.147126-18.450211-13.735839-85.086887-84.33271-99.516528-182.853561-6.336312-43.366693 26.143427-85.932138 63.975057-92.973508 60.727083-11.308557 121.134895-24.189935 181.861978-35.284621 38.603203-7.04137 64.987107-28.217668 81.120552-63.475683 3.780093-8.241708 9.238408-24.910343 11.741415-48.940642 0.665149-3.573385 3.593851-6.320962 7.348362-6.320962 2.503007 0 4.632507 1.280156 6.043646 3.120061l1.677199-1.039679c23.934109 34.751478 71.403237 111.695918 78.218456 192.827727 7.827269 92.76066 3.460822 156.289556-67.542302 221.285872-0.079818 0.079818-0.186242 0.186242-0.292666 0.267083-0.985444 1.066285-1.570775 2.453888-1.570775 3.973498 0 2.000564 1.065262 3.707438 2.609431 4.747117 0.585331 0.240477 1.171686 0.559748 1.757017 0.800225 0.478907 0.106424 0.931209 0.267083 1.411139 0.267083 0.479931 0 0.905626-0.159636 1.331321-0.267083 1.01205-0.533143 1.969864-1.147126 2.955308-1.680269 71.802326-39.552831 126.884852-105.615433 152.336524-185.066973 10.302647 15.308661 26.436092 26.323529 45.178969 30.163997-30.057573 136.713708-151.990646 222.272339-303.050084 235.261165-9.052167-22.456455-31.335682-38.405705-57.505715-38.405705-34.183544 0-61.898769 27.070542-61.898769 60.435441s27.714202 60.408835 61.898769 60.408835c27.554566 0 50.637284-17.709337 58.650795-42.032302 174.859493-14.21577 315.02993-118.150934 344.289324-279.640931 25.850761-10.80202 44.007283-36.165688 44.007283-65.769936L944.083879 430.42203C944.081833 391.029858 911.920342 359.078145 872.252901 359.078145zM807.744701 399.564231C762.140037 276.026614 643.960451 187.773617 504.935094 187.773617c-138.440026 0-256.192893 87.505983-302.224276 210.243375-2.289136-2.800789-4.978385-5.200442-7.614421-7.627724C221.160006 240.820788 349.882716 127.0977 505.09473 127.0977c154.467047 0 282.789645 112.602567 309.705668 261.131369C812.137755 391.803477 809.68796 395.537522 807.744701 399.564231zM376.292202 809.063743c-0.00307-0.001023-0.005117-0.002047-0.008186-0.00307C376.255363 809.125141 376.25434 809.133328 376.292202 809.063743z" fill="#2c2c2c" p-id="1510"></path></svg>
  </div>
          <span class="item-text">{{
            t("right.online") + t("right.customer")
          }}</span>
          <Chat v-if="showChat" @closeChat="pudateshowChat" />
        </div>
      </div>

      <!-- 分享按钮 -->
      <div
        class="menu-button blue-button location-button"
        @click="openShareModal"
      >
        <div class="icon-wrapper">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="20"
            height="20"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </div>
        <div class="button-text-multi">
          <span>{{ t("right.share") }}</span>
        </div>
      </div>

      <!-- 分享弹窗(定位在分享按钮下方) -->
      <Transition name="fade">
        <div
          v-if="showShareModal"
          class="share-modal-overlay"
          @mousedown.stop
          @click.self="closeShareModal"
        >
          <div class="share-modal">
            <div class="share-modal-header">
              <span>{{ t("right.shares") }}</span>
              <button class="close-btn" @click="closeShareModal">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            <div class="share-modal-body">
              <button class="share-btn qq" @click="shareTo('qq')">
                <div class="share-icon-wrapper">
                  <img
                    src="/src/assets/7db26f270ac9e70826dff125fa1b4b3.png"
                    alt=""
                  />
                </div>
                <span>QQ</span>
              </button>
              <button class="share-btn wechat" @click="shareTo('wechat')">
                <div class="share-icon-wrapper">
                  <svg
                    viewBox="0 0 1024 1024"
                    width="36"
                    height="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M692.677 347.791c11.947 0 23.894 0.853 35.84 2.133-32.427-151.04-193.707-263.68-377.173-263.68-205.653 0-373.333 140.373-373.333 313.173 0 101.12 55.04 184.32 147.2 248.747l-36.693 110.507 128.427-64.427c45.653 9.387 82.773 18.773 128.427 18.773 11.52 0 22.613-0.427 33.707-1.707-7.253-24.32-11.093-50.347-11.093-76.8 0-161.707 138.667-286.72 324.693-286.72zM498.304 248.064c27.733 0 46.080 18.347 46.080 45.653 0 27.307-18.347 45.653-46.080 45.653-27.307 0-54.613-18.347-54.613-45.653 0-27.307 27.307-45.653 54.613-45.653zM283.221 339.371c-27.307 0-54.613-18.347-54.613-45.653 0-27.307 27.307-45.653 54.613-45.653 27.733 0 46.080 18.347 46.080 45.653 0 27.307-18.347 45.653-46.080 45.653z"
                      fill="#00C800"
                    ></path>
                    <path
                      d="M1010.347 629.248c0-146.347-147.2-265.813-312.32-265.813-174.933 0-312.747 119.467-312.747 265.813 0 146.773 137.813 265.813 312.747 265.813 36.693 0 73.387-9.387 110.080-18.347l100.693 55.040-27.733-91.733c73.813-55.467 129.28-129.28 129.28-210.773zM618.197 583.168c-18.347 0-36.693-18.347-36.693-36.693 0-18.347 18.347-36.693 36.693-36.693 27.733 0 46.080 18.347 46.080 36.693 0 18.347-18.347 36.693-46.080 36.693zM769.664 583.168c-18.347 0-36.693-18.347-36.693-36.693 0-18.347 18.347-36.693 36.693-36.693 27.307 0 45.653 18.347 45.653 36.693 0 18.347-18.347 36.693-45.653 36.693z"
                      fill="#00C800"
                    ></path>
                  </svg>
                </div>
                <span>{{t("chatModal.wechat")}}</span>
              </button>
            </div>
            <div class="share-modal-footer">
              <div class="share-tip">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="tip-icon"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
                {{t("right.shareTip")}}
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 添加优惠券弹窗 -->
    <VoucherGiftModal
      v-if="userStore.showVoucherModal"
      :voucherData="userStore.currentVoucher"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { useI18n } from "../composables/useI18n";
import Aiavater from "./Aiavater.vue";
import { getvoucher } from "@/api/voucher";
import Chat from "./chat_modal.vue";
import { getQrcode } from "@/api/map";
import { useUserStore } from "@/store/user";
import VoucherGiftModal from "@/components/VoucherGiftModal.vue";
import { gettonyonggfx } from "@/api/map";
import { useChatModalStore } from "@/store/chatModal";
import { getRandomCustomer } from "@/api/pusher";

const { t } = useI18n();
const userStore = useUserStore();
const chatModalStore = useChatModalStore();
const activeQR = ref(""); // 当前激活的QR码
const timeoutIds = ref({}); // 存储所有超时ID的对象
const isServicing = ref(false);
let showChat = ref(false);

// 元素引用
const miniprogramItem = ref(null);
const wechatItem = ref(null);
const miniprogramQR = ref(null);
const wechatQR = ref(null);

//微信小程序
const WechatMiniprogram = ref("");
//微信社群
const WechatGroup = ref("");
// 分享弹窗相关
const showShareModal = ref(false);
const shareContent = ref("");

// 处理菜单项的鼠标进入事件
const handleMenuItemEnter = (type) => {
  // 清除所有相关的超时
  clearAllTimeouts();
  
  // 立即显示当前项的QR码，并确保其他的都隐藏
  activeQR.value = type;
};

// 处理菜单项的鼠标离开事件
const handleMenuItemLeave = (type) => {
  // 设置超时，延迟隐藏QR码，以便用户有时间移动到QR码上
  timeoutIds.value[type] = setTimeout(() => {
    // 只有当没有其他事件取消这个操作时，才隐藏QR码
    if (activeQR.value === type) {
      activeQR.value = "";
    }
  }, 200); // 增加延迟时间为200ms
};

// 处理QR码的鼠标进入事件
const handleQREnter = (type) => {
  // 清除对应项的超时
  if (timeoutIds.value[type]) {
    clearTimeout(timeoutIds.value[type]);
    delete timeoutIds.value[type];
  }
  
  // 确保QR码保持显示
  if (activeQR.value !== type) {
    activeQR.value = type;
  }
};

// 处理QR码的鼠标离开事件
const handleQRLeave = (type) => {
  // 立即隐藏QR码
  if (activeQR.value === type) {
    activeQR.value = "";
  }
};

// 清除所有超时
const clearAllTimeouts = () => {
  Object.values(timeoutIds.value).forEach(id => clearTimeout(id));
  timeoutIds.value = {};
};

const getgettonyonggfx = async () => {
  const res = await gettonyonggfx("分享");
  shareContent.value = res.data.info;
  console.log(shareContent.value);
};

// 添加随机获取客服并打开聊天窗口的方法
const openCustomerServiceChat = async () => {
  // 确保在点击客服按钮时关闭所有QR码
  activeQR.value = "";
  
  // 检查用户是否已登录
  if (!userStore.isLogin) {
    // 用户未登录，显示登录对话框
    userStore.showLoginDialog = true;
    return;
  }

  try {
    const res = await getRandomCustomer();
    if (res.code === 200 && res.data) {
      // 将客服信息转换为聊天所需的格式
      const customerService = {
        id: res.data.userId,
        name: res.data.nickName || res.data.userName || "客服",
        avatar:
          res.data.avatar ||
          "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/3825398873.jpg",
        phonenumber: res.data.phonenumber,
        introduction: res.data.introduction || "欢迎咨询，我是您的专属客服",
        companyName: res.data.companyName,
        memberType: "在线客服",
      };

      // 使用store打开聊天模态框
      chatModalStore.open(customerService);
    } else {
      console.error("获取客服信息失败");
    }
  } catch (error) {
    console.error("获取客服信息出错:", error);
  }
};

//页面渲染成功后调用方法
onMounted(() => {
  getQrcodeData();
  getgettonyonggfx();
});

// 组件卸载时清理所有超时
onUnmounted(() => {
  clearAllTimeouts();
});

const getQrcodeData = async () => {
  const res = await getQrcode();
  let data = res.data;
  console.log(data);

  data.forEach((item) => {
    if (item.type === "微信小程序二维码") {
      WechatMiniprogram.value = import.meta.env.VITE_BASE_API + item.qrCode;
    } else if (item.type === "微信社群二维码") {
      WechatGroup.value = import.meta.env.VITE_BASE_API + item.qrCode;
    }
  });
};

const pudateshowChat = () => {
  showChat.value = false;
  console.log(showChat.value);
};

const openShareModal = async () => {
  // 检查用户是否已登录
  if (!userStore.token || !userStore.userInfo || !userStore.userInfo.userId) {
    // 用户未登录，显示提示
    alert("请先登录后再分享，登录后可获得优惠券奖励");
    userStore.showLoginDialog = true;
    return;
  }
  
  showShareModal.value = true;
  
  // 确保每次打开弹窗都会复制内容
  // 稍微延迟以确保弹窗已经完全显示
  setTimeout(async () => {
    // 确保我们有内容可以复制
    if (!shareContent.value || shareContent.value.trim() === '') {
      // 如果没有内容，尝试重新获取
      await getgettonyonggfx();
    }
    copyText(shareContent.value);
  }, 300);
};

const closeShareModal = () => {
  showShareModal.value = false;
};

// 复制文本到剪贴板
async function copyText(text) {
  if (!text || text.trim() === '') {
    console.error("无内容可复制");
    return false;
  }

  // 显示一个临时提示，指示复制状态
  const showCopyStatus = (success) => {
    const statusDiv = document.createElement('div');
    statusDiv.textContent = success ? '复制成功！' : '复制失败，请重试';
    statusDiv.style.position = 'fixed';
    statusDiv.style.bottom = '20px';
    statusDiv.style.left = '50%';
    statusDiv.style.transform = 'translateX(-50%)';
    statusDiv.style.padding = '8px 16px';
    statusDiv.style.borderRadius = '4px';
    statusDiv.style.backgroundColor = success ? '#52c41a' : '#ff4d4f';
    statusDiv.style.color = 'white';
    statusDiv.style.zIndex = '10000';
    statusDiv.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
    document.body.appendChild(statusDiv);
    
    setTimeout(() => {
      document.body.removeChild(statusDiv);
    }, 2000);
  };

  let success = false;

  // 尝试使用现代 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text);
      success = true;
    } catch (err) {
      console.error("复制失败 (Clipboard API):", err);
      // 如果现代API失败，尝试备用方法
    }
  }

  // 如果现代API失败或不可用，尝试使用传统方法
  if (!success) {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    // 确保文本框不可见但仍然在页面上
    textarea.style.position = "fixed";
    textarea.style.left = "-999999px";
    textarea.style.top = "-999999px";
    textarea.style.opacity = "0";
    textarea.style.zIndex = "-1";
    document.body.appendChild(textarea);

    try {
      textarea.focus();
      textarea.select();
      success = document.execCommand("copy");
      if (!success) {
        console.error("复制命令执行失败");
      }
    } catch (e) {
      console.error("复制失败 (execCommand):", e);
    } finally {
      document.body.removeChild(textarea);
    }
  }

  // 显示复制状态
  showCopyStatus(success);
  return success;
}

// 跳转到QQ或微信客户端
async function shareTo(type) {
  // 再次复制内容，确保每次分享时内容都已复制
  const copySuccess = await copyText(shareContent.value);
  
  if (copySuccess) {
    if (type === "qq") {
      // 跳转QQ客户端
      window.location.href = "tencent://message/?uin=&Site=&Menu=yes";
    } else if (type === "wechat") {
      // 跳转微信客户端
      window.location.href = "weixin://";
    }
    console.log("分享跳转");
  } else {
    // 如果复制失败，提醒用户手动复制
    alert("复制分享内容失败，请手动复制后再分享");
  }

  // 使用已导入的userStore实例
  // 检查用户是否已登录
  if (!userStore.token || !userStore.userInfo || !userStore.userInfo.userId) {
    // 用户未登录，显示提示并引导登录
    setTimeout(() => {
      alert("请先登录后再分享，登录后可获得优惠券奖励");
      userStore.showLoginDialog = true;
    }, 1000);
    return;
  }

  // 10秒后获取优惠券
  setTimeout(async () => {
    try {
      const response = await getvoucher({
        userId: userStore.userInfo.userId,
        issuanceSource: "分享",
      });
      if (response.code === 200) {
        console.log("获取分享优惠券成功");
        // 设置优惠券数据并显示弹窗
        userStore.currentVoucher = response;
        userStore.showVoucherModal = true;
      }
    } catch (error) {
      console.error("获取优惠券失败:", error);
    }
  }, 6000); // 10秒延迟
}
</script>

<style scoped>
.floating-menu {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%) scale(0.85);
  z-index: 99;
}

/* 添加响应式样式 - 非PC端向上移动20px */
@media screen and (max-width: 1080px) {
  .floating-menu {
    bottom: 20px;
    top: auto;
    transform: scale(0.85);
  }
}

@media screen and (max-width: 768px) {
  .floating-menu {
    bottom: 20px;
    right: 15px;
  }
}

@media screen and (max-width: 480px) {
  .floating-menu {
    bottom: 20px;
    right: 10px;
  }
}

/* 横屏模式特殊处理 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
  .floating-menu {
    bottom: 20px;
    right: 15px;
  }
}

.menu-container {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  width: 60px;
  position: relative;
}

.menu-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  margin: 6px 0;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.menu-button:hover {
  transform: scale(1.05);
}

.blue-button {
  background-color: #548efe;
  color: white;
  padding: 10px 0;
}

.blue-button:hover {
  background-color: #5c8be8;
}

.location-button {
  border-radius: 21px;
  height: 60px;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text-multi {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 11px;
  margin-top: 4px;
}

.menu-items {
  width: 100%;
  padding: 6px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.menu-item:hover {
  color: #1677ff;
}

.menu-item:hover .icon-circle {
  background-color: #1677ff;
  color: white;
  transform: scale(1.1);
}

.icon-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3px;
  color: #666;
  transition: all 0.2s ease;
  cursor: pointer;
}

.item-text {
  font-size: clamp(8px, 0.7vw, 9px);
  color: #666;
  pointer-events: none; /* 文本不接收鼠标事件，避免干扰 */
  user-select: none; /* 防止文本选择 */
}

/* 添加更多的响应式字体大小控制 */
@media screen and (max-width: 1480px) {
  .item-text {
    font-size: clamp(7px, 0.65vw, 8px);
  }
}

@media screen and (max-width: 1280px) {
  .item-text {
    font-size: clamp(6px, 0.55vw, 7px);
  }
}

@media screen and (max-width: 1080px) {
  .item-text {
    font-size: clamp(5px, 0.45vw, 6px);
  }
}

@media screen and (max-width: 960px) {
  .item-text {
    font-size: clamp(4.5px, 0.4vw, 5.5px);
  }
}

@media screen and (max-width: 768px) {
  .item-text {
    font-size: clamp(4px, 0.35vw, 5px);
  }
}

/* 移动设备特殊处理 */
@media screen and (max-width: 480px) {
  .item-text {
    font-size: clamp(3.5px, 0.3vw, 4.5px);
  }
}

/* 横屏模式特殊处理 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
  .item-text {
    font-size: clamp(4px, 0.35vw, 5px);
  }
}

/* 适配极小屏幕 */
@media screen and (max-width: 375px) {
  .item-text {
    font-size: clamp(3px, 0.25vw, 4px);
  }
}

/* 优化QR弹出框的样式和交互 */
.qr-popup {
  position: absolute;
  right: 70px;
  top: -66px;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  width: 160px;
  text-align: center;
  pointer-events: auto; /* 确保弹出窗口可接收鼠标事件 */
  transition: opacity 0.2s ease;
}

/* 优化菜单项的交互区域，确保更好的点击体验 */
.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

/* 将hover行为仅限制在图标圆圈内 */
.icon-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3px;
  color: #666;
  transition: all 0.2s ease;
  cursor: pointer;
}

.icon-circle:hover {
  background-color: #1677ff;
  color: white;
  transform: scale(1.1);
}

.item-text {
  font-size: clamp(8px, 0.7vw, 9px);
  color: #666;
  pointer-events: none; /* 文本不接收鼠标事件，避免干扰 */
  user-select: none; /* 防止文本选择 */
}

/* 调整QR码出现的位置和指示箭头 */
.qr-popup::after {
  content: "";
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 0 8px 8px;
  border-style: solid;
  border-color: transparent transparent transparent white;
}

.qr-popup img {
  width: 120px;
  height: 120px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.qr-popup p {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* 分享弹窗样式优化 */
.share-modal-overlay {
  position: fixed;
  top: 350px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 995;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.share-modal {
  position: absolute;
  right: 70px;
  bottom: -50%;
  background: #fff;
  border-radius: 12px;
  width: 280px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-modal-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: color 0.2s;
  padding: 4px;
  border-radius: 50%;
}

.close-btn:hover {
  color: #333;
  background-color: #f5f5f5;
}

.share-modal-body {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
  justify-content: center;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: none;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  width: 100px;
}

.share-btn:hover {
  background: #e6f7ff;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.share-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.share-icon {
  width: 36px;
  height: 36px;
  transition: transform 0.3s;
}
.share-icon-wrapper img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}
.share-btn:hover .share-icon-wrapper {
  transform: scale(1.1);
}

.share-modal-footer {
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #888;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 8px;
}

.share-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tip-icon {
  color: #548efe;
  flex-shrink: 0;
}

/* Vue 3 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>