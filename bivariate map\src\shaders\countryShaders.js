export const countryShaders = {
  vertex: `
    uniform float time;
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    varying vec3 vViewPosition;
    
    void main() {
      // ...existing vertex shader code...
    }
  `,
  
  fragment: `
    uniform float time;
    uniform vec3 baseColor;
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    varying vec3 vViewPosition;
    
    void main() {
      // ...existing fragment shader code...
    }
  `
}
