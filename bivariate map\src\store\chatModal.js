import { defineStore } from 'pinia'
import { ref, watch, computed } from 'vue'
import { getUnreadCount } from '../api/pusher'

export const useChatModalStore = defineStore('chatModal', () => {
  // Initialize from localStorage or default to 0
  const weiduCount = ref(parseInt(localStorage.getItem('weiduCount') || '0'))
  const visible = ref(false)
  const currentSupplier = ref(null)
  // 添加一个状态来记录是从会员信息卡片打开的聊天
  const openedFromMemberCard = ref(false)
  
  // 计算属性：当前聊天窗口是否显示
  const showChat = computed(() => visible.value)

  // Watch for changes to weiduCount and update localStorage
  watch(weiduCount, (newCount) => {
    localStorage.setItem('weiduCount', newCount.toString())
  })

  function open(supplier = null, fromMemberCard = false) {
    visible.value = true
    openedFromMemberCard.value = fromMemberCard
    if(supplier) {
      currentSupplier.value = supplier
    }
    
    // 触发自定义事件，通知其他组件聊天窗口已打开
    // 使用window作为事件总线，方便跨组件通信
    window.dispatchEvent(new CustomEvent('chat-modal-opened'))
  }

  function close() {
    visible.value = false
    currentSupplier.value = null
    openedFromMemberCard.value = false
  }

  // Update the unread count (set to a specific value)
  function updateWeiduCount(count) {
    
    console.log(`更新未读消息计数：${weiduCount.value} => ${count}`)
    // 确保count是一个数字且不小于0
    const newCount = typeof count === 'number' ? Math.max(0, count) : 0
    weiduCount.value = newCount
    // 手动更新localStorage，以防watch不触发
    localStorage.setItem('weiduCount', newCount.toString())
  }

  // Increment the unread count by 1 or a specified amount
  function incrementWeiduCount(amount = 1) {
    weiduCount.value += amount
  }

  // Decrement the unread count by 1 or a specified amount, ensuring it doesn't go below 0
  function decrementWeiduCount(amount = 1) {
    weiduCount.value = Math.max(0, weiduCount.value - amount)
  }

  // Synchronize with server data
  async function syncUnreadCount() {
    try {
      const response = await getUnreadCount()
      if (response && response.code === 200 && response.data) {
        // 计算总未读消息数
        let totalUnread = 0
        
        // 遍历所有联系人的未读消息
        Object.entries(response.data).forEach(([userId, count]) => {
          const unread = parseInt(count)
          if (!isNaN(unread) && unread > 0) {
            totalUnread += unread
            console.log(`用户 ${userId} 有 ${unread} 条未读消息`)
          }
        })
        
        console.log(`同步后总未读消息数: ${totalUnread}`)
        // 确保未读计数为0或正数
        weiduCount.value = Math.max(0, totalUnread)
        // 存入本地存储
        localStorage.setItem('weiduCount', weiduCount.value.toString())
        return weiduCount.value
      }
      
      // 如果响应不符合预期，重置未读计数
      console.log('从服务器获取未读消息失败，重置本地计数')
      weiduCount.value = 0
      localStorage.setItem('weiduCount', '0')
      return 0
    } catch (error) {
      console.error('同步未读消息数量失败:', error)
      // 出错时不要修改当前计数
      return weiduCount.value
    }
  }

  return {
    visible,
    currentSupplier,
    weiduCount,
    openedFromMemberCard,
    showChat,
    open,
    close,
    updateWeiduCount,
    incrementWeiduCount,
    decrementWeiduCount,
    syncUnreadCount
  }
})
