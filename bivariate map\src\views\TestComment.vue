<template>
  <div class="test-page">
    <h2>评论测试页面</h2>
    
    <div class="section">
      <h3>使用修改后的组件</h3>
      <PlazaCommentForm :targetId="2" @comment-success="handleSuccess" />
    </div>
    
    <div class="section">
      <h3>使用直接请求测试</h3>
      <TestCommentForm />
    </div>
    
    <div v-if="commentList.length > 0" class="comment-list">
      <h3>当前评论列表</h3>
      <div v-for="(comment, index) in commentList" :key="index" class="comment-item">
        <div class="comment-content">{{ comment.content }}</div>
        <div class="comment-meta">
          <span>ID: {{ comment.id }}</span>
          <span>Target: {{ comment.targetId }}</span>
          <span v-if="comment.parentId">Parent: {{ comment.parentId }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import PlazaCommentForm from '../components/PlazaCommentForm.vue';
import TestCommentForm from '../components/TestCommentForm.vue';

// 评论列表
const commentList = ref([]);

// 处理评论成功的事件
const handleSuccess = (newComment) => {
  fetchComments();
};

// 获取评论列表
const fetchComments = async () => {
  try {
    const baseURL = import.meta.env.VITE_BASE_API || '';
    const response = await axios.get(`${baseURL}/comment/list`, {
      params: {
        targetId: 2,
        targetType: 'article'
      }
    });
    
    if (response.data.code === 200) {
      commentList.value = response.data.data || [];
    } else {
      ElMessage.warning('获取评论列表失败');
    }
  } catch (error) {
    console.error('获取评论列表出错:', error);
    ElMessage.error('获取评论列表失败');
  }
};

onMounted(() => {
  fetchComments();
});
</script>

<style scoped>
.test-page {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.comment-list {
  margin-top: 30px;
}

.comment-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.comment-content {
  font-size: 16px;
  margin-bottom: 8px;
}

.comment-meta {
  font-size: 12px;
  color: #999;
  display: flex;
  gap: 10px;
}
</style> 