<template>
  <div class="password-change-container">
    <div class="card">
      <div class="card-header">
        <h2>{{ t('changePassword.title') }}</h2>
        <p class="subtitle">{{ t('changePassword.subtitle') }}</p>
      </div>
      
      <form @submit.prevent="handleSubmit" class="password-form">
        <!-- 当前密码 -->
        <div class="form-group">
          <label for="currentPassword">{{ t('changePassword.currentPassword') }}</label>
          <div class="password-input-wrapper">
            <input 
              :type="showCurrentPassword ? 'text' : 'password'" 
              id="currentPassword" 
              v-model="currentPassword"
              :class="{ 'error': errors.currentPassword }"
              :placeholder="t('changePassword.currentPasswordPlaceholder')"
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showCurrentPassword = !showCurrentPassword"
            >
              <svg v-if="showCurrentPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
          <div v-if="errors.currentPassword" class="error-message">{{ errors.currentPassword }}</div>
        </div>
        
        <!-- 新密码 -->
        <div class="form-group">
          <label for="newPassword">{{ t('changePassword.newPassword') }}</label>
          <div class="password-input-wrapper">
            <input 
              :type="showNewPassword ? 'text' : 'password'" 
              id="newPassword" 
              v-model="newPassword"
              :class="{ 'error': errors.newPassword }"
              :placeholder="t('changePassword.newPasswordPlaceholder')"
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showNewPassword = !showNewPassword"
            >
              <svg v-if="showNewPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
          <div v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</div>
          
          <!-- 密码强度指示器 -->
          <div class="password-strength" v-if="newPassword">
            <div class="strength-label">{{ t('changePassword.strengthLabel') }}{{ t(`changePassword.strength.${passwordStrengthText}`) }}</div>
            <div class="strength-meter">
              <div 
                class="strength-meter-fill" 
                :style="{ width: passwordStrength + '%' }"
                :class="strengthClass"
              ></div>
            </div>
            <div class="password-requirements">
              <div class="requirement" :class="{ 'met': hasMinLength }">
                <svg v-if="hasMinLength" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
                {{ t('changePassword.requirements.minLength') }}
              </div>
              <div class="requirement" :class="{ 'met': hasNumber }">
                <svg v-if="hasNumber" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
                {{ t('changePassword.requirements.number') }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 确认新密码 -->
        <div class="form-group">
          <label for="confirmPassword">{{ t('changePassword.confirmPassword') }}</label>
          <div class="password-input-wrapper">
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              id="confirmPassword" 
              v-model="confirmPassword"
              :class="{ 'error': errors.confirmPassword }"
              :placeholder="t('changePassword.confirmPasswordPlaceholder')"
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showConfirmPassword = !showConfirmPassword"
            >
              <svg v-if="showConfirmPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
          <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="form-actions">
          <button type="button" class="btn-cancel" @click="resetForm">{{ t('changePassword.cancel') }}</button>
          <button type="submit" class="btn-submit" :disabled="isSubmitting">
            <span v-if="isSubmitting" class="loading-spinner"></span>
            <span v-else>{{ t('changePassword.submit') }}</span>
          </button>
        </div>
      </form>
      
      <!-- 操作结果提示 -->
      <div v-if="successMessage" class="success-message">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
        {{ successMessage }}
      </div>
      
      <div v-if="errorMessage" class="error-alert">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from '../composables/useI18n';
import { updateUserPwd } from '@/api/user';

const { t } = useI18n();

// 表单数据
const currentPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');

// 密码可见性控制
const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// 表单错误
const errors = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 表单状态
const isSubmitting = ref(false);
const successMessage = ref('');
const errorMessage = ref('');

// 密码强度检查
const hasMinLength = computed(() => newPassword.value.length >= 8);
const hasNumber = computed(() => /\d/.test(newPassword.value));
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(newPassword.value));

// 计算密码强度百分比
const passwordStrength = computed(() => {
  if (!newPassword.value) return 0;
  
  let strength = 0;
  if (hasMinLength.value) strength += 33;
  if (hasNumber.value) strength += 33;
  if (hasSpecialChar.value) strength += 34;
  
  return strength;
});

// 密码强度文本
const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value;
  if (strength === 0) return 'veryWeak';
  if (strength < 50) return 'weak';
  if (strength < 80) return 'medium';
  if (strength < 100) return 'strong';
  return 'veryStrong';
});

// 密码强度样式类
const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength < 33) return 'very-weak';
  if (strength < 66) return 'weak';
  if (strength < 100) return 'medium';
  return 'strong';
});

// 监听新密码变化，实时验证
watch(newPassword, () => {
  validateNewPassword();
});

// 监听确认密码变化，实时验证
watch(confirmPassword, () => {
  validateConfirmPassword();
});

// 验证当前密码
const validateCurrentPassword = () => {
  if (!currentPassword.value) {
    errors.value.currentPassword = t('changePassword.errors.currentPassword');
    return false;
  }
  errors.value.currentPassword = '';
  return true;
};

// 验证新密码
const validateNewPassword = () => {
  if (!newPassword.value) {
    errors.value.newPassword = t('changePassword.errors.newPassword');
    return false;
  }
  
  if (newPassword.value.length < 8) {
    errors.value.newPassword = t('changePassword.errors.minLength');
    return false;
  }
  
  if (!/\d/.test(newPassword.value)) {
    errors.value.newPassword = t('changePassword.errors.number');
    return false;
  }
  
  if (newPassword.value === currentPassword.value) {
    errors.value.newPassword = t('changePassword.errors.sameAsCurrent');
    return false;
  }
  
  errors.value.newPassword = '';
  return true;
};

// 验证确认密码
const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    errors.value.confirmPassword = t('changePassword.errors.confirmPassword');
    return false;
  }
  
  if (confirmPassword.value !== newPassword.value) {
    errors.value.confirmPassword = t('changePassword.errors.mismatch');
    return false;
  }
  
  errors.value.confirmPassword = '';
  return true;
};

// 验证整个表单
const validateForm = () => {
  const isCurrentPasswordValid = validateCurrentPassword();
  const isNewPasswordValid = validateNewPassword();
  const isConfirmPasswordValid = validateConfirmPassword();
  
  return isCurrentPasswordValid && isNewPasswordValid && isConfirmPasswordValid;
};

// 提交表单
const handleSubmit = async () => {
  // 清除之前的消息
  successMessage.value = '';
  errorMessage.value = '';
  
  // 验证表单
  if (!validateForm()) {
    return;
  }
  
  // 设置提交状态
  isSubmitting.value = true;
  
  try {
    // 调用修改密码接口
    const res = await updateUserPwd(currentPassword.value, newPassword.value);
    
    if (res.code === 200) {
      successMessage.value = t('changePassword.success');
      resetForm();
    } else {
      errorMessage.value = res.msg || t('changePassword.failure');
    }
  } catch (error) {
    errorMessage.value = error.response?.data?.msg || t('changePassword.failure');
    console.error('Error changing password:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  currentPassword.value = '';
  newPassword.value = '';
  confirmPassword.value = '';
  errors.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  showCurrentPassword.value = false;
  showNewPassword.value = false;
  showConfirmPassword.value = false;
};
</script>

<style scoped>
.password-change-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  display: flex;
  justify-content: center;
  padding: 20px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 700px;
  padding: 24px;
}

.card-header {
  margin-bottom: 24px;
}

.card-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

input {
  width: 100%;
  padding: 12px 40px 12px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

input.error {
  border-color: #ef4444;
}

.toggle-password {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.password-strength {
  margin-top: 12px;
}

.strength-label {
  font-size: 12px;
  margin-bottom: 4px;
  color: #666;
}

.strength-meter {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.strength-meter-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.strength-meter-fill.very-weak {
  background-color: #ef4444;
}

.strength-meter-fill.weak {
  background-color: #f97316;
}

.strength-meter-fill.medium {
  background-color: #eab308;
}

.strength-meter-fill.strong {
  background-color: #22c55e;
}

.password-requirements {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.requirement {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.requirement.met {
  color: #22c55e;
}

.requirement svg {
  flex-shrink: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}

.btn-cancel {
  padding: 10px 16px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-cancel:hover {
  background-color: #f9fafb;
}

.btn-submit {
  padding: 10px 16px;
  background-color: #4f46e5;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.btn-submit:hover {
  background-color: #4338ca;
}

.btn-submit:disabled {
  background-color: #818cf8;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.success-message, .error-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  margin-top: 20px;
}

.success-message {
  background-color: #f0fdf4;
  border: 1px solid #dcfce7;
  color: #16a34a;
}

.error-alert {
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  color: #dc2626;
}

@media (max-width: 600px) {
  .card {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-cancel, .btn-submit {
    width: 100%;
  }
}
</style>