import request from '../utils/request';

/**
 * 获取文章评论列表
 * @param {number|string} targetId 目标ID
 * @param {string} targetType 目标类型
 * @returns {Promise} 请求Promise
 */
export function getCommentList(targetId, targetType = 'article') {
  return request({
    url: '/comment/list',
    method: 'get',
    params: {
      targetId,
      targetType
    }
  });
}

/**
 * 发布评论（小广场）
 * @param {Object} data 评论数据
 * @param {string} data.content 评论内容
 * @param {number} data.targetId 目标ID
 * @param {string} data.targetType 目标类型（必须是文章）
 * @param {number} data.parentId 父评论ID（可选，回复评论时提供）
 * @param {number} data.rootId 根评论ID（可选，回复子评论时提供）
 * @param {number} data.userId 用户ID（需要传递给后端）
 * @returns {Promise} 请求Promise
 */
export function postComment(data) {
  // 过滤掉不需要的参数
  const requestData = {
    content: data.content,
    targetId: data.targetId,
    targetType: data.targetType,
    userId: data.userId
  };
  
  // 只有当parentId存在且不为0时添加
  if (data.parentId && data.parentId !== 0) {
    requestData.parentId = data.parentId;
  }
  
  // 只有当rootId存在且不为0时添加
  if (data.rootId && data.rootId !== 0) {
    requestData.rootId = data.rootId;
  }
  
  console.log('调用POST /comment接口，参数:', requestData);
  
  return request({
    url: '/comment',
    method: 'post',
    data: requestData
  });
}

/**
 * 添加评论
 * @param {Object} data 评论数据
 * @param {number|string} data.articleId 文章ID
 * @param {string} data.content 评论内容
 * @returns {Promise} 请求Promise
 */
export function addComment(data) {
  return request({
    url: '/comment/add',
    method: 'post',
    data
  });
}

/**
 * 回复评论
 * @param {Object} data 回复数据
 * @param {number|string} data.articleId 文章ID
 * @param {number|string} data.parentId 父评论ID
 * @param {string} data.content 回复内容
 * @param {number} data.userId 用户ID
 * @returns {Promise} 请求Promise
 */
export function addReply(data) {
  // 构建符合后端期望的数据格式 - 完全重构请求对象
  let replyData = {};
  
  // 添加基本字段
  replyData.content = data.content;
  replyData.targetId = data.articleId || data.targetId; // 兼容两种传参方式
  replyData.targetType = 'article';
  replyData.userId = data.userId; // 添加用户ID
  
  // 只有当父评论ID存在且有意义时才添加
  if (data.parentId && data.parentId !== 0) {
    replyData.parentId = data.parentId;
  }
  
  // 确保不包含rootId
  console.log("API发送的数据:", replyData);
  
  return request({
    url: '/comment',
    method: 'post',
    data: replyData
  });
}

/**
 * 删除评论
 * @param {number|string} commentId 评论ID
 * @returns {Promise} 请求Promise
 */
export function removeComment(commentId) {
  // 确保commentId是一个数字
  const id = parseInt(commentId);
  console.log('删除评论ID:', id);
  
  return request({
    url: '/comment',
    method: 'delete',
    params: {
      commentId: id
    }
  });
}

/**
 * 点赞评论
 * @param {number|string} commentId 评论ID
 * @returns {Promise} 请求Promise
 */
export function likeComment(commentId) {
  return request({
    url: `/comment/like/${commentId}`,
    method: 'post'
  });
}

/**
 * 取消点赞评论
 * @param {number|string} commentId 评论ID
 * @returns {Promise} 请求Promise
 */
export function unlikeComment(commentId) {
  return request({
    url: `/comment/unlike/${commentId}`,
    method: 'post'
  });
} 