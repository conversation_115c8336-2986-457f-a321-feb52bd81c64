#map {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.leaflet-control-attribution {
  display: none !important;
}

.leaflet-top.leaflet-right {
  margin-top: 100px;
}

.leaflet-popup-content {
  font-size: 14px;
  padding: 5px;
}

.leaflet-popup-content strong {
  color: #333;
  font-size: 16px;
}

.search-container {
  display: none;
}

.search-input {
  padding: 8px;

  width: 280px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.search-button {
  padding: 8px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-button:disabled {
  background: #cccccc;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  min-height: 50px;
}

.search-results-list {
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: all 0.2s;
}

.search-result-item:hover {
  background-color: #f8f8f8;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.result-number {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  margin-right: 8px;
}

.result-title {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.result-detail {
  padding-left: 32px;
  font-size: 12px;
  color: #666;
}

.result-coord {
  margin-right: 16px;
}

.search-status {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
  background: #f9f9f9;
  border-radius: 4px;
}

.search-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.search-error {
  color: #dc3545;
  font-size: 12px;
  margin: 4px 0;
  padding: 4px 8px;
  background-color: #fff3f3;
  border-radius: 4px;
}

.debug-info {
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px;
  margin-top: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
}

.leaflet-control-mouseposition {
  padding: 2px 5px;
  background: white;
  font-size: 12px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.leaflet-control-layers {
  background: white;
  padding: 6px;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.leaflet-popup-content {
  margin: 8px;
  font-size: 14px;
  color: #333;
}

/* 铁轨路线样式 */
.leaflet-overlay-pane path {
  /* 应用于所有路线 */
  transition: opacity 0.3s ease;
}

/* 确保铁轨线端点样式正确 */
.route-0 path,
.route-1 path,
.route-2 path {
  stroke-linecap: butt !important;
  stroke-linejoin: miter !important;
}

/* 路线标签样式优化 */
.route-label {
  background: transparent;
  box-shadow: none;
}

.route-number {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 站点图标样式 */
.station-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  /* 移除位移变换，保持图标位置固定 */
}

.station-icon-inner {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.3));
  /* 移除所有动画相关属性 */
}

.station-name-label {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  margin-top: 34px;
  /* 增加顶部边距，使标签置于图标下方 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  color: #333;
  position: absolute;
  /* 绝对定位确保标签位置固定 */
  transform: translateX(-50%);
  /* 水平居中 */
  left: 50%;
}

.start-station .station-name-label {
  color: #4CAF50;
  border-bottom: 2px solid #4CAF50;
}

.end-station .station-name-label {
  color: #FF5722;
  border-bottom: 2px solid #FF5722;
}

.route-search {
  margin-bottom: 16px;
}

.route-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.route-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
}

.route-button {
  padding: 8px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.route-button:disabled {
  background: #cccccc;
}

.route-options {
  margin-top: 0;
  border-top: none;
  padding: 0;
  background: white;
}

.route-options-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  font-size: 12px;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 1;
}

.toggle-routes {
  font-size: 12px;
  color: #548efe;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto; /* 将toggle-routes推到最右边 */
}

.dropdown-arrow {
  stroke: #548efe; /* 修改SVG颜色为蓝色 */
  transition: transform 0.3s ease;
  margin-left: 4px; /* 增加文字和图标的间距 */
}

.dropdown-arrow.is-expanded {
  transform: rotate(180deg);
}

.route-option {
  padding: 10px 12px;
  border: none;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.route-option:hover {
  background-color: #f8f8f8;
}

.route-option.active {
  border-color: #eee;
  background-color: #f8f8f8;
}

.route-option-left {
  margin-bottom: 4px;
}

.route-distance {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.route-stations {
  font-size: 11px;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.route-option-right {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.route-ports,
.route-time,
.route-quality {
  font-size: 11px;
  color: #666;
}

/* 修改热门站点样式 */
.hot-station-icon {
  position: relative;
  text-align: center;
}

.station-marker {
  position: relative;
  width: 100%;
  height: 100%;
  color: #ff0000;
  opacity: 0.8;
}

.station-marker::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #ff0000;
  animation: pulse 1.5s ease-out infinite;
  opacity: 0.6;
}

.station-marker::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #ff0000;
  animation: pulse 1.5s ease-out infinite;
  animation-delay: 0.3s;
}

@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 0.8;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.station-label {
  position: absolute;
  width: max-content;
  left: 50%;
  transform: translateX(-50%);
  bottom: -20px;
  font-size: 12px;
  color: #ff0000;
  font-weight: bold;
  text-shadow: 0 0 2px white;
  white-space: nowrap;
}

.station-popup {
  padding: 8px;
}

.station-popup h3 {
  margin: 0 0 8px 0;
  color: #333;
  border-bottom: 2px solid #eee;
  padding-bottom: 4px;
}

.station-popup p {
  margin: 4px 0;
  color: #666;
}

/*  */
.container {
  position: relative;
  left: 15px;
  top: 15px;
  width: 330px;
  background-color: #f5f7fa;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  /* 移除固定高度，由JavaScript动态控制 */
}

.container::-webkit-scrollbar {
  width: 6px;
}

.container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.header {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  color: white;
  /* padding: 8px 12px; */
}

.logo {
  font-size: 12px;
  margin-right: 8px;
}

.title {
  font-size: 14px;
}

.search-form {
  padding: 0px 12px 12px   12px;
  background-color: white;
  position: relative;
  top: -10px;
}

.form-group {
  margin-bottom: 15px;
}

.label {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding: 6px 0;
}

.select-value {
  color: #333;
  font-size: 12px;
}

.select-arrow {
  color: #999;
  font-size: 12px;
}

.radio-group {
  margin-bottom: 2px;
}

.radio-label {
  font-size: 12px;
  display: flex;
  align-items: center;
  position: relative;
}

.radio-input {
  position: absolute;
  opacity: 0;
}

.radio-custom {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #ddd;
  margin-right: 6px;
  position: relative;
}

.radio-input:checked+.radio-custom {
  border-color: #4285f4;
}

.radio-input:checked+.radio-custom::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 2.15px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4285f4;
}

.search-button {
  width: 100%;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.search-icon {
  margin-right: 5px;
}

.tabs {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #eee;
  padding: 0 12px;
  position: relative;
  top: -10px;
}

.tab {
  /* 鼠标悬停样式 */
  cursor: pointer;
  padding: 8px 12px;
  font-size: 10px;
  color: #666;
}

.tab.active {
  color: #4285f4;
  border-bottom: 2px solid #4285f4;
}

.results-list {
  background-color: white;
}

.result-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  font-size: 12px;
  color: #4285f4;
}

.result-description {
  font-size: 11px;
  color: #666;
  margin-left: 5px;
}

/* 隐藏缩放控件 */
.leaflet-control-zoom {
  display: block !important;
  margin-top: 60px !important;
  /* Nav组件下方间距 */
  margin-right: 30px !important;
  /* 增加右边距 */
}

.dropdown-arrow {
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.is-expanded {
  transform: rotate(180deg);
}

/* 添加Nav容器样式 */
.nav-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* 热门站点新样式 */
.map-container {
  position: relative;
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

}

.marker-container {
  position: absolute;
  bottom: 42px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.marker-label {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  min-width: 50px;
  z-index: 10;
}

.label-content {
  display: flex;
  align-items: flex-start;
}

.label-icon {
  color: #ff4d4f;
  margin-right: 8px;
  margin-top: 2px;
}

.label-text {
  display: flex;
  flex-direction: column;
}

.primary-text {
  font-size: 16px;
  color: red;
  margin-bottom: 2px;
}

.secondary-text {
  font-weight: bold;
  font-size: 12px;
  color: #333;
}

.label-arrow {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.marker-dot-container {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
}

.marker-dot {
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
  position: absolute;
  z-index: 5;
}

.pulse-ring {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 77, 79, 0.3);
  z-index: 4;
}

.pulse-ring-1 {
  width: 15px;
  height: 15px;
  animation: pulse 2s infinite;
}

.pulse-ring-2 {
  width: 15px;
  height: 15px;
  animation: pulse 2s infinite 0.6s;
}

.pulse-ring-3 {
  width: 15px;
  height: 15px;
  animation: pulse 2s infinite 1.2s;
}

@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 0.8;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 修改容器相关样式 */
.container {
  position: relative;
  left: 15px;
  top: 15px;
  width: 325px;
  background-color: #f5f7fa;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 99;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

/* 固定头部样式 */
.fixed-header {
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 6px 6px 0 0;
  /* overflow: hidden; */
}

/* 可滚动内容样式 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  background-color: transparent;
  border-radius: 0 0 6px 6px;
  max-height: 300px;
  /* 限制最大高度 */
  margin-bottom: 10px;
  /* 添加底部间距 */
}

/* 美化滚动条 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 路线选项样式优化 */
.route-options {
  padding: 0;
}

.route-options-title {
  padding: 8px 12px;
  /* 减小内边距 */
  font-size: 12px;
  /* 减小字号 */
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 1;
}

.route-count {
  color: #ffca79;
  margin-left: 4px;
}

.route-options-list {
  padding: 8px;
  background-color: #fff;
}

.route-options-list.all-routes {
  max-height: 250px;
  /* 相应调整列表最大高度 */
  overflow-y: auto;
}

.route-options-list .listcard {
  margin-bottom: 8px;
}

.route-options-list .listcard:last-child {
  margin-bottom: 0;
}

.route-option {
  position: relative;
  margin-bottom: 8px;
  /* 减小边距 */
  padding: 12px;
  /* 减小内边距 */
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  /* 减小圆角 */
  cursor: pointer;
  transition: all 0.2s ease;
}

.route-option:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.route-option.active {
  background: #eff6ff;
  border-color: #3b82f6;
}

.listcard.active {
  background-color: #ECF3FF;
  border: 1px solid #548efe;
}

.listcard:hover {
  background-color: #F5F8FF;
  border: 1px solid #548efe;
}

.route-index {
  position: absolute;
  top: 8px;
  /* 调整位置 */
  right: 8px;
  font-size: 11px;
  /* 减小字号 */
  color: #3b82f6;
  font-weight: 500;
}

.route-main-info {
  margin-bottom: 8px;
  /* 减小间距 */
}

.route-distance {
  margin-bottom: 2px;
  /* 减小间距 */
}

.distance-value {
  font-size: 14px;
  /* 减小字号 */
  font-weight: bold;
  color: #1f2937;
  margin-right: 2px;
}

.distance-unit {
  font-size: 10px;
  /* 减小字号 */
  color: #6b7280;
}

.route-time {
  font-size: 10px;
  /* 减小字号 */
  color: #4b5563;
  margin-bottom: 4px;
  /* 减小间距 */
}

.route-stations {
  font-size: 11px;
  /* 减小字号 */
  color: #374151;
  line-height: 1.0;
  /* 减小行高 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.route-detail {
  border-top: 1px dashed #e5e7eb;
  padding-top: 8px;
  /* 减小内边距 */
  margin-top: 8px;
  /* 减小间距 */
}

.route-ports {
  font-size: 11px;
  /* 减小字号 */
  color: #6b7280;
  margin-bottom: 4px;
  /* 减小间距 */
}

.route-status {
  display: flex;
  align-items: center;
  gap: 4px;
  /* 减小间距 */
  font-size: 11px;
  /* 减小字号 */
  color: #059669;
}

.status-dot {
  width: 4px;
  /* 减小大小 */
  height: 4px;
  background-color: #059669;
  border-radius: 50%;
}

/* 添加动画效果 */
.route-option {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加历史时间样式 */
.history-time {
  font-size: 10px;
  color: #999;
  margin-left: 8px;
}

.result-item {
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background-color: #f5f5f5;
}

/* 移除原有的其他路线按钮样式 */
.other-routes-button {
  display: none;
}

/* 添加新的路线信息容器样式 */
.route-info-container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

/* 添加右侧菜单样式 */
.right-menu {
  position: fixed;
  z-index: 100;
  right: 40px;
  /* 右侧间距 */
  bottom: 422px;
  /* 底部间距 */
  z-index: 1000;
}

/* 添加下拉框相关样式 */
.select-container {
  position: relative;
  cursor: pointer;
}

.select-arrow {
  transition: transform 0.3s ease;
}

.select-arrow.open {
  transform: rotate(180deg);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.select-option {
  padding: 8px 12px;
  font-size: 12px;
  color: #333;
  transition: background-color 0.2s;
}

.select-option:hover {
  background-color: #f5f5f5;
}

.select-option.selected {
  color: #4285f4;
  background-color: #f0f7ff;
}

.select-option.disabled {
  color: #bbb;
  cursor: not-allowed;
  background: #f5f5f5;
}

/* 修改表单组间距，适应新增的初始站 */
.form-group+.form-group {
  margin-top: 12px;
}

/* 展开按钮样式 */
.expand-button {
  float: right;
  font-size: 80%;
  color: #548efe;
  position: relative;
  top: -3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-bottom-img {
  width: 100%;
  z-index: 100;

}

.qweactive {
  color: #fca61b !important;
  font-weight: 900 !important;
  font-size: 14px !important;
}

.div-image {
  position: fixed;
  top: 480px;
  left: 15px;
  width: 320px;
  height: 100px;
  display: none;
  z-index: 1111;
  /* 确保在地图下方 */
}

.div-image img {
  width: 98%;
  height: 50%;
  object-fit: cover;
  border-radius: 8px;
}

/* 修改容器响应式样式 */
.container {
  position: relative;
  left: 15px;
  top: 15px;
  width: 325px;
  background-color: #f5f7fa;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 99;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

/* 添加媒体查询实现移动端自适应 */
@media screen and (max-width: 768px) {
  .container {
    width: 260px !important;
    transform: scale(0.85);
    transform-origin: top left;
    height: auto;
    max-height: 85vh;
  }

  .control-buttons {
    left: 220px !important;
    top: 10px;
  }

  .sidebar-hidden {
    transform: translateX(-260px) !important;
  }

  .buttons-shifted {
    left: 10px !important;
  }

  /* 搜索表单优化 */
  .search-form {
    padding: 4px 8px;
  }

  .form-group {
    margin-bottom: 6px;
  }

  .search-input {
    height: 32px;
    font-size: 13px;
  }

  .button-group {
    margin-top: 6px;
  }

  .search-button, .clear-button {
    height: 32px;
    font-size: 12px;
  }

  /* 标签页和结果列表优化 */
  .tabs {
    padding: 4px 6px;
    gap: 2px;
  }

  .tab {
    padding: 5px 6px;
    font-size: 11px;
  }

  .results-list {
    padding: 4px;
  }

  .result-item {
    padding: 8px;
    font-size: 12px;
    margin-bottom: 4px;
  }
}

/* 小屏幕手机竖屏模式 */
@media screen and (max-width: 375px) {
  .container {
    width: 240px !important;
    transform: scale(0.8);
    transform-origin: top left;
  }

  .control-buttons {
    left: 190px !important;
  }
}

/* 手机横屏模式 */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .container {
    width: 240px !important;
    transform: scale(0.8);
    transform-origin: top left;
    max-height: 92vh;
  }

  .scrollable-content {
    max-height: calc(100vh - 140px);
  }

  .search-form {
    padding: 2px 6px;
  }

  .form-group {
    margin-bottom: 4px;
  }

  .button-group {
    margin-top: 4px;
  }

  .tabs {
    padding: 2px 4px;
  }
}

/* 修复iPad等平板设备的显示 */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .container {
    width: 300px !important;
    transform: scale(0.9);
    transform-origin: top left;
    max-height: 90vh;
  }

.control-buttons {
    left: 250px !important;
  }
}

/* 调整路线选项和卡片布局 */
@media screen and (max-width: 768px) {
  .listcard {
    padding: 5px 6px;
    margin-bottom: 4px;
  }

  .route-name {
    max-width: 130px;
    font-size: 11px;
  }

  .tag {
    font-size: 9px;
    padding: 1px 5px;
  }

  .info-grid {
    gap: 3px;
  }

  .info-item {
    font-size: 10px;
  }

  .info-label, .info-value {
    font-size: 10px;
  }

  .route-options-title {
    font-size: 12px;
    padding: 4px 6px;
  }

  .route-count {
    font-size: 10px;
  }

  .toggle-routes {
    font-size: 10px;
  }
}

/* 优化滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 4px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 修改移动容器适配样式，确保路线列表完整显示 */
.mobile-container {
  transform: scale(0.9);
  transform-origin: top left;
  width: 260px !important;
  max-height: none !important; /* 移除最大高度限制 */
  overflow: visible !important; /* 确保内容不会被截断 */
}

/* 优化移动设备上的滚动内容区域 */
.mobile-container .scrollable-content {
  max-height: none !important; /* 不限制最大高度 */
  overflow-y: visible !important; /* 确保滚动内容可见 */
  padding-bottom: 20px; /* 添加底部间距 */
}

/* 确保路线列表在移动设备上可见 */
.mobile-container .route-options-list {
  padding: 4px;
  width: 100% !important;
  overflow: visible !important;
}

/* 调整路线卡片在移动设备上的显示 */
.mobile-container .listcard {
  width: 95% !important; 
  margin-bottom: 5px;
  box-sizing: border-box;
}

/* 修复控制按钮位置 */
.mobile-controls {
  left: 235px !important;
  position: absolute !important;
  z-index: 1001;
}

.mobile-controls.buttons-shifted {
  left: 15px !important;
  transform: translateX(0) !important;
}

/* 让列表项保持正常显示 */
.mobile-container .scrollable-content {
  padding: 8px !important;
}

/* 优化路线选项和卡片样式 */
.mobile-container .route-options-title {
  padding: 8px 10px;
}

/* 确保移动设备上路线卡片完整显示 */
.mobile-container .route-options-list {
  width: 100% !important;
  padding: 5px;
}

.mobile-container .listcard {
  width: 95% !important;
  margin: 0 auto 8px auto;
  box-sizing: border-box;
}

/* 修复控制按钮位置 */
.mobile-controls {
  left: 235px !important;
  position: absolute !important;
  z-index: 1001;
}

/* 覆盖原有媒体查询，使用更精确的样式控制 */
@media screen and (max-width: 768px) {
  /* 左侧栏完整显示 */
  .container {
    height: auto !important; /* 让高度自适应内容 */
    min-height: 300px; /* 设置最小高度 */
    overflow: visible !important;
    transform-origin: top left;
  }
  
  /* 滚动内容区域不设限制 */
  .scrollable-content {
    max-height: none !important;
    overflow-y: visible !important;
  }
  
  /* 路线列表卡片使用固定尺寸以确保显示 */
  .listcard {
    width: 95% !important;
    min-height: 80px;
  }
}

/* 确保viewport缩放不影响组件显示 */
@media screen and (orientation: portrait) {
  .mobile-container {
    transform: scale(0.85);
    transform-origin: top left;
    height: auto !important;
    min-height: 300px;
    width: 280px !important; /* 稍微加宽以显示更多内容 */
  }
}

/* 横屏模式优化 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-container {
    transform: scale(0.8);
    transform-origin: top left;
    width: 270px !important;
    height: auto !important;
  }
  
  /* 横屏模式下列表显示优化 */
  .mobile-container .route-options-list {
    padding: 2px;
  }
  
  .mobile-container .listcard {
    padding: 4px;
  }
}

/* 控制按钮样式 */
.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
  transition: all 0.2s ease;
  padding: 0;
  margin: 0;
}

.control-btn:hover {
  background: #f5f5f5;
  transform: scale(1.05);
}

.toggle-btn {
  z-index: 1001;
}

/* 确保移动设备上路线列表内容完整显示 */
@media screen and (max-width: 768px) {
  /* 调整路线选项显示 */
  .route-options {
    width: 100% !important;
    overflow: visible !important;
  }

  /* 确保路线列表完整渲染 */
  .route-options-list {
    height: auto !important;
    overflow: visible !important;
  }

  /* 优化移动设备上信息网格布局 */
  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 3px;
  }

  .info-row {
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;
  }
  
  /* 优化内容元素显示 */
  .headercard {
    margin-bottom: 8px;
  }
  
  .info-item {
    margin-right: 5px !important;
  }
  
  /* 修复容器内滚动问题 */
  .container.mobile-container {
    position: relative !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
  }
}

/* 移动端适配根本解决方案 */
/* 首先重置所有可能导致内容被隐藏的样式 */
.container {
  overflow: visible !important;
  max-height: none !important;
  /* height: auto !important; */
}

.scrollable-content {
  overflow: visible !important;
  max-height: none !important;
  height: auto !important;
}

.route-options,
.route-options-list,
.results-list {
  overflow: visible !important;
  max-height: none !important;
  height: auto !important;
}

/* 移动设备整体缩小策略 */
@media screen and (max-width: 768px) {
  /* 使用transform缩放整个容器而不是修改尺寸 */
  .container {
    transform: scale(0.85);
    transform-origin: top left;
    width: 300px !important; /* 保持与桌面端相同宽度，让内容完整显示 */
    margin-bottom: 50px; /* 添加底部间距避免内容被截断 */
  }
  
  /* 确保按钮位置随容器缩放调整 */
  .control-buttons {
    left: 255px !important;
  }
  
  /* 按钮位置调整 */
  .control-buttons.buttons-shifted {
    left: 15px !important;
  }
  
  /* 确保路线列表容器内容可见 */
  .route-options {
    margin-bottom: 30px;
  }
  
  /* 通过样式确保卡片完整显示 */
  .listcard {
    width: 95% !important;
    margin-bottom: 8px !important;
    background: white !important; /* 强制白色背景确保可见性 */
    border: 1px solid #e0e0e0 !important;
  }
  
  /* 小屏幕设备特殊处理 */
  @media (max-width: 375px) {
    .container {
      transform: scale(0.8);
      width: 320px !important;
    }
    
    .control-buttons {
      left: 256px !important;
    }
  }
  
  /* 超小屏幕设备处理 */
  @media (max-width: 320px) {
    .container {
      transform: scale(0.75);
      width: 340px !important;
    }
    
    .control-buttons {
      left: 255px !important;
    }
  }
}

/* 确保横屏模式下也能完整显示 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .container {
    transform: scale(0.8);
    transform-origin: top left;
    width: 320px !important;
    max-height: none !important;
  }
  
  .control-buttons {
    top: 10px !important;
    left: 256px !important;
}
}

/* 避免iOS safari中的奇怪行为 */
@supports (-webkit-overflow-scrolling: touch) {
  .container, 
  .scrollable-content,
  .route-options,
  .route-options-list {
    -webkit-overflow-scrolling: touch;
    overflow: visible !important;
  }
}

/* 强制路线列表显示的特殊样式 */
.mobile-view {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 300px !important;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.15);
  height: auto !important;
  transform: scale(0.85);
  transform-origin: top left;
}

.mobile-view .scrollable-content {
  position: static !important;
  display: block !important;
  visibility: visible !important;
  height: auto !important;
}

.mobile-view .route-options,
.mobile-view .route-options-list {
  display: block !important;
  visibility: visible !important;
  height: auto !important;
  width: 100% !important;
}

/* 调整路线卡片在移动视图中的显示 */
.mobile-view .listcard {
  display: block !important;
  width: 95% !important;
  margin: 0 auto 8px auto !important;
  background-color: #fff !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  position: relative !important;
  z-index: 1001 !important;
}

/* 强制显示信息项 */
.mobile-view .info-grid,
.mobile-view .info-row,
.mobile-view .info-item {
  display: block !important;
  width: auto !important;
  margin: 4px 0 !important;
}

/* 移动视图下控制按钮调整 */
.mobile-view + .control-buttons {
  left: 265px !important;
  top: 20px !important;
  z-index: 1002 !important;
  transform: scale(1.1) !important;
}

.mobile-view + .control-buttons.buttons-shifted {
  left: 10px !important;
  top: 20px !important;
}

/* 确保控制按钮在移动端正确显示 */
@media screen and (max-width: 768px) {
  .control-buttons {
    top: 20px !important;
    transform: translateY(0) !important;
  }
  
  .sidebar-hidden + .control-buttons {
    left: 10px !important;
  }
}

/* 优化移动设备上路线列表的布局 */
.mobile-view .route-options-title {
  font-size: 14px !important;
  padding: 8px 10px !important;
  background: #f8f8f8 !important;
  border-bottom: 1px solid #eee !important;
}

.mobile-view .listcard {
  padding: 8px !important;
}

.mobile-view .headercard {
  margin-bottom: 5px !important;
}

/* 优化信息项布局 */
.mobile-view .info-grid {
  margin-top: 5px !important;
}

.mobile-view .info-row {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 5px !important;
}

.mobile-view .info-item {
  display: flex !important;
  align-items: center !important;
  font-size: 12px !important;
}

.mobile-view .info-label {
  margin-right: 3px !important;
}

/* 最终路线列表修复 */
.route-options {
  display: block !important;
}

.route-options-list {
  display: block !important;
}

.listcard {
  position: relative !important;
  z-index: 10 !important;
}

/* 确保在任何设备上列表都能显示 */
@media screen and (max-width: 767px) {
  /* 重置所有可能的限制 */
  .container, 
  .scrollable-content,
  .route-options,
  .route-options-list,
  .listcard {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }
  
  /* 强制显示和背景色 */
  .listcard {
    display: block !important;
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
    margin-bottom: 8px !important;
  }
  
  /* 确保标题和选项卡可见 */
  .fixed-header {
    background-color: white !important;
    position: relative !important;
    z-index: 9 !important;
  }
}

/* 修复PC端滚轮滑动功能 */
/* 先恢复桌面端的滚动样式 */
@media screen and (min-width: 769px) {
  .container {
    overflow: hidden !important; /* PC端需要隐藏溢出内容 */
  }
  
  .scrollable-content {
    overflow-y: auto !important; /* PC端需要垂直滚动 */
    max-height: calc(100% - 180px) !important; /* 保留头部空间 */
  }
  
  .route-options-list {
    overflow-y: auto !important;
    max-height: 250px !important; /* 设置合适的最大高度 */
  }
}

/* 移动端保持现有样式不变 */
@media screen and (max-width: 768px) {
  .container, 
  .scrollable-content,
  .route-options,
  .route-options-list {
    overflow: visible !important;
    max-height: none !important;
  }
}

/* 专门为桌面端优化的滚动条样式 */
@media screen and (min-width: 769px) {
  .scrollable-content::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollable-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  .scrollable-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  .scrollable-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* 添加新的专用样式类 */
.pc-scrollable {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: calc(100% - 180px) !important;
  position: relative;
  top: -10px;
}

.mobile-visible {
  overflow: visible !important;
  max-height: none !important;
  height: auto !important;
}

/* 确保路线选项区域PC端能滚动 */
.pc-scrollable .route-options {
  position: relative;
}

.pc-scrollable .route-options-list {
  overflow-y: auto !important;
  max-height: 250px !important;
}

/* 确保滚动条在PC端可用 */
.pc-scrollable::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.pc-scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pc-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 解决容器高度问题 */
@media screen and (min-width: 769px) {
  .container {
    /* height: 600px !important; 固定高度 */
    overflow: hidden !important;
  }
}

@media screen and (max-width: 768px) {
  .container.mobile-view {
    height: auto !important;
    min-height: auto !important;
  }
}

/* 移动设备60%缩放样式 */
body.mobile-scaled-60 {
  overflow: hidden !important;
}

html.mobile-scaled-60 {
  transform: scale(0.6);
  transform-origin: top left;
  width: 166.67%; /* 100% / 0.6 = 166.67% */
  height: 166.67%;
  position: relative;
  overflow-x: hidden;
}

/* 防止iOS橡皮筋效果 */
html.mobile-scaled-60,
body.mobile-scaled-60 {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-overflow-scrolling: none;
  touch-action: none;
  overscroll-behavior: none;
}

/* 内联下拉菜单样式 - 直接显示在输入框下方 */
.inline-suggestion-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid #e2e8f0;
  overflow-y: auto;
  max-height: 350px;
  z-index: 9999;
  margin-top: 5px;
}

/* 确保表单相关容器在移动设备上仍然可见 */
.select-container {
  position: relative;
  /* z-index: 56; */
  overflow: visible !important;
}

/* 修复移动设备上的下拉菜单 */
@media screen and (max-width: 768px) {
  .station-suggestions-list {
    max-height: 200px; /* 减小高度以适应小屏幕 */
    position: absolute;
    z-index: 1000;
  }
  
  /* 确保下拉菜单在移动设备横屏模式下仍能显示 */
  .mobile-landscape .station-suggestions-list {
    max-height: 150px;
  }
  
  /* 调整表单组和下拉框的z-index */
  .form-group:nth-child(1) {
    position: relative;
    z-index: 1003;
  }
  
  .form-group:nth-child(2) {
    position: relative;
    z-index: 1002;
  }
  
  .form-group:nth-child(3) {
    position: relative;
    z-index: 1001;
  }
  
  /* 根据表单顺序调整下拉框z-index */
  .form-group:nth-child(2) .start-suggestions {
    z-index: 1002;
  }
  
  .form-group:nth-child(3) .destination-suggestions {
    z-index: 1001;
  }
}

/* 目的站下拉菜单定位 */
.destination-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1001;
  width: 100%;
  margin-top: 5px;
}

/* 起始站下拉菜单定位 */
.start-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1001;
  width: 100%;
  margin-top: 5px;
}

/* 移动设备下拉菜单样式增强 */
@media screen and (max-width: 768px) {
  .destination-suggestions,
  .start-suggestions {
    position: fixed !important;
    top: 25% !important; /* 固定在页面中上部 */
    left: 4% !important;
    width: 92% !important;
    max-height: 40vh !important;
    z-index: 10000 !important; /* 确保在最顶层 */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    background: white !important;
    border-radius: 8px !important;
    border: 1px solid #e2e8f0 !important;
    overflow-y: auto !important;
    margin-top: 0 !important;
    transform: none !important;
  }
  
  /* 确保下拉框内容可见 */
  .station-suggestion-item {
    padding: 10px 12px !important;
    font-size: 14px !important;
  }
  
  /* 设置清晰的标题样式 */
  .suggestions-header {
    padding: 10px 12px !important;
    background-color: #f8f9fa !important;
    font-weight: bold !important;
    color: #333 !important;
    border-bottom: 1px solid #e2e8f0 !important;
  }
}

/* 修复移动端下拉菜单重叠问题 */
@media screen and (max-width: 768px) {
  /* 确保表单组正确定位 */
  .form-group {
    position: relative;
    z-index: auto !important;
  }
  
  /* 确保起始站和目的站的下拉菜单不会互相覆盖 */
  .form-group:nth-child(1) .select-container {
    z-index: 1003;
  }
  
  .form-group:nth-child(2) .select-container {
    z-index: 1002;
  }
  
  .form-group:nth-child(3) .select-container {
    z-index: 1001;
  }
  
  /* 根据不同的输入框调整下拉菜单层级 */
  .start-suggestions {
    z-index: 1002 !important;
  }
  
  .destination-suggestions {
    z-index: 1001 !important;
  }
}