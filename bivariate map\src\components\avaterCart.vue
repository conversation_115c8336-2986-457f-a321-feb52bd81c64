<template>
  <div>
    <!-- 会员容器 -->
  <div class="supplier-container" :style="computedStyle">
    <!-- 有会员时显示的内容 -->
    <template v-if="hasMembers">  
      <!-- 供应商信息头部 -->
      <div class="supplier-header">
        <h3>{{ t('avaterCart.siteMembers') }} ({{ totalMembersCount }})</h3>
          <div class="supplier-icons-container">
            <!-- 超级会员行 -->
            <div class="members-row">
          <div 
            v-for="supplier in superMembersForDisplay" 
            :key="supplier.id" 
            class="icon-wrapper"
            @click="selectSupplier(supplier, $event)"
            @mouseenter="showMemberInfo(supplier, $event)"
            :class="{ 'active': selectedSupplier && selectedSupplier.id === supplier.id }"
          >
            <img :src="supplier.avatar" :alt="supplier.name" class="supplier-icon" />
                <div class="member-type-badge vip-badge">VIP</div>
              </div>
          </div>
          
          <!-- 分割线，当有普通会员时显示 -->
          <div v-if="ordinaryMembersForDisplay.length > 0" class="member-divider"></div>
          
          <!-- 直接显示前10个普通会员 -->
          <div v-if="visibleOrdinaryMembers.length > 0" class="members-row">
            <div 
              v-for="supplier in visibleOrdinaryMembers" 
              :key="supplier.id" 
              class="icon-wrapper"
              @click="selectSupplier(supplier, $event)"
              @mouseenter="showMemberInfo(supplier, $event)"
              :class="{ 'active': selectedSupplier && selectedSupplier.id === supplier.id }"
            >
              <img :src="supplier.avatar" :alt="supplier.name" class="supplier-icon" />
                <div class="member-type-badge normal-badge">VIP</div>
            </div>
          </div>

          <!-- 需要展开才显示的普通会员（第11个开始） -->
          <div v-if="expanded && extraOrdinaryMembers.length > 0" class="members-row">
            <div 
              v-for="supplier in extraOrdinaryMembers" 
              :key="supplier.id" 
              class="icon-wrapper"
              @click="selectSupplier(supplier, $event)"
              @mouseenter="showMemberInfo(supplier, $event)"
              :class="{ 'active': selectedSupplier && selectedSupplier.id === supplier.id }"
            >
              <img :src="supplier.avatar" :alt="supplier.name" class="supplier-icon" />
                <div class="member-type-badge normal-badge">VIP</div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <!-- 当有需要展开显示的普通会员时，显示展开/收起按钮 -->
          <button v-if="extraOrdinaryMembers.length > 0" class="expand-btn" @click="toggleExpand">
            <template v-if="expanded">
              {{ t('avaterCart.collapse') }}
            </template>
            <template v-else>
              {{ t('avaterCart.expand') }}
              ({{ extraOrdinaryMembers.length }}{{ t('avaterCart.more') }})
            </template>
          </button>
        </div>
      </div>
      </template>
          </div>

    <!-- 会员详细信息卡片 - 显示在头像正上方 -->
    <div v-if="selectedSupplier" class="member-info-popup-external" :style="popupPosition">
    

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useUserStore } from '../store/user';
import { useChatModalStore } from '../store/chatModal';
import { useI18n } from '../composables/useI18n';

// Add the translation function
const { t } = useI18n();

// 翻译已在翻译文件中定义

const props = defineProps({
  left: Number,
  top: Number,
  ordinaryMembers: {
    type: Array,
    default: () => []
  },
  superMembers: {
    type: Array,
    default: () => []
  }
});

// 控制展开/收起状态
const expanded = ref(false);

// 控制显示所有点赞用户状态
const showAllLikes = ref(false);

// 点赞状态
const isLiked = ref(false);

// 计算是否有会员数据
const hasMembers = computed(() => {
  const ordinaryCount = props.ordinaryMembers?.length || 0;
  const superCount = props.superMembers?.length || 0;
  return (ordinaryCount + superCount) > 0;
});

// 计算会员数量
const ordinaryMembersCount = computed(() => props.ordinaryMembers?.length || 0);
const superMembersCount = computed(() => props.superMembers?.length || 0);
const totalMembersCount = computed(() => ordinaryMembersCount.value + superMembersCount.value);

// 分离超级会员和普通会员显示
const superMembersForDisplay = computed(() => {
  if (!props.superMembers?.length) return [];
  return suppliers.value.filter(supplier => supplier.memberType === '超级会员');
});

const ordinaryMembersForDisplay = computed(() => {
  if (!props.ordinaryMembers?.length) return [];
  return suppliers.value.filter(supplier => supplier.memberType !== '超级会员');
});

// 判断超级会员是否超过一行(默认每行显示10个)
const superMembersExceedOneRow = computed(() => {
  return superMembersForDisplay.value.length > 10;
});

// 直接可见的普通会员（前10个）
const visibleOrdinaryMembers = computed(() => {
  return ordinaryMembersForDisplay.value.slice(0, 10);
});

// 需要展开才能看到的普通会员（第11个开始）
const extraOrdinaryMembers = computed(() => {
  return ordinaryMembersForDisplay.value.slice(10);
});

// 切换展开/收起供应商图标
const toggleExpand = () => {
  expanded.value = !expanded.value;
  
  // 如果点击展开按钮，则显示所有点赞用户
  if (expanded.value) {
    showAllLikes.value = true;
    // 如果不是通过点击选择的供应商，则隐藏会员信息卡片
    if (!selectedByClick.value) {
      selectedSupplier.value = null;
    }
  } else {
    showAllLikes.value = false;
    // 收起时，如果不是通过点击选择的供应商，则隐藏会员信息卡片
    if (!selectedByClick.value) {
      selectedSupplier.value = null;
    }
  }
};

// 切换显示所有点赞用户
const toggleAllLikes = () => {
  showAllLikes.value = !showAllLikes.value;
  if (!showAllLikes.value) {
    expanded.value = false;
  }
};

// 切换点赞状态
const likeUsers = ref([]);

// 供应商数据 - 处理会员数据
const suppliers = computed(() => {
  if (!hasMembers.value) return [];
  
  // 处理超级会员
  const superList = (props.superMembers || []).map(member => ({
    id: member.userId,
    name: member.nickName || t('avaterCart.noNickname'),
    company: member.companyName || t('avaterCart.noCompanyInfo'),
    avatar: member.avatar 
      ? (member.avatar.startsWith('http') 
          ? member.avatar 
          : `${import.meta.env.VITE_BASE_API}${member.avatar}`)
      : 'https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/3825398873.jpg',
    vipLevel: t('avaterCart.superMember'),
    rating: 5.0,
    percentage: 420,
    phone: member.phone,
    qq: member.qq,
    email: member.email,
    memberType: '超级会员',
    introduction: member.introduction,
    businessCard:  member.businessCard ? `${import.meta.env.VITE_BASE_API}${member.businessCard}` : null
  }));

  // 处理普通会员
  const ordinaryList = (props.ordinaryMembers || []).map(member => ({
    id: member.userId,
    name: member.nickName || t('avaterCart.noNickname'),
    company: member.companyName || t('avaterCart.noCompanyInfo'),
    avatar: member.avatar 
      ? (member.avatar.startsWith('http') 
          ? member.avatar 
          : `${import.meta.env.VITE_BASE_API}${member.avatar}`)
      : `https://picsum.photos/50/50?random=${member.userId}`,
    vipLevel: t('avaterCart.normalMember'),
    rating: 3.5,
    percentage: 180,
    phone: member.phone,
    qq: member.qq,
    email: member.email,
    memberType: '普通会员',
    introduction: member.introduction,
    businessCard: member.businessCard ? `${import.meta.env.VITE_BASE_API}${member.businessCard}` : null
  }));

  return [...superList, ...ordinaryList];
});

// 当前选中的供应商
const selectedSupplier = ref(null);
// 存储点击的元素位置信息
const clickedElementRect = ref(null);
// 记录是否通过点击选择了供应商
const selectedByClick = ref(false);

// 计算弹出框位置
const popupPosition = computed(() => {
  if (!clickedElementRect.value) return {};
  
  // 完全重新计算位置
  return {
    position: 'absolute',
    left: '50%', // 居中
    top: '0px', // 放在容器顶部
    transform: 'translate(-50%, -100%)', // 水平居中并向上平移100%
    zIndex: '1000',
    width: '280px'
  };
});

// 鼠标悬浮时显示会员信息
const showMemberInfo = (supplier, event) => {
  try {
    // 如果已经通过点击选择了供应商，则不再响应悬浮事件
    if (selectedByClick.value) {
      return;
    }
    
    // 检查聊天窗口是否已打开，如果已打开则不显示会员信息
    const chatModalStore = useChatModalStore();
    if (chatModalStore?.visible) {
      // 如果聊天窗口已打开，不再显示会员信息
      return;
    }
    
    // 安全检查事件和组件是否有效
    if (!event || !supplier || !event.currentTarget) return;
    
    // 获取元素位置信息
    const element = event.currentTarget;
    const rect = element.getBoundingClientRect();
    
    // 存储相对于视口的位置
    clickedElementRect.value = {
      left: rect.left + (rect.width / 2), // 取头像中心点的水平位置
      top: rect.top, // 取头像顶部位置
      width: rect.width,
      height: rect.height
    };
    
    // 设置当前选中的供应商
    selectedSupplier.value = supplier;
    
    // 通知父组件显示会员信息卡片
    emit('show-member-info', {
      supplier,
      position: clickedElementRect.value
    });
  } catch (error) {
    console.error('显示会员信息时出错:', error);
  }
};

// 鼠标移开时隐藏会员信息，除非是点击选择的
const hideIfNotClicked = () => {
  // 只有在非点击选择状态下才隐藏会员信息
  if (!selectedByClick.value) {
    selectedSupplier.value = null;
    
    // 通知父组件隐藏会员信息卡片
    if (selectedSupplier.value === null) {
      emit('show-member-info', null);
    }
  }
};

// 选择供应商
const selectSupplier = (supplier, event) => {
  try {
    // 安全检查
    if (!supplier || !event) return;
    
    // 检查聊天窗口是否已打开，如果已打开则不处理点击
    const chatModalStore = useChatModalStore();
    if (chatModalStore?.visible) {
      return; // 如果聊天窗口已打开，不再处理点击
    }
    
    // 安全检查事件对象
    if (!event.currentTarget) return;
    
    // 获取点击元素的位置信息
    const element = event.currentTarget;
    const rect = element.getBoundingClientRect();
    
    // 存储相对于视口的位置
    clickedElementRect.value = {
      left: rect.left + (rect.width / 2), // 取头像中心点的水平位置
      top: rect.top, // 取头像顶部位置
      width: rect.width,
      height: rect.height
    };
    
    if (selectedSupplier.value && selectedSupplier.value.id === supplier.id) {
      // 如果点击的是当前已选中的供应商，则取消选择
      selectedSupplier.value = null;
      selectedByClick.value = false;
    } else {
      // 否则选择新的供应商
      selectedSupplier.value = supplier;
      selectedByClick.value = true;
      
      // 通知父组件显示会员信息卡片
      emit('show-member-info', {
        supplier,
        position: clickedElementRect.value
      });
    }
  } catch (error) {
    console.error('选择供应商时出错:', error);
  }
};

// 计算总点赞数
const totalLikes = computed(() => {
  return 2;
});

// 显示业务简介
const showBusinessIntro = () => {
  if (!selectedSupplier.value) return;
  emit('show-business-intro', selectedSupplier.value);
};

const openQQChat = (qq) => {
  if (!qq) {
    alert(t('avaterCart.qqNotProvided'));
    return;
  }

  // 多重备份链接
  const urls = [
    `http://qm.qq.com/q/uin=${qq}&web_src=localhost&menu=yes`
  ];

  // 依次尝试打开链接
  const tryNextUrl = (index = 0) => {
    if (index >= urls.length) {
      alert(t('avaterCart.qqNotProvided'));
      return;
    }

    const win = window.open(urls[index], '_blank');
    
    // 如果打开失败，尝试下一个链接
    if (!win || win.closed || typeof win.closed == 'undefined') {
      setTimeout(() => tryNextUrl(index + 1), 500);
    }
  };

  tryNextUrl();
};

const canvasRef = ref(null);
let animationFrame = null;

// 监听窗口大小变化，用于响应式布局
const handleResize = () => {
  // 仅触发响应式计算
  const temp = window.innerWidth;
  computedStyle.value; // 强制重新计算
};

// 处理聊天窗口打开事件，隐藏会员信息卡片
const handleChatModalOpened = () => {
  // 重置选中状态
  selectedSupplier.value = null;
  selectedByClick.value = false;
  
  // 通知父组件隐藏会员信息卡片
  emit('show-member-info', null);
};

onMounted(() => {
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
  
  // 添加聊天窗口打开事件监听
  window.addEventListener('chat-modal-opened', handleChatModalOpened);
});

onUnmounted(() => {
  // 清理动画
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
  
  // 移除聊天窗口打开事件监听
  window.removeEventListener('chat-modal-opened', handleChatModalOpened);
  
  // 清除选中状态
  selectedSupplier.value = null;
  selectedByClick.value = false;
});

// 添加计算样式
const computedStyle = computed(() => {
  // 根据屏幕宽度调整容器宽度
  const screenWidth = window.innerWidth;
  let maxWidth = '500px'; // 默认最大宽度
  
  if (screenWidth <= 450) {
    maxWidth = '90vw'; // 小屏幕上占据90%视口宽度
  } else if (screenWidth <= 768) {
    maxWidth = '360px'; // 中等屏幕上使用固定宽度
  }
  
  return {
    position: 'absolute',
    left: props.left + 'px',
    top: props.top + 'px',
    minWidth: '180px',
    maxWidth: maxWidth,
    width: 'auto',
    overflowX: 'hidden'
  };
});

// 聊天相关
const emit = defineEmits(['map-control', 'open-chat', 'show-business-intro', 'show-member-info']);
const userStore = useUserStore();
const chatModalStore = useChatModalStore();

// 监听聊天窗口状态，当聊天窗口打开时自动隐藏会员卡片
watch(() => chatModalStore.visible, (newValue) => {
  if (newValue) {
    // 聊天窗口打开时，隐藏会员信息卡片
    selectedSupplier.value = null;
    selectedByClick.value = false;
  }
});

// 监听登录对话框状态，当登录对话框显示时自动隐藏会员卡片
watch(() => userStore.showLoginDialog, (newValue) => {
  if (newValue) {
    // 登录对话框显示时，隐藏会员信息卡片
    selectedSupplier.value = null;
    selectedByClick.value = false;
  }
});

// 添加一个全局事件监听器来监听登录弹窗的显示状态
onMounted(() => {
  // 监听登录弹窗显示事件
  const handleLoginShow = () => {
    selectedSupplier.value = null;
    selectedByClick.value = false;
  };

  // 监听登录弹窗隐藏事件
  const handleLoginHide = () => {
    // 登录弹窗关闭时可以选择性地执行一些操作
  };

  // 添加事件监听器
  window.addEventListener('login-modal-show', handleLoginShow);
  window.addEventListener('login-modal-hide', handleLoginHide);

  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('login-modal-show', handleLoginShow);
    window.removeEventListener('login-modal-hide', handleLoginHide);
  });
});

const handleContact = (supplier) => {
  if (!userStore.token) {
    // 用户未登录，隐藏会员信息卡片并显示登录对话框
    selectedSupplier.value = null;
    selectedByClick.value = false;
    userStore.showLoginDialog = true;
    return;
  }
  emit('map-control', false);

  // 只在聊天窗口未打开时才打开聊天
  if (!chatModalStore.visible) {
    chatModalStore.open(supplier); // 使用 Pinia 打开聊天弹窗并传递联系人
  }
};
</script>

<style scoped>
.supplier-container {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.15);
  pointer-events: auto;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 无会员状态样式 */
.no-members-container {
  padding: 12px 14px;
  text-align: center;
}

.no-members-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-members-icon {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: #999;
}

.no-members-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
}

.no-members-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.supplier-header {
  padding: 12px;
  background-color: #fff;
  box-sizing: border-box;
}

.supplier-header h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.supplier-icons-container {
  position: relative;
  margin-bottom: 12px;
  width: 100%;
}

.members-row {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 6px;
  margin-bottom: 8px;
  width: 100%;
  box-sizing: border-box;
}

.member-divider {
  width: 100%;
  height: 1px;
  background-color: #f0f0f0;
  margin: 12px 0;
  position: relative;
}

.member-divider::after {
  content: attr(data-text);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 0 8px;
  font-size: 10px;
  color: #999;
}

.icon-wrapper {
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
}

.icon-wrapper:hover {
  transform: translateY(-2px);
}

.icon-wrapper.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: #f39c12;
  border-radius: 50%;
}

.icon-wrapper {
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 36px;
  margin: 0 auto;
}

.supplier-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: none;
  transition: all 0.2s ease;
}

.icon-wrapper.active .supplier-icon {
  box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.3);
}

.icon-wrapper:hover .supplier-icon {
  transform: scale(1.1);
}

.member-type-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  padding: 1px 3px;
  color: white;
  font-weight: bold;
  z-index: 1;
  text-align: center;
  min-width: 16px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

.vip-badge {
  background: linear-gradient(to right, #000000, #8e7632);
}

.normal-badge {
  background-color: #3498db;
}

/* 操作栏样式 */
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.member-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.likes-count-btn {
  margin-right: auto;
  display: flex;
  align-items: center;
}

.likes-count-text {
  color: #1890ff;
  font-size: 14px;
  font-weight: 500;
}

.expand-btn {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.expand-btn:hover {
  background-color: #f5f5f5;
}

.like-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.2s;
}

.like-btn:hover {
  background-color: #f0f8ff;
}

.thumbs-up-icon {
  stroke: #1890ff;
}

.thumbs-up-icon.liked {
  fill: #1890ff;
  stroke: #1890ff;
}

/* 点赞用户列表样式 */
.likes-container {
  background-color: #fff;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.likes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.likes-count {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.collapse-btn {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.likes-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
}

.like-user {
  display: flex;
  justify-content: center;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

@media (max-width: 600px) {
  .likes-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 400px) {
  .likes-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 会员信息弹出框 - 外部版本 */
.member-info-popup-external {
  position: absolute;
  z-index: 1000;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));
  pointer-events: auto;
}

.member-info-popup-external::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  z-index: 1001;
}

.member-info-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.member-header {
  background-color: #f8f8f8;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.member-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-right: 8px;
}

.member-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
}

.company-name {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-contact-list {
  padding: 12px 16px;
  background-color: white;
}

.contact-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
}

.contact-row:last-child {
  margin-bottom: 0;
}

.contact-label {
  color: #666;
  width: 60px;
  flex-shrink: 0;
}

.contact-value {
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-btn {
  background: none;
  border: none;
  color: #1890ff;
  font-size: 12px;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.qq-btn {
  color: #12B7F5;
}

.contact-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.contact-btn:not(:disabled):hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.member-actions {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #e8e8e8;
  justify-content: space-between;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  flex: 1;
}

.business-intro-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 8px;
}

.business-intro-btn:hover {
  background-color: #e8e8e8;
}

.contact-now-btn {
  background-color: #1890ff;
  color: white;
}

.contact-now-btn:hover {
  background-color: #40a9ff;
}

.contact-button {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
}

.contact-button:hover {
  background: #40a9ff;
}

/* 添加关闭按钮样式 */
.close-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
}

.close-btn:hover {
  color: #666;
}
</style>
