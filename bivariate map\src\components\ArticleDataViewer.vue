<template>
  <div class="article-data-viewer">
    <div class="preview-actions">
      <button class="preview-button" @click="previewData">
        <span class="icon">👁️</span>
        预览文章
      </button>
      <button class="copy-button" @click="copyData">
        <span class="icon">📋</span>
        复制数据
      </button>
    </div>
    
    <div class="article-data">
      <h2 class="article-title">{{ articleData.title || '无标题' }}</h2>
      
      <div class="data-grid">
        <div class="data-item">
          <div class="label">作者</div>
          <div class="value">{{ articleData.username || '未知' }}</div>
        </div>
        <div class="data-item">
          <div class="label">会员等级</div>
          <div class="value">{{ articleData.memberType || '普通会员' }}</div>
        </div>
        <div class="data-item">
          <div class="label">发布时间</div>
          <div class="value">{{ formatTime(articleData.createTime) }}</div>
        </div>
        <div class="data-item">
          <div class="label">标签</div>
          <div class="value">
            <span v-for="tag in articleData.tags" :key="tag.tid" class="tag">{{ tag.name }}</span>
          </div>
        </div>
        <div class="data-item">
          <div class="label">公司</div>
          <div class="value">{{ articleData.companyName || '未填写' }}</div>
        </div>
        <div class="data-item">
          <div class="label">职位</div>
          <div class="value">{{ articleData.post || '未填写' }}</div>
        </div>
        <div class="data-item full-width">
          <div class="label">业务介绍</div>
          <div class="value">{{ articleData.businessIntroduction || '未填写' }}</div>
        </div>
        <div class="data-item full-width">
          <div class="label">内容</div>
          <div class="value content-text">{{ articleData.content || '无内容' }}</div>
        </div>
      </div>
      
      <div v-if="articleData.articleImgList && articleData.articleImgList.length > 0" class="image-section">
        <h3>图片附件 ({{ articleData.articleImgList.length }}张)</h3>
        <div class="image-grid">
          <div v-for="img in articleData.articleImgList" :key="img.id" class="image-item">
            <img :src="getImageUrl(img.imgUrl)" :alt="'图片' + img.id" class="preview-image">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { previewArticle } from '../utils/articleUtils';
import imageCache from '../utils/imageCache';

const props = defineProps({
  articleData: {
    type: Object,
    required: true
  }
});

const router = useRouter();
const baseUrl = import.meta.env.VITE_BASE_API || '';

// 获取图片URL
const getImageUrl = (imgUrl) => {
  return imageCache.getImageUrl(imgUrl, baseUrl);
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '未知时间';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 预览文章
const previewData = () => {
  previewArticle(props.articleData);
};

// 复制数据到剪贴板
const copyData = () => {
  try {
    const dataStr = JSON.stringify(props.articleData, null, 2);
    navigator.clipboard.writeText(dataStr)
      .then(() => {
        ElMessage.success('文章数据已复制到剪贴板');
      })
      .catch((err) => {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制');
      });
  } catch (error) {
    console.error('复制时出错:', error);
    ElMessage.error('复制失败，请手动复制');
  }
};

onMounted(() => {
  console.log('ArticleDataViewer组件已加载:', props.articleData);
});
</script>

<style scoped>
.article-data-viewer {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.preview-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.preview-button,
.copy-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.preview-button {
  background-color: #6b7afc;
  color: white;
}

.preview-button:hover {
  background-color: #5c6bc0;
}

.copy-button {
  background-color: #f0f2f5;
  color: #333;
}

.copy-button:hover {
  background-color: #e0e4e9;
}

.icon {
  font-size: 16px;
}

.article-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.data-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.full-width {
  grid-column: 1 / -1;
}

.label {
  font-size: 12px;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
  word-break: break-word;
}

.content-text {
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fb;
  border-radius: 4px;
}

.tag {
  display: inline-block;
  font-size: 12px;
  background-color: #f0f2f5;
  color: #5c6bc0;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: 8px;
  margin-bottom: 4px;
}

.image-section {
  margin-top: 20px;
}

.image-section h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.image-item {
  border-radius: 4px;
  overflow: hidden;
  aspect-ratio: 1/1;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-actions {
    flex-direction: column;
  }
}
</style> 