<template>
  <div class="article-card-container">
    <div class="article-card">
      <!-- 添加置顶标签插槽 -->
      <slot name="topBadge"></slot>
      
      <div :class="['card-image', imageAspectRatioClass]" @click="viewArticleDetail">
        <img v-if="hasImage" :src="getImageUrl(article.articleImgList[0].imgUrl)" :alt="t('plaza.articleImage')" class="card-thumbnail" @load="onImageLoad">
        <div v-else class="card-no-image">
          <div class="card-no-image-text" :title="article.content">{{ article.content || article.title || t('plaza.noTitle') }}</div>
        </div>
      </div>
      <div class="card-content">
        <h3 class="card-title" @click="handleTitleClick">
          <span class="title-text">{{ truncatedTitle }}</span>
        </h3>
        <div class="card-user" @click.stop="showUserInfo">
          <div class="card-avatar-container">
            <img :src="getAvatarUrl(article.avatar)" :alt="t('plaza.userAvatar')" class="card-avatar">
            <!-- 根据会员等级显示VIP徽章 -->
            <div v-if="article.memberType === '普通会员'" class="vip-badge vip-regular">
              VIP
            </div>
            <div v-else-if="article.memberType === '超级会员'" class="vip-badge vip-super">
              SVIP
            </div>
          </div>
          <span class="card-username">{{ article.nickName || t('plaza.user') }}</span>
        </div>
        <div class="card-tags" v-if="article.tagList && article.tagList.length > 0" @click="viewArticleDetail">
          <span v-for="(tag, index) in article.tagList.slice(0, 5)" :key="index" class="tag">{{ tag.name }}</span>
          <span v-if="article.tagList.length > 5" class="tag-more">+{{ article.tagList.length - 5 }}</span>
        </div>
        <div class="card-meta">
          <span class="card-time" @click="viewArticleDetail">{{ formatTime(article.createTime) }}</span>
          <div class="card-actions" @click.stop>
            <button class="like-button" :class="{ 'liked': isLiked }" @click="handleLike">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
              <span>{{ likeCount }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户信息弹窗 - 移到外部，避免嵌套关系导致z-index问题 -->
    <div v-if="showUserInfoModal" class="user-info-modal" @click="closeUserInfo">
      <div class="user-info-card" @click.stop>
        <button class="close-button" @click="closeUserInfo">&times;</button>
        <div class="user-info-content-new">
          <!-- 用户信息区域重新布局：头像在左侧，信息在右侧 -->
          <div class="user-info-layout">
            <!-- 用户头像 -->
            <div class="user-avatar-container">
              <img :src="getAvatarUrl(article.avatar)" :alt="t('plaza.userAvatar')" class="user-avatar-new">
              <!-- 根据会员等级显示VIP徽章 -->
              <div v-if="article.memberType === '普通会员'" class="vip-badge vip-regular">
                VIP
              </div>
              <div v-else-if="article.memberType === '超级会员'" class="vip-badge vip-super">
                SVIP
              </div>
            </div>
            
            <!-- 用户基本信息 -->
            <div class="user-info-side">
              <!-- 名字和职位行 -->
              <div class="info-row">
                <span class="user-name-new">{{ article.nickName || article.username || t('plaza.anonymous') }}</span>
                <span class="post-info" style="margin-left: -5rem;">{{ t('plaza.supplierInfo.position') }}: {{ article.post || t('plaza.notProvided') }}</span>
              </div>
              <div class="business-intro" :title="article.introduction">{{ t('plaza.supplierInfo.businessScope') }}：{{ truncatedBusinessIntro }}</div>
            </div>
          </div>
          
          <div class="user-info-rows">
            <!-- 公司行 -->
            <div class="info-row">
              <span class="info-label">{{ t('plaza.supplierInfo.companyName') }}:&nbsp;&nbsp;{{ article.companyName || t('plaza.notProvided') }}</span>
            </div>  
            
            <!-- 电话行 -->
            <div class="info-row">
              <span class="info-label">{{ t('plaza.supplierInfo.phone') }}:&nbsp;&nbsp;{{ article.phone || t('plaza.notProvided') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import imageCache from '../utils/imageCache';
import { addArticleLike, deleteArticleLike } from '../api/article';
import { useUserStore } from '../store/user';
import { ElMessage } from 'element-plus';
import { useI18n } from '../composables/useI18n';

const { t } = useI18n();
const userStore = useUserStore();
const props = defineProps({
  article: {
    type: Object,
    required: true,
    default: () => ({
      title: '无标题',
      createTime: new Date().toISOString(),
      viewCount: 0,
      articleImgList: [],
      tagList: []
    })
  }
});

const router = useRouter();
const defaultAvatar = '/image/default-avatar.png';
const showUserInfoModal = ref(false);
const baseUrl = import.meta.env.VITE_BASE_API || '';
const isLiked = ref(props.article.liked || false);
const likeCount = ref(props.article.likeCount || 0);

// 计算属性：是否置顶
const isTopPost = computed(() => {
  return Number(props.article.isTop) === 1;
});

// 计算属性：截断的标题
const truncatedTitle = computed(() => {
  if (!props.article.title) return t('plaza.noTitle');
  return props.article.title.length > 20 
    ? props.article.title.substring(0, 20) + '...' 
    : props.article.title;
});

// 计算属性：截断的公司简介
const truncatedBusinessIntro = computed(() => {
  if (!props.article.introduction) return '';
  return props.article.introduction.length > 10 
    ? props.article.introduction.substring(0, 10) + '...' 
    : props.article.introduction;
});

// 组件挂载时检查状态
onMounted(() => {
  // 初始化点赞状态
  if (props.article.liked) {
    isLiked.value = true;
  }
});

// 判断是否有图片
const hasImage = computed(() => {
  return props.article.articleImgList && 
         props.article.articleImgList.length > 0 && 
         props.article.articleImgList[0].imgUrl;
});

// 获取会员类型名称
const getMemberTypeName = (type) => {
  const memberTypes = {
    1: '普通会员',
    2: '高级会员',
    3: 'VIP会员',
    4: '钻石会员',
    5: '至尊会员',
    6: '超级会员'
  };
  return memberTypes[type] || `${type}`;
};

// 显示用户信息
const showUserInfo = () => {
  showUserInfoModal.value = true;
};

// 关闭用户信息
const closeUserInfo = () => {
  showUserInfoModal.value = false;
};

// 获取头像URL的函数 - 使用缓存服务
const getAvatarUrl = (avatarUrl) => {
  return imageCache.getAvatarUrl(avatarUrl, baseUrl);
};

// 获取图片URL的函数 - 使用缓存服务
const getImageUrl = (imgUrl) => {
  return imageCache.getImageUrl(imgUrl, baseUrl);
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return t('plaza.unknownTime');

  try {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return t('plaza.dateFormat', {
      year,
      month,
      day,
      hours,
      minutes
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return t('plaza.dateFormatError');
  }
};

// 查看文章详情
const viewArticleDetail = () => {
  // 确保点击事件在最高层级
  setTimeout(() => {
    router.push({
      path: `/article-detail/${props.article.id}`,
      query: {
        title: props.article.title,
        createTime: props.article.createTime
      }
    });
  }, 10);
};

// 处理点赞和取消点赞
const handleLike = async () => {
  if (!userStore.isLogin) {
    ElMessage.warning(t('plaza.loginRequired'));
    userStore.showLoginDialog = true;
    return;
  }
  
  try {
    if (!isLiked.value) {
      // 添加点赞
      const response = await addArticleLike(props.article.id);
      if (response.code === 200) {
        isLiked.value = true;
        // 直接修改props是不推荐的，但在这种情况下可以确保状态一致
        props.article.liked = true;
        likeCount.value += 1;
        props.article.likeCount = likeCount.value;
        ElMessage.success(t('plaza.likeSuccess'));
      } else {
        ElMessage.error(response.msg || t('plaza.likeFailed'));
      }
    } else {
      // 取消点赞
      const response = await deleteArticleLike(props.article.id);
      if (response.code === 200) {
        isLiked.value = false;
        // 直接修改props是不推荐的，但在这种情况下可以确保状态一致
        props.article.liked = false;
        likeCount.value = Math.max(0, likeCount.value - 1);
        props.article.likeCount = likeCount.value;
        ElMessage.success(t('plaza.unlikeSuccess'));
      } else {
        ElMessage.error(response.msg || t('plaza.unlikeFailed'));
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error(t('plaza.networkError'));
  }
};

// 处理标题点击事件
const handleTitleClick = (event) => {
  // 阻止默认行为和冒泡
  event.preventDefault();
  viewArticleDetail();
};

// 存储图片宽高比类型
const imageRatioClass = ref('');

// 添加图片加载后的处理逻辑
const onImageLoad = (event) => {
  // 不再根据宽高比应用不同样式，所有图片使用统一高度
  imageRatioClass.value = '';
  
  // 获取卡片元素
  const card = event.target.closest('.article-card');
  
  // 如果存在长图类，移除它
  if (card) card.classList.remove('has-tall-image');
};

// 计算图片宽高比类名
const imageAspectRatioClass = computed(() => {
  return imageRatioClass.value;
});
</script>

<style scoped>
.article-card-container {
  position: relative;
}

.article-card {
  background: white;
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 380px; /* 固定高度 */
  position: relative;
  margin-bottom: 10px;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-image {
  position: relative;
  width: 100%;
  height: 180px; /* 固定高度，所有图片统一 */
  overflow: hidden;
  background-color: #f5f7fa;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 确保图片填满容器并裁剪多余部分 */
  transition: transform 0.3s ease;
}

/* 统一所有图片居中显示 */
.card-thumbnail {
  object-position: center;
}

.card-no-image {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  color: #666;
  font-size: 15px; /* 增加字体大小 */
  text-align: center; /* 居中对齐 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
  padding: 20px 25px; /* 左右增加内边距 */
  box-sizing: border-box; /* 确保padding不会增加元素尺寸 */
}

.card-no-image-text {
  display: -webkit-box;
  -webkit-line-clamp: 5; /* 显示5行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word; /* 使用break-word替代break-all */
  line-height: 1.6; /* 增加行高 */
  width: 100%;
  max-height: 8em; /* 5行文字的高度 */
  position: relative;
  padding: 0 15px; /* 左右添加更大的内边距 */
  text-align: center; /* 文本居中显示 */
  margin: 0 auto; /* 水平居中 */
  color: #555; /* 稍微深一点的颜色 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.card-content {
  padding: 12px 10px; /* 水平方向减少，垂直方向适中 */
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: visible; /* 允许内容溢出，避免被截断 */
  position: relative; /* 为子元素定位提供参考 */
}

.card-title {
  margin: 0 0 12px 0;
  position: relative;
  z-index: 30; /* 确保标题在最上层 */
  width: 100%; /* 确保标题宽度不超过容器 */
  box-sizing: border-box; /* 考虑padding在内的宽度计算 */
}

/* 全局清除底部横线 */
h3.card-title {
  text-decoration: none;
  border-bottom: none;
}

h3.card-title::after {
  display: none;
}

/* 清除点击元素可能出现的底部横线 */
.article-card *:focus {
  outline: none;
  text-decoration: none;
  border-bottom: none;
}

.article-card *:hover {
  text-decoration: none;
  border-bottom: none;
}

/* 清除父元素可能继承的样式 */
.article-card {
  text-decoration: none;
}

.card-content * {
  text-decoration: none;
}

.title-text {
  font-size: 16px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  height: 2.8em;
  position: relative;
  cursor: pointer;
  text-decoration: none; /* 确保没有文本装饰 */
  border-bottom: none; /* 确保没有底部边框 */
}

.title-text:hover {
  color: #1890ff;
  text-decoration: none; /* 确保鼠标悬停时也没有文本装饰 */
  border-bottom: none;
}

.title-text[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  width: max-content;
  max-width: 300px;
  line-height: 1.5;
  white-space: normal;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.card-user {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  padding: 5px;
  border-radius: 20px;
  transition: background-color 0.2s;
  position: relative;
  z-index: 25;
}

.card-user:hover {
  background-color: #f0f2f5;
}

.card-avatar-container {
  position: relative;
  width: 40px;
  height: 40px;
  margin-right: 8px;
}

.card-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.card-username {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10px;
  z-index: 20;
  position: relative;
}

.card-tags {
  display: flex;
  gap: 5px;
  margin-bottom: 12px;
  flex-wrap: wrap;
  position: relative;
  z-index: 28; /* 确保标签在用户区域之上 */
  max-width: 100%;
  overflow: hidden;
}

.tag {
  padding: 3px 6px;
  border-radius: 10px;
  font-size: 11px;
  color: #1890ff;
  background: #e6f7ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90px;
}

.tag-more {
  font-size: 11px;
  color: #999;
  padding: 3px 0;
}

.card-meta {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-time {
  font-size: 12px;
  color: #999;
  cursor: pointer;
}

.card-views {
  font-size: 12px;
  color: #999;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.like-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 16px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
}

.like-button:hover {
  background-color: #f0f2f5;
  color: #1890ff;
}

.like-button.liked {
  color: #ff4d4f;
}

.like-button.liked svg {
  fill: #ff4d4f;
  stroke: #ff4d4f;
}

/* 用户信息弹窗样式 - 修改为固定定位，不再是卡片的子元素 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 0;
}

.user-info-card {
  background: white;
  border-radius: 12px;
  width: 507px; /* 再增加30%: 从390px增加到507px */
  max-width: 507px; /* 同样增加最大宽度 */
  overflow-y: auto;
  padding: 20px 20px 15px; /* 适当增加内边距 */
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 31;
}

.user-info-content-new {
  padding: 0;
}

.user-info-layout {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  gap: 25px;
}

.user-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar-new {
  width: 85px; /* 再增加30%: 从65px增加到85px */
  height: 85px; /* 再增加30%: 从65px增加到85px */
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.vip-badge {
  position: absolute;
  bottom: 0;
  right: -5px;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  border: none; /* 移除边框 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 20;
  line-height: 1;
  min-width: 18px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #fbbf24;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border: none; /* 移除金色边框 */
  animation: svip-glow 2s ease-in-out infinite alternate;
}

/* 文章卡片上的VIP徽章位置调整 */
.card-vip-badge {
  position: relative;
  bottom: auto;
  right: auto;
  margin-left: 5px;
  font-size: 0.6rem;
  height: 16px;
  min-width: 30px;
  border-radius: 8px;
  padding: 1px 4px;
  animation: none;
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.5); /* 修改为黑色阴影 */
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.5); /* 修改为黑色阴影 */
  }
}

.user-info-side {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-top: 7px;
}

.info-row {
  display: flex;
  align-items: center;
  position: relative; /* 为绝对定位提供参考 */
  font-size: 15px;
  width: 100%;
}

.user-name-new {
  font-size: 19px;
  font-weight: 500;
  color: #333;
}

.post-info {
  position: absolute;
  left: 80%; /* 从左侧80%的位置开始，相当于右侧20%的位置 */
  transform: translateX(-50%); /* 向左移动自身的50%以使文本居中于这个位置 */
  font-size: 15px;
  color: #666;
}

.business-intro {
  font-size: 16px;
  color: #333;
  margin-left: 0;
  position: relative;
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.user-info-rows {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-top: 1px solid #eee;
  padding-top: 15px;
  margin-top: 8px;
}

.info-label {
  color: #666;
  min-width: auto;
  font-size: 16px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  line-height: 1;
  font-weight: bold;
}

/* 更强烈的标题样式覆盖 */
.article-card .card-title {
  text-decoration: none !important;
  border-bottom: none !important;
}

.article-card .card-title span {
  text-decoration: none !important;
  border-bottom: none !important;
}

.article-card .card-title::after,
.article-card .card-title::before,
.article-card .title-text::after,
.article-card .title-text::before {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  background: none !important;
  display: none !important;
}

.article-card .title-text {
  text-decoration: none !important;
  border-bottom: none !important;
}

.article-card .title-text:hover {
  color: #1890ff;
  text-decoration: none !important;
  border-bottom: none !important;
}
</style> 