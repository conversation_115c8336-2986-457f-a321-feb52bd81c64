<template>
  <div class="share-buttons">
    <div class="share-title">分享至</div>
    <div class="buttons-container">
      <button class="share-button wechat" @click="shareToWechat">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9.5,4C5.36,4 2,6.69 2,10C2,11.89 3.08,13.56 4.78,14.66L4,17L6.5,15.5C7.39,15.81 8.37,16 9.41,16C9.15,15.37 9,14.7 9,14C9,10.69 12.13,8 16,8C16.19,8 16.38,8 16.56,8.03C15.54,5.69 12.78,4 9.5,4M6.5,6.5C7.05,6.5 7.5,6.95 7.5,7.5C7.5,8.05 7.05,8.5 6.5,8.5C5.95,8.5 5.5,8.05 5.5,7.5C5.5,6.95 5.95,6.5 6.5,6.5M11.5,6.5C12.05,6.5 12.5,6.95 12.5,7.5C12.5,8.05 12.05,8.5 11.5,8.5C10.95,8.5 10.5,8.05 10.5,7.5C10.5,6.95 10.95,6.5 11.5,6.5M16,9C12.69,9 10,11.24 10,14C10,16.76 12.69,19 16,19C16.67,19 17.31,18.88 17.91,18.66L20,20L19.38,18.18C20.82,17.13 22,15.7 22,14C22,11.24 19.31,9 16,9M14,11.5C14.55,11.5 15,11.95 15,12.5C15,13.05 14.55,13.5 14,13.5C13.45,13.5 13,13.05 13,12.5C13,11.95 13.45,11.5 14,11.5M18,11.5C18.55,11.5 19,11.95 19,12.5C19,13.05 18.55,13.5 18,13.5C17.45,13.5 17,13.05 17,12.5C17,11.95 17.45,11.5 18,11.5Z" />
        </svg>
        <span>微信</span>
      </button>
      <button class="share-button weibo" @click="shareToWeibo">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 1024 1024" fill="currentColor">
          <path d="M851.4 590.193c-22.196-66.233-90.385-90.422-105.912-91.863-15.523-1.442-29.593-9.94-19.295-27.505 10.302-17.566 29.304-68.684-7.248-104.681-36.564-36.14-124.181-0.816-146.365 0.816-22.38 1.587-54.516-10.591-38.644-53.107 0 0-0.312-3.597-0.312-3.597 26.415-83.312 11.322-160.939-53.382-160.939-64.68 0-148.069 83.977-238.108 220.45C142.4 510.344 67.81 683.755 67.81 683.755c-22.985 77.44 25.307 116.65 44.597 122.988 19.296 6.33 52.518 13.1 93.415-6.677 40.903-19.79 79.222-55.05 79.222-55.05l0.47 0.177c36.99-20.13 76.753-20.13 76.753-20.13 51.48 0.418 39.88 41.933 39.88 41.933-10.399 19.19-53.568 87.493-60.196 125.058-6.639 37.522 21.880 92.806 100.421 92.806 78.518 0 197.927-143.337 197.927-143.337 82.307-109.637 69.544-185.923 69.544-185.923 84.243 6.922 85.556 66.68 85.556 66.68 0.23 13.645 12.697 91.346 92.477 38.644 79.79-52.755 19.291-131.935 19.291-131.935l-0.055-0.148z" />
          <path d="M851.81 585.298c-22.097-65.996-90.025-90.134-105.539-91.565-15.499-1.442-29.527-9.91-19.26-27.435 10.287-17.545 29.258-68.506-7.219-104.412-36.498-36.027-123.811-0.816-146.074 0.816-22.328 1.587-54.399-10.57-38.561-52.984 0 0-0.312-3.597-0.312-3.597 26.337-83.081 11.322-160.487-53.198-160.487-64.506 0-147.875 83.747-237.384 219.876-89.516 136.14-164.078 308.828-164.078 308.828-22.91 77.21 25.232 116.303 44.474 122.603 19.223 6.3 52.341 13.046 93.15-6.637 40.796-19.708 79.015-54.888 79.015-54.888l0.47 0.177c36.876-20.093 76.521-20.093 76.521-20.093 51.308 0.407 39.749 41.817 39.749 41.817-10.369 19.143-53.43 87.258-60.033 124.738-6.639 37.402 21.816 92.517 100.169 92.517 78.307 0 197.372-143.13 197.372-143.13 82.096-109.335 69.376-185.346 69.376-185.346 84.054 6.852 85.328 66.504 85.328 66.504 0.23 13.603 12.638 91.139 92.262 38.541 79.607-52.635 19.223-131.559 19.223-131.559l-0.055-0.118z" />
          <path d="M602.034 726.548a32.079 32.079 0 0 0-14.677 32.653c2.655 17.935 20.805 30.215 40.528 27.377 19.730-2.844 33.48-19.790 30.807-37.73-2.629-17.92-20.79-30.215-40.483-27.377a36.224 36.224 0 0 0-16.175 5.077zM596.83 785.55c-6.258 10.224-19.79 15.297-30.136 11.252-10.164-3.94-13.307-15.408-7.096-25.265 6.115-9.835 19.374-14.814 29.503-11.104 10.303 3.78 13.739 15.133 7.729 25.117z" />
          <path d="M591.051 844.171c-64.703 3.021-115.673-27.377-114.566-67.997 1.1-40.666 55.677-79.401 120.392-82.393 64.708-3.021 115.703 27.377 114.566 67.956-1.106 40.62-55.712 79.442-120.392 82.435z" />
        </svg>
        <span>微博</span>
      </button>
      <button class="share-button qq" @click="shareToQQ">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12.005 22C10.325 22 8.88 21.745 7.67 21.235C7.205 21.41 6.59 21.67 5.93 21.955C4.99 22.345 3.935 22 4.005 21.415C4.06 20.94 4.205 20.405 4.395 19.92C3.165 18.835 2.555 17.355 2.555 15.5C2.555 11.41 6.79 8.105 12.005 8.105C17.22 8.105 21.455 11.41 21.455 15.5C21.455 19.59 17.22 22.895 12.005 22.895C11.87 22.895 11.74 22.89 11.605 22.88C11.535 22.87 11.35 22.835 11.675 22.58C11.905 22.4 12.005 22.195 12.005 22ZM12 2C9.715 2 7.845 3.84 7.845 6.095C7.845 8.35 9.715 10.19 12 10.19C14.285 10.19 16.155 8.35 16.155 6.095C16.155 3.84 14.285 2 12 2Z" />
        </svg>
        <span>QQ</span>
      </button>
      <button class="share-button link" @click="copyLink">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
        </svg>
        <span>复制链接</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  url: {
    type: String,
    default: () => window.location.href
  },
  summary: {
    type: String,
    default: ''
  },
  coverImage: {
    type: String,
    default: ''
  }
});

// 分享到微信
const shareToWechat = () => {
  ElMessage({
    message: '请打开微信APP，使用"扫一扫"扫描二维码进行分享',
    type: 'info',
    duration: 3000
  });
  
  // 这里可以实现弹出二维码的逻辑
  // 由于微信分享需要调用原生API，这里仅做示例提示
};

// 分享到微博
const shareToWeibo = () => {
  const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(props.url)}&title=${encodeURIComponent(props.title)}&pic=${encodeURIComponent(props.coverImage)}`;
  window.open(weiboUrl, '_blank', 'width=700,height=500');
};

// 分享到QQ
const shareToQQ = () => {
  const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(props.url)}&title=${encodeURIComponent(props.title)}&desc=${encodeURIComponent(props.summary)}&pics=${encodeURIComponent(props.coverImage)}`;
  window.open(qqUrl, '_blank', 'width=700,height=500');
};

// 复制链接
const copyLink = () => {
  try {
    navigator.clipboard.writeText(props.url).then(() => {
      ElMessage({
        message: '链接已复制到剪贴板',
        type: 'success',
        duration: 2000
      });
    });
  } catch (err) {
    // 如果浏览器不支持clipboard API，使用传统方法
    const textarea = document.createElement('textarea');
    textarea.value = props.url;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    
    try {
      document.execCommand('copy');
      ElMessage({
        message: '链接已复制到剪贴板',
        type: 'success',
        duration: 2000
      });
    } catch (e) {
      ElMessage({
        message: '复制失败，请手动复制',
        type: 'error',
        duration: 2000
      });
    }
    
    document.body.removeChild(textarea);
  }
};
</script>

<style scoped>
.share-buttons {
  margin-top: 20px;
}

.share-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.buttons-container {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
}

.share-button svg {
  width: 18px;
  height: 18px;
}

.wechat {
  background-color: #07C160;
}

.wechat:hover {
  background-color: #06b057;
}

.weibo {
  background-color: #E6162D;
}

.weibo:hover {
  background-color: #d41429;
}

.qq {
  background-color: #12B7F5;
}

.qq:hover {
  background-color: #10a6dc;
}

.link {
  background-color: #6E6E6E;
}

.link:hover {
  background-color: #5a5a5a;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .share-button {
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style> 