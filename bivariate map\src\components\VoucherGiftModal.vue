<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="isVisible" class="modal-overlay" @click="closeOnOverlayClick ? close() : null">
        <div class="modal-container" @click.stop>
          <!-- 礼盒顶部 -->
          <div class="gift-box-top" :class="{ 'open': isOpen }">
            <div class="ribbon"></div>
          </div>
          
          <!-- 礼盒内容 -->
          <div class="modal-content" :class="{ 'show': isOpen }">
            <!-- 标题和关闭按钮 -->
            <div class="modal-header">
              <button class="close-button" @click="close">
                <i-lucide-x class="close-icon" />
              </button>
            </div>
            
            <!-- 主要内容 -->
            <div class="modal-body">
              <div class="gift-title">
                <span class="gift-icon">🎁</span>
                <h2>送您一张会员抵值券</h2>
                <span class="gift-icon">🎁</span>
              </div>
              
              <div class="voucher-container">
                <div class="voucher-card">
                  <div class="voucher-left">
                    <div class="voucher-amount">
                      <span class="currency">¥</span>
                      <span class="value">{{ voucher.amount.toFixed(2) }}</span>
                    </div>
                    <div class="voucher-threshold" v-if="voucher.threshold > 0">
                      满{{ voucher.threshold }}元可用
                    </div>
                  </div>
                  
                  <div class="voucher-divider">
                    <div class="circle top"></div>
                    <div class="dashed-line"></div>
                    <div class="circle bottom"></div>
                  </div>
                  
                  <div class="voucher-right">
                    <div class="voucher-name">{{ voucher.name }}</div>
                    <div class="voucher-validity">
                      <div class="validity-item">
                        <i-lucide-calendar class="icon" />
                        <span>有效期至: {{ formatDate(voucher.expiryDate) }}</span>
                      </div>
                      <div class="validity-item">
                        <i-lucide-clock class="icon" />
                        <span>剩余 {{ voucher.remainingDays }} 天</span>
                      </div>
                    </div>
                    <div class="voucher-description">
                      {{ voucher.description }}
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="congratulation-text">
                <p>恭喜您获得此专属优惠！</p>
                <p class="small-text">抵值券已自动添加到您的账户</p>
              </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="modal-footer">
              <button class="btn-accept" @click="acceptVoucher">
                <span>立即使用</span>
              </button>
              <button class="btn-later" @click="close">
                <span>稍后查看</span>
              </button>
            </div>
          </div>
          
          <!-- 礼盒底部 -->
          <div class="gift-box-bottom"></div>
          
          <!-- 礼花效果 -->
          <div class="confetti-container" v-if="showConfetti">
            <div v-for="n in 50" :key="n" class="confetti" :style="getConfettiStyle(n)"></div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue' // 添加 watch 导入
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'

const router = useRouter()
const store = useUserStore()

// 定义组件的属性
const props = defineProps({
  // 是否显示弹窗
  show: {
    type: Boolean,
    default: true
  },
  // 点击遮罩是否关闭弹窗
  closeOnOverlayClick: {
    type: Boolean,
    default: true
  },
  // 抵值券数据
  voucherData: {
    type: Object,
    default: () => ({
      id: 'VIP2023',
      name: 'VIP会员专享券',
      amount: 100,
      // threshold: 500,
      expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后过期
      remainingDays: 30,
      description: '适用于全部商品，节假日通用，不可与其他优惠同时使用'
    })
  }
})

// 定义事件
const emit = defineEmits(['close', 'accept'])

// 内部状态
const isVisible = ref(props.show)
const isOpen = ref(false)
const showConfetti = ref(false)

// 监听show属性变化
watch(() => props.show, (newVal) => {
  isVisible.value = newVal
  if (newVal) {
    setTimeout(() => {
      isOpen.value = true
      setTimeout(() => {
        showConfetti.value = true
      }, 500)
    }, 300)
  } else {
    isOpen.value = false
    showConfetti.value = false
  }
})

// 修改计算属性：处理后端返回的抵值券数据格式
const voucher = computed(() => {
  // 如果使用默认数据
  if (!props.voucherData || !props.voucherData.data) {
    return {
      name: 'VIP会员专享券',
      amount: 0,
      expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      remainingDays: 30,
      description: '适用于全部商品，节假日通用，不可与其他优惠同时使用'
    }
  }

  // 处理后端返回的数据
  const data = props.voucherData.data
  const expiryDate = new Date(Date.now() + data.有效期 * 24 * 60 * 60 * 1000)
  
  return {
    name: 'VIP会员专享券',
    amount: data.金额,
    expiryDate: expiryDate,
    remainingDays: data.有效期,
    description: `${data.message || '发放成功'}，有效期${data.有效期}天`
  }
})

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 生成随机礼花样式
const getConfettiStyle = (index) => {
  const colors = ['#ff6b6b', '#ffb347', '#4ecdc4', '#45aaf2', '#a55eea', '#fed330']
  const size = Math.floor(Math.random() * 10) + 5 // 5px - 15px
  const left = Math.floor(Math.random() * 100) // 0% - 100%
  const animationDelay = Math.random() * 3 // 0s - 3s
  const animationDuration = Math.random() * 2 + 3 // 3s - 5s
  
  return {
    backgroundColor: colors[index % colors.length],
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  }
}

// 关闭弹窗
const close = () => {
  isOpen.value = false
  showConfetti.value = false
  
  setTimeout(() => {
    isVisible.value = false
    store.closeVoucherModal() // 调用store的关闭方法
    emit('close')
  }, 300)
}

// 接受抵值券
const acceptVoucher = () => {
  emit('accept', voucher.value)
  handleUse()
  close()
}

// 跳转到会员页面
const handleUse = () => {
  store.showVoucherModal = false
  router.push('/member')
}

// 组件挂载时
onMounted(() => {
  if (props.show) {
    setTimeout(() => {
      isOpen.value = true
      setTimeout(() => {
        showConfetti.value = true
      }, 500)
    }, 300)
  }
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  position: relative;
  width: 100%;
  max-width: 480px;
  perspective: 1000px;
}

/* 礼盒样式 */
.gift-box-top {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%) rotateX(0deg);
  width: 100%;
  height: 40px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 10px 10px 0 0;
  transform-origin: bottom;
  transition: transform 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  z-index: 2;
}

.gift-box-top.open {
  transform: translateX(-50%) rotateX(-110deg);
}

.ribbon {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 60px;
  background: #ffb347;
  border-radius: 15px;
  z-index: 1;
}

.ribbon::before, .ribbon::after {
  content: '';
  position: absolute;
  top: 0;
  width: 20px;
  height: 30px;
  background: #ffb347;
  border-radius: 50%;
}

.ribbon::before {
  left: -10px;
}

.ribbon::after {
  right: -10px;
}

.gift-box-bottom {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 0 0 10px 10px;
  z-index: 0;
}

/* 弹窗内容样式 */
.modal-content {
  position: relative;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  z-index: 1;
}

.modal-content.show {
  opacity: 1;
  transform: translateY(0);
}

.modal-header {
  position: relative;
  padding: 16px;
  text-align: right;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.close-icon {
  width: 18px;
  height: 18px;
  color: #64748b;
}

.modal-body {
  padding: 0 24px 24px;
}

.gift-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.gift-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 12px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 2s infinite;
}

.gift-icon {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* 抵值券样式 */
.voucher-container {
  margin-bottom: 24px;
}

.voucher-card {
  display: flex;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  transform: scale(1);
}

.voucher-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.voucher-left {
  width: 120px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.voucher-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.voucher-amount {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.currency {
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 4px;
}

.value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.voucher-threshold {
  font-size: 0.8rem;
  opacity: 0.9;
  background: rgba(0, 0, 0, 0.2);
  padding: 3px 8px;
  border-radius: 10px;
  margin-top: 5px;
}

.voucher-divider {
  position: relative;
  width: 20px;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.circle {
  width: 20px;
  height: 20px;
  background: #f5f7fa;
  border-radius: 50%;
  position: absolute;
}

.circle.top {
  top: -10px;
}

.circle.bottom {
  bottom: -10px;
}

.dashed-line {
  flex: 1;
  width: 0;
  border-left: 2px dashed #e2e8f0;
  margin: 10px 0;
}

.voucher-right {
  flex: 1;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.voucher-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

.voucher-validity {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.validity-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #64748b;
}

.validity-item .icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  color: #94a3b8;
}

.voucher-description {
  font-size: 0.8rem;
  color: #94a3b8;
  line-height: 1.4;
  padding-top: 8px;
  border-top: 1px dashed #e2e8f0;
}

.congratulation-text {
  text-align: center;
  margin-bottom: 24px;
}

.congratulation-text p {
  margin: 5px 0;
  color: #1e293b;
  font-weight: 500;
}

.congratulation-text .small-text {
  font-size: 0.85rem;
  color: #64748b;
  font-weight: normal;
}

.modal-footer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 24px 24px;
}

.btn-accept, .btn-later {
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
}

.btn-accept {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
  box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);
}

.btn-accept:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 107, 107, 0.4);
}

.btn-later {
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-later:hover {
  background: #f8fafc;
}

/* 礼花效果 */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.confetti {
  position: absolute;
  top: -10px;
  border-radius: 50%;
  animation: fall linear forwards;
}

@keyframes fall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* 弹窗动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .modal-container {
    max-width: 100%;
  }
  
  .voucher-card {
    flex-direction: column;
  }
  
  .voucher-left {
    width: 100%;
    padding: 15px;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .voucher-divider {
    display: none;
  }
  
  .gift-title h2 {
    font-size: 1.3rem;
  }
}
</style>