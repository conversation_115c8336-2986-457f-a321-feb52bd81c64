/* 图标修复样式 */
svg.icon-sm {
  width: 16px;
  height: 16px;
  stroke-width: 2;
  stroke: currentColor;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  display: inline-block;
  vertical-align: middle;
}

.action-btn svg.icon-sm {
  width: 18px;
  height: 18px;
}

/* 为图标按钮添加文字标签 */
.action-btn {
  position: relative;
  overflow: visible;
}

.icon-text {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.action-btn:hover .icon-text {
  opacity: 1;
}

/* 修复文章卡片中的操作按钮 */
.article-actions-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.article-card:hover .article-actions-overlay {
  opacity: 1;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.edit-btn {
  color: #0ea5e9;
}

.edit-btn:hover {
  background-color: #0ea5e9;
  color: white;
}

.delete-btn {
  color: #ef4444;
}

.delete-btn:hover {
  background-color: #ef4444;
  color: white;
}

/* 修复图标统计区域 */
.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  font-size: 0.75rem;
}

.stat svg {
  margin-right: 2px;
}

/* 修复文章作者头像 */
.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* 修复文章图片容器 */
.article-image-container {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.article-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

/* 暗色模式样式调整 */
@media (prefers-color-scheme: dark) {
  .action-btn {
    background-color: rgba(30, 30, 30, 0.9);
  }
  
  .stat {
    color: #94a3b8;
  }
} 