import request from '../utils/request'

export function getMaprouter(data) {
    return request({
        url: '/route/list',
        method: 'get',
       params:{endStationNameEn:data.endStationNameEn, startStationNameZh:data.startStationNameZh,startStationNameEn:data.startStationNameEn,endStationNameZh:data.endStationNameZh}
        
    })
}
export function listStation(query){
return request({
    url:'/station/list',
    method:'get',
    params: query
})
}