<template>
  <div>
    <!-- 添加全局遮罩层 -->
    <div v-if="isVisible" class="global-overlay"></div>

    <!-- 原有的登录模态框 -->
    <div v-if="isVisible" class="modal-overlay">
      <div
        class="modal-container"
        @click.stop
        :style="modalStyle"
        :class="{ 'modal-entering': isEntering, 'modal-leaving': isLeaving }"
      >
        <!-- 关闭按钮 -->
        <button class="close-button" @click="closeModal">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>

        <!-- 登录表单 -->
        <div v-if="currentView === 'login'" class="modal-content">
          <h2 class="modal-title">{{ t('login.title') }}</h2>

          <!-- 登录方式切换 -->
          <div class="login-type-switch">
            <button
              :class="['switch-btn', { active: loginType === 'code' }]"
              @click="loginType = 'code'"
            >
              {{ t('login.codeLogin') }}
            </button>
            <button
              :class="['switch-btn', { active: loginType === 'password' }]"
              @click="loginType = 'password'"
            >
              {{ t('login.passwordLogin') }}
            </button>
          </div>

          <!-- 手机号输入框 -->
          <div class="form-group">
            <label for="username">{{ t('login.phoneNumber') }}</label>
            <input
              type="tel"
              id="username"
              v-model="loginForm.username"
              :placeholder="t('login.enterPhoneNumber')"
              class="form-input"
              autocomplete="tel"
              inputmode="tel"
            />
          </div>

          <!-- 验证码登录表单 -->
          <template v-if="loginType === 'code'">
            <div class="form-group">
              <label for="verificationCode">{{ t('login.verificationCode') }}</label>
              <div class="verification-code-container">
                <input
                  type="text"
                  id="verificationCode"
                  v-model="loginForm.verificationCode"
                  :placeholder="t('login.enterVerificationCode')"
                  class="form-input"
                  inputmode="numeric"
                  maxlength="6"
                  autocomplete="one-time-code"
                />
                <button
                  type="button"
                  class="send-code-button"
                  @click="sendLoginVerificationCode"
                  :disabled="cooldown > 0"
                >
                  {{ cooldown > 0 ? `${cooldown}${t('login.seconds')}` : t('login.getCode') }}
                </button>
              </div>
            </div>
          </template>

          <!-- 密码登录表单 -->
          <template v-else>
            <div class="form-group">
              <label for="password">{{ t('login.password') }}</label>
              <div class="password-input-container">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  id="password"
                  v-model="loginForm.password"
                  :placeholder="t('login.enterPassword')"
                  class="form-input"
                  autocomplete="current-password"
                />
                <button
                  type="button"
                  class="toggle-password"
                  @click="showPassword = !showPassword"
                >
                  <svg
                    v-if="!showPassword"
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"
                    ></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                    <path
                      d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
                    ></path>
                    <path
                      d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
                    ></path>
                    <line x1="2" x2="22" y1="2" y2="22"></line>
                  </svg>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label for="captcha">{{ t('login.captcha') }}</label>
              <div class="captcha-container">
                <input
                  type="text"
                  id="captcha"
                  v-model="loginForm.captcha"
                  :placeholder="t('login.enterCaptcha')"
                  class="form-input"
                  autocomplete="off"
                  maxlength="4"
                />
                <img
                  v-if="captchaImg"
                  :src="captchaImg"
                  class="captcha-img"
                  @click="refreshCaptcha"
                  :alt="t('login.refreshCaptcha')"
                />
              </div>
            </div>
          </template>

          <div class="form-options">
            
          </div>

          <div class="agreement-section">
            <div class="agreement-checkbox">
              <input
                type="checkbox"
                id="agreement"
                v-model="loginForm.agreement"
              />
              <label for="agreement">
                {{ t('login.agreementPrefix') }}
                <a href="#" @click.prevent="showAgreement('user')"
                  >{{ t('login.userAgreement') }}</a
                >
                {{ t('login.and') }}
                <a href="#" @click.prevent="showAgreement('privacy')"
                  >{{ t('login.privacyPolicy') }}</a
                >
              </label>
            </div>
          </div>

          <button
            class="primary-button"
            @click="handleLogin"
            :disabled="isLoading"
          >
            <span v-if="!isLoading">{{ t('login.login') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>

          <div class="alternative-options">
            <span class="divider">{{ t('login.otherLoginMethods') }}</span>
            <div class="login-methods">
              <button
                class="method-button phone"
                @click="switchToRegister('phone')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <span>{{ t('login.phoneVerification') }}</span>
              </button>
              <button
                class="method-button wechat"
                @click="switchToRegister('wechat')"
              >
                <img src="../assets/wechat.png" alt="微信" class="login-method-icon" />
                <span>{{ t('login.wechatScan') }}</span>
              </button>
            </div>
          </div>

          <div class="register-link">
            <span>{{ t('login.noAccount') }}</span>
            <a href="#" @click.prevent="switchToRegister('phone')">{{ t('login.register') }}</a>
          </div>
        </div>

        <!-- 注册表单 - 手机验证码 -->
        <div v-if="currentView === 'register-phone'" class="modal-content">
          <h2 class="modal-title">{{ t('register.phoneTitle') }}</h2>

          <div class="form-group">
            <label for="phone">{{ t('register.phoneNumber') }}</label>
            <input
              type="tel"
              id="phone"
              v-model="registerForm.phone"
              :placeholder="t('register.enterPhoneNumber')"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="verificationCode">{{ t('register.verificationCode') }}</label>
            <div class="verification-code-container">
              <input
                type="text"
                id="verificationCode"
                v-model="registerForm.verificationCode"
                :placeholder="t('register.enterVerificationCode')"
                class="form-input"
              />
              <button
                type="button"
                class="send-code-button"
                @click="sendVerificationCode"
                :disabled="cooldown > 0"
              >
                {{ cooldown > 0 ? `${cooldown}${t('register.secondsRemaining')}` : t('register.getCode') }}
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="register-password">{{ t('register.password') }}</label>
            <div class="password-input-container">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="register-password"
                v-model="registerForm.password"
                :placeholder="t('register.enterPassword')"
                class="form-input"
              />
              <button
                type="button"
                class="toggle-password"
                @click="showPassword = !showPassword"
              >
                <svg
                  v-if="!showPassword"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                  <path
                    d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
                  ></path>
                  <path
                    d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
                  ></path>
                  <line x1="2" x2="22" y1="2" y2="22"></line>
                </svg>
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="confirm-password">{{ t('register.confirmPassword') }}</label>
            <div class="password-input-container">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="confirm-password"
                v-model="registerForm.confirmPassword"
                :placeholder="t('register.enterPasswordAgain')"
                class="form-input"
              />
            </div>
          </div>

          <div class="agreement-section">
            <div class="agreement-checkbox">
              <input
                type="checkbox"
                id="register-agreement"
                v-model="registerForm.agreement"
              />
              <label for="register-agreement">
                {{ t('register.agreementPrefix') }}
                <a href="#" @click.prevent="showAgreement('user')"
                  >{{ t('register.userAgreement') }}</a
                >
                {{ t('register.and') }}
                <a href="#" @click.prevent="showAgreement('privacy')"
                  >{{ t('register.privacyPolicy') }}</a
                >
              </label>
            </div>
          </div>

          <button
            class="primary-button"
            @click="handleRegister"
            :disabled="isLoading"
          >
            <span v-if="!isLoading">{{ t('register.register') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>

          <div class="alternative-options">
            <span class="divider">{{ t('register.otherRegisterMethods') }}</span>
            <div class="login-methods">
              <button
                class="method-button wechat"
                @click="switchToRegister('wechat')"
              >
                <img src="../assets/wechat.png" alt="微信" class="login-method-icon" />
                <span>{{ t('register.wechatScan') }}</span>
              </button>
            </div>
          </div>

          <div class="login-link">
            <span>{{ t('register.alreadyHaveAccount') }}</span>
            <a href="#" @click.prevent="currentView = 'login'">{{ t('register.goToLogin') }}</a>
          </div>
        </div>

        <!-- 注册表单 - 微信扫码 -->
        <div v-if="currentView === 'register-wechat'" class="modal-content">
          <h2 class="modal-title">{{ t('wechat.scanTitle') }}</h2>

          <!-- 微信二维码部分的模板 -->
          <div class="qr-code-container">
            <div class="qr-code">
              <iframe
                v-if="qrUrl"
                :src="qrUrl"
                frameborder="0"
                class="wx-login-frame"
                @load="handleIframeLoad"
              ></iframe>
              <div v-else class="qr-loading">
                <svg class="loading-icon" viewBox="0 0 50 50">
                  <circle
                    cx="25"
                    cy="25"
                    r="20"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="5"
                  ></circle>
                </svg>
                <span>{{ t('wechat.loadingQrCode') }}</span>
              </div>
            </div>
            <p class="qr-instructions">{{ statusMsg }}</p>
          </div>

          <!-- 确保协议同意区域在二维码下方清晰可见 -->
          <div class="agreement-section">
            <div class="agreement-checkbox">
              <input
                type="checkbox"
                id="wechat-agreement"
                v-model="registerForm.agreement"
              />
              <label for="wechat-agreement">
                {{ t('wechat.agreementPrefix') }}
                <a href="#" @click.prevent="showAgreement('user')"
                  >{{ t('wechat.userAgreement') }}</a
                >
                {{ t('wechat.and') }}
                <a href="#" @click.prevent="showAgreement('privacy')"
                  >{{ t('wechat.privacyPolicy') }}</a
                >
              </label>
            </div>
          </div>
          <div class="alternative-options">
            <span class="divider">{{ t('wechat.otherLoginMethods') }}</span>
            <div class="login-methods">
              <button
                class="method-button phone"
                @click="switchToRegister('phone')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <span>{{ t('wechat.phoneVerification') }}</span>
              </button>
            </div>
          </div>

          <div class="login-link">
            <span>{{ t('wechat.alreadyHaveAccount') }}</span>
            <a href="#" @click.prevent="currentView = 'login'">{{ t('wechat.goToLogin') }}</a>
          </div>
        </div>

        <!-- 添加协议确认弹窗 -->
        <div v-if="showAgreementConfirmModal" class="confirmation-modal">
          <div class="confirmation-modal-content">
            <div class="confirmation-modal-header">
              <h3>{{ t('agreement.confirmTitle') }}</h3>
            </div>
            <div class="confirmation-modal-body">
              <p>{{ t('agreement.wechatConfirmText') }}</p>
              <div class="agreement-links">
                <a href="#" @click.prevent="showAgreement('user')">{{ t('agreement.userAgreement') }}</a>
                <span>{{ t('agreement.and') }}</span>
                <a href="#" @click.prevent="showAgreement('privacy')">{{ t('agreement.privacyPolicy') }}</a>
              </div>
            </div>
            <div class="confirmation-modal-footer">
              <button class="secondary-button" @click="handleAgreementCancel">{{ t('agreement.cancel') }}</button>
              <button class="primary-button" @click="handleAgreementConfirm">{{ t('agreement.agreeAndContinue') }}</button>
            </div>
          </div>
        </div>

        <!-- 添加取消协议确认弹窗 -->
        <div v-if="showCancelAgreementModal" class="confirmation-modal">
          <div class="confirmation-modal-content">
            <div class="confirmation-modal-header">
              <h3>{{ t('agreement.cancelConfirmTitle') }}</h3>
            </div>
            <div class="confirmation-modal-body">
              <p>{{ t('agreement.cancelConfirmText') }}</p>
              <p>{{ t('agreement.confirmCancel') }}</p>
            </div>
            <div class="confirmation-modal-footer">
              <button class="secondary-button" @click="handleCancelAgreementCancel">{{ t('agreement.dontCancel') }}</button>
              <button class="primary-button" @click="handleCancelAgreementConfirm">{{ t('agreement.confirmReturn') }}</button>
            </div>
          </div>
        </div>

        <!-- 提示框 -->
        <div
          v-if="showAlert"
          class="alert-container"
          :class="{ show: showAlert, [alertType]: true }"
        >
          <div class="alert-content">
            <!-- 成功图标 -->
            <svg
              v-if="alertType === 'success'"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="alert-icon"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <!-- 警告图标 -->
            <svg
              v-else-if="alertType === 'warning'"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="alert-icon"
            >
              <path
                d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
              ></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <!-- 错误图标 -->
            <svg
              v-else
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="alert-icon"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
            <span>{{ alertMessage }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted } from "vue";
import QrcodeVue from "qrcode.vue";

import {
  getsendSms,
  register,
  login,
  getCodeImg,
  loginpassword,
} from "@/api/user";
import {
  getQrCode,
  checkScanStatus,
  wechatLogin,
} from "@/api/wechat";
import { getxieyiList } from "@/api/login";
import { useUserStore } from "@/store/user";
import { useI18n } from "../composables/useI18n";
import { ElMessage } from "element-plus";

const store = useUserStore();
const { t } = useI18n(); // 从useI18n中解构出t函数

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["close", "login-success", "register-success", "open"]);

// State
const currentView = ref("login");
const loginType = ref("code"); // 'code' | 'password'
const showPassword = ref(false);
const isLoading = ref(false);
const showAgreementModal = ref(false);
const agreementType = ref("user");
const showAlert = ref(false);
const alertMessage = ref("");
const alertType = ref("error"); // 'success' | 'error' | 'warning'
const cooldown = ref(0);
const isEntering = ref(false);
const isLeaving = ref(false);
const modalStyle = ref({});
const captchaImg = ref("");
const captchaUuid = ref("");
// 添加状态
const wxState = ref("");
const qrCodeUrl = ref("");
const qrCodeBase64 = ref("");
const scanInterval = ref(null);
const qrUrl = ref("");
const statusMsg = ref(t('wechat.scanInstructions'));

// 协议相关状态
const agreements = ref([]);
const userAgreementUrl = ref("");
const privacyAgreementUrl = ref("");
const baseUrl = import.meta.env.VITE_BASE_API || "";

// 添加新的响应式变量来控制协议确认弹窗
const showAgreementConfirmModal = ref(false);
const showCancelAgreementModal = ref(false);
const previousView = ref('login'); // 用于存储上一个页面

// Forms
const loginForm = reactive({
  username: "",
  verificationCode: "",
  password: "",
  captcha: "",
  rememberMe: false,
  autoLogin: false,
  agreement: false,
});

const registerForm = reactive({
  phone: "",
  verificationCode: "",
  agreement: false,
  password: "",
  confirmPassword: "",
});

// Methods
const closeModal = () => {
  isLeaving.value = true;
  setTimeout(() => {
    isLeaving.value = false;
    emit("close");
  }, 300); // 动画时长
};

const closeOnOverlay = (event) => {
  if (event.target === event.currentTarget) {
    closeModal();
  }
};

// 获取协议列表
const fetchAgreements = async () => {
  try {
    const res = await getxieyiList();
    if (res.code === 200 && res.data && res.data.length > 0) {
      agreements.value = res.data;
      
      // 查找用户协议和隐私协议
      const userAgreement = agreements.value.find(item => item.name === "用户协议");
      const privacyAgreement = agreements.value.find(item => item.name === "隐私协议");
      
      if (userAgreement) {
        userAgreementUrl.value = userAgreement.url;
      }
      
      if (privacyAgreement) {
        privacyAgreementUrl.value = privacyAgreement.url;
      }
    }
  } catch (error) {
    console.error("获取协议列表失败:", error);
  }
};

const showAgreement = (type) => {
  agreementType.value = type;
  showAgreementModal.value = true;
  
  // 如果有协议URL，打开PDF
  if (type === 'user' && userAgreementUrl.value) {
    window.open(baseUrl + userAgreementUrl.value, '_blank');
  } else if (type === 'privacy' && privacyAgreementUrl.value) {
    window.open(baseUrl + privacyAgreementUrl.value, '_blank');
  }
};

const agreeToTerms = () => {
  if (currentView.value === "login") {
    loginForm.agreement = true;
  } else {
    registerForm.agreement = true;
  }
  showAgreementModal.value = false;
};

const showAlertMessage = (message, type = "error") => {
  alertMessage.value = message;
  alertType.value = type;
  showAlert.value = true;
  setTimeout(() => {
    showAlert.value = false;
  }, 3000);
};

const sendLoginVerificationCode = async () => {
  if (!loginForm.username) {
    showAlertMessage(t('login.enterPhoneNumber'));
    return;
  }
  
  // 验证手机号格式
  const phonePattern = /^1[3-9]\d{9}$/;
  if (!phonePattern.test(loginForm.username)) {
    showAlertMessage(t('login.invalidPhone'));
    return;
  }

  try {
    const response = await getsendSms(loginForm.username);

    if (response.code === 200) {
      store.setuuidAction(response.uuid);
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      showAlertMessage(t('login.codeSent'), "success");
    } else {
      showAlertMessage(response.msg || t('login.codeSendFailed'));
    }
  } catch (error) {
    showAlertMessage(t('login.codeSendError'));
    console.error(t('login.codeSendErrorLog'), error);
  }
};

const refreshCaptcha = async () => {
  try {
    const res = await getCodeImg();

    if (res.code === 200) {
      captchaImg.value = "data:image/png;base64," + res.img;
      captchaUuid.value = res.uuid;
    }
  } catch (error) {
    console.error("获取验证码失败:", error);
  }
};

const handleLogin = async () => {
  // 基本表单验证
  if (!loginForm.username) {
    showAlertMessage(t('login.enterPhoneNumber'));
    return;
  }
  
  // 验证手机号格式
  const phonePattern = /^1[3-9]\d{9}$/;
  if (!phonePattern.test(loginForm.username)) {
    showAlertMessage(t('login.invalidPhone'));
    return;
  }

  if (loginType.value === "code") {
    if (!loginForm.verificationCode) {
      showAlertMessage(t('login.enterVerificationCode'));
      return;
    }
    
    // 验证码必须是数字
    if (!/^\d+$/.test(loginForm.verificationCode)) {
      showAlertMessage(t('login.codeFormatError'));
      return;
    }
  } else {
    if (!loginForm.password) {
      showAlertMessage(t('login.enterPassword'));
      return;
    }
    
    if (!loginForm.captcha) {
      showAlertMessage(t('login.enterCaptcha'));
      return;
    }
  }

  if (!loginForm.agreement) {
    showAlertMessage(t('login.agreementRequired'));
    return;
  }

  isLoading.value = true;

  try {
    let response;
    if (loginType.value === "code") {
      response = await login({
        phoneNumber: loginForm.username,
        smsCode: loginForm.verificationCode,
        uuid: store.uuid,
      });
    } else {
      response = await loginpassword({
        username: loginForm.username,
        password: loginForm.password,
        code: loginForm.captcha,
        uuid: captchaUuid.value,
      });
    }

    if (response.code === 200) {
      if(loginType.value === "code"){
        await store.loginAction(response)
      }else{
      await store.loginpasswordaction(response)
      }
      
      localStorage.setItem("userId", store.userId);
      emit("login-success");
      closeModal();
      ElMessage.success(t('login.success'));
    } else {
      if (response.code === 500 && loginType.value === "password") {
        refreshCaptcha(); // 刷新验证码
      }
      showAlertMessage(response.msg || t('login.failure'));
    }
  } catch (error) {
    showAlertMessage(t('login.errorRetry'));
    console.error(t('login.loginError'), error);
    if (loginType.value === "password") {
      refreshCaptcha(); // 刷新验证码
    }
  } finally {
    isLoading.value = false;
  }
};

const switchToRegister = (method) => {
  if (method === "phone") {
    currentView.value = "register-phone";
  } else if (method === "wechat") {
    // 保存当前页面，以便返回
    previousView.value = currentView.value;
    // 显示协议确认弹窗
    showAgreementConfirmModal.value = true;
  }
};

const sendVerificationCode = async () => {
  if (!registerForm.phone) {
    showAlertMessage(t('register.enterPhoneNumber'));
    return;
  }

  try {
    const response = await getsendSms(registerForm.phone);

    if (response.code === 200) {
      store.setuuidAction(response.uuid);
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      showAlertMessage(t('register.codeSent'), "success");
    } else {
      showAlertMessage(response.msg || t('register.codeSendFailed'));
    }
  } catch (error) {
    showAlertMessage(t('register.codeSendError'));
    console.error(t('register.codeSendErrorLog'), error);
  }
};

const handleRegister = async () => {
  if (!registerForm.agreement) {
    showAlertMessage(t('register.agreementRequired'));
    return;
  }

  if (
    !registerForm.phone ||
    !registerForm.verificationCode ||
    !registerForm.password
  ) {
    showAlertMessage(t('register.completeInfo'));
    return;
  }

  if (registerForm.password !== registerForm.confirmPassword) {
    showAlertMessage(t('register.passwordMismatch'));
    return;
  }

  isLoading.value = true;

  try {
    const response = await register({
      phoneNumber: registerForm.phone,
      password: registerForm.password,
      code: registerForm.verificationCode,
      uuid: store.uuid,
    });

    if (response.code === 200) {
      // 注册成功后直接登录
      try {
        // 构建登录响应对象
        let loginResponse;
        
        // 如果注册响应中包含token，直接使用
        if (response.data && response.data.token) {
          loginResponse = response;
        } else {
          // 否则尝试使用手机号和密码登录
          loginResponse = await loginpassword({
            username: registerForm.phone,
            password: registerForm.password,
            code: "",
            uuid: "",
          });
        }
        
        if (loginResponse.code === 200) {
          // 登录成功处理
          if (loginResponse.data && loginResponse.data.token) {
            await store.loginAction(loginResponse);
          } else {
            await store.loginpasswordaction(loginResponse);
          }
          
          // 发送登录成功事件并关闭弹窗
          emit("login-success");
          closeModal();
          ElMessage.success(t('register.registerLoginSuccess'));
        } else {
          // 登录失败但注册成功
          showAlertMessage(t('register.registerSuccessLoginManually'), "success");
          emit("register-success");
          currentView.value = "login";
          loginForm.username = registerForm.phone;
        }
      } catch (loginError) {
        console.error(t('register.autoLoginFailed'), loginError);
        showAlertMessage(t('register.registerSuccessLoginManually'), "success");
        emit("register-success");
        currentView.value = "login";
        loginForm.username = registerForm.phone;
      }
    } else {
      showAlertMessage(response.msg || t('register.failure'));
    }
  } catch (error) {
    console.error(t('register.registerError'), error);
    showAlertMessage(t('register.errorRetry'));
  } finally {
    isLoading.value = false;
  }
};

const setModalPosition = (buttonRect) => {
  // 不再需要基于按钮位置设置动画起点，现在始终居中
  // 为了适配移动端横屏，我们需要设置缩放比例
  
  // 判断是否为移动设备横屏模式
  const isMobileLandscape = window.innerHeight <= 600 && window.innerWidth > window.innerHeight;
  
  if (isMobileLandscape) {
    // 设置移动端横屏的缩放样式
    document.documentElement.style.setProperty('--modal-scale', '0.85');
  } else {
    // 其他情况正常显示
    document.documentElement.style.setProperty('--modal-scale', '1');
  }
  
  // 设置起始中心点
  modalStyle.value = {
    "--start-x": `${window.innerWidth / 2}px`,
    "--start-y": `${window.innerHeight / 2}px`,
  };
};

// 初始化微信二维码
const initWechatQrCode = async () => {
  try {
    statusMsg.value = t('wechat.loadingQrCode');
    const res = await getQrCode();
    if (res.code === 200) {
      wxState.value = res.data.state;
      qrUrl.value = res.data.url; // 使用后端返回的完整授权URL
      console.log(t('wechat.loginUrlLog'), res.data.url);
      statusMsg.value = t('wechat.scanInstructions');
      
      // 确保iframe在加载时正确显示
      setTimeout(() => {
        const iframe = document.querySelector('.wx-login-frame');
        if (iframe) {
          iframe.style.visibility = 'visible';
        }
      }, 200);
      
      // 启动轮询检查扫码状态
      // startScanStatusCheck();
    }
  } catch (error) {
    statusMsg.value = t('wechat.qrCodeError');
    console.error(t('wechat.qrCodeErrorLog'), error);
  }
};

// 处理iframe加载完成事件
const handleIframeLoad = () => {
  console.log(t('wechat.iframeLoaded'));
  statusMsg.value = t('wechat.scanInstructions');
};

// 创建一个引用保存调整大小的处理函数
const resizeHandler = () => {
  const isMobileLandscape = window.innerHeight <= 600 && window.innerWidth > window.innerHeight;
  if (isMobileLandscape) {
    document.documentElement.style.setProperty('--modal-scale', '0.85');
  } else {
    document.documentElement.style.setProperty('--modal-scale', '1');
  }
};

// 监听URL参数变化
onMounted(() => {
  // 添加窗口大小变化监听，以便调整缩放比例
  window.addEventListener('resize', resizeHandler);
  
  // 初始调用一次以设置初始值
  resizeHandler();
  
  // 获取协议列表
  fetchAgreements();
});

onUnmounted(() => {
  if (scanInterval.value) {
    clearInterval(scanInterval.value);
  }
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', resizeHandler);
});

// 在每次弹窗显示状态改变时检查
watch(
  () => props.isVisible,
  (newVal) => {
    if (newVal) {
      // 禁用页面滚动
      document.body.style.overflow = "hidden";
      isEntering.value = true;
      setTimeout(() => {
        isEntering.value = false;
      }, 300);
    } else {
      // 恢复页面滚动
      document.body.style.overflow = "";
    }
  }
);

// 监听视图切换
watch(currentView, (newView) => {
  if (newView === "register-wechat") {
    initWechatQrCode();
  } else {
    if (scanInterval.value) {
      clearInterval(scanInterval.value);
    }
  }
});

// 监听登录方式变化
watch(loginType, (newType) => {
  if (newType === "password") {
    refreshCaptcha();
  }
});

// 在组件挂载时如果是密码登录则获取验证码
onMounted(() => {
  if (loginType.value === "password") {
    refreshCaptcha();
  }
});

// 添加协议确认处理函数
const handleAgreementConfirm = () => {
  // 自动勾选协议
  registerForm.agreement = true;
  // 关闭确认弹窗
  showAgreementConfirmModal.value = false;
  // 切换到微信扫码页面
  currentView.value = "register-wechat";
};

// 添加协议确认取消函数
const handleAgreementCancel = () => {
  // 关闭确认弹窗
  showAgreementConfirmModal.value = false;
  // 不切换页面，保持在当前页面
};

// 监听协议勾选状态变化
watch(() => registerForm.agreement, (newVal, oldVal) => {
  // 如果是在微信扫码页面取消勾选
  if (currentView.value === "register-wechat" && oldVal === true && newVal === false) {
    // 显示取消确认弹窗
    showCancelAgreementModal.value = true;
  }
});

// 添加取消协议处理函数
const handleCancelAgreementConfirm = () => {
  // 关闭取消确认弹窗
  showCancelAgreementModal.value = false;
  // 返回上一个页面
  currentView.value = previousView.value;
};

// 添加取消协议取消函数
const handleCancelAgreementCancel = () => {
  // 关闭取消确认弹窗
  showCancelAgreementModal.value = false;
  // 恢复勾选状态
  registerForm.agreement = true;
};

defineExpose({
  setModalPosition,
});
</script>

<style scoped>
:root {
  --modal-scale: 1;
}

/* 基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  transition: background-color 0.3s ease;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(var(--modal-scale, 1));
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 360px;
  max-width: 95%;
  max-height: 98vh; /* Increased to ensure full visibility */
  overflow-y: auto;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.modal-entering {
  animation: modalEnter 0.3s ease-out;
}

.modal-leaving {
  animation: modalLeave 0.3s ease-in;
}

@keyframes modalEnter {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(calc(0.3 * var(--modal-scale, 1)));
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(var(--modal-scale, 1));
  }
}

@keyframes modalLeave {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(var(--modal-scale, 1));
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(calc(0.3 * var(--modal-scale, 1)));
  }
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f5f5f5;
  color: #666;
}

.modal-content {
  padding: 20px;
  flex: 1;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

/* 表单样式 */
.form-group {
  margin-bottom: 14px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 9px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  height: 40px;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3b82f6;
  outline: none;
}

.password-input-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.verification-code-container {
  display: flex;
  gap: 10px;
}

.send-code-button {
  white-space: nowrap;
  padding: 0 12px;
  height: 40px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-code-button:hover {
  background-color: #2563eb;
}

.send-code-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

/* 选项样式 */
.form-options {
  margin-bottom: 20px;
}

.login-options-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.remember-me,
.auto-login {
  display: flex;
  align-items: center;
  gap: 6px;
}

.remember-me label,
.auto-login label {
  font-size: 14px;
  color: #666;
}

.login-method-icon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

/* 登录方式开关 */
.login-type-switch {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  gap: 10px;
}

.switch-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.switch-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

@media screen and (max-width: 480px) {
  .login-options-row {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .switch-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .login-type-switch {
    margin-bottom: 16px;
  }
  
  .remember-me label,
  .auto-login label {
    font-size: 13px;
  }
}

/* 协议样式 */
.agreement-section {
  margin-bottom: 16px;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.agreement-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}

.agreement-checkbox label {
  font-size: 13px;
  color: #333;
}

.agreement-checkbox a {
  color: #3b82f6;
  text-decoration: none;
}

.agreement-checkbox a:hover {
  text-decoration: underline;
}

/* 按钮样式 */
.primary-button {
  width: 100%;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 20px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-button:hover {
  background-color: #2563eb;
}

.primary-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.secondary-button {
  width: 100%;
  padding: 12px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.secondary-button:hover {
  background-color: #e5e7eb;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 其他登录方式样式 */
.alternative-options {
  margin-top: 24px;
  margin-bottom: 20px;
}

.divider {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 14px;
  margin-bottom: 16px;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #eee;
}

.divider::before {
  margin-right: 10px;
}

.divider::after {
  margin-left: 10px;
}

.login-methods {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.method-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.method-button:hover {
  background-color: #f3f4f6;
}

.method-button span {
  font-size: 14px;
  color: #4b5563;
}

.phone {
  color: #3b82f6;
}

.wechat {
  color: #10b981;
}

/* 注册链接样式 */
.register-link,
.login-link {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.register-link a,
.login-link a {
  color: #3b82f6;
  text-decoration: none;
  margin-left: 4px;
}

.register-link a:hover,
.login-link a:hover {
  text-decoration: underline;
}

/* 二维码样式 */
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 15px 0;
}

.qr-code {
  width: 225px;
  height: 350px;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  overflow: hidden;
}

.qr-instructions {
  text-align: center;
  font-size: 13px;
  color: #666;
  margin-top: 5px;
  margin-bottom: 5px;
}

.wx-login-frame {
  width: 300px;
  height: 450px;
  border: none;
  transform: scale(0.75);
  transform-origin: top center;
  margin-top: 110px;
}

/* 协议同意区域样式 */
.agreement-section {
  margin-bottom: 16px;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.agreement-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}

.agreement-checkbox label {
  font-size: 13px;
  color: #333;
}

.agreement-checkbox a {
  color: #3b82f6;
  text-decoration: none;
}

.agreement-checkbox a:hover {
  text-decoration: underline;
}

/* 协议弹窗样式 */
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2100;
}

.agreement-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.agreement-modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.agreement-modal-header button {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.agreement-modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.agreement-modal-body h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: 0;
  margin-bottom: 16px;
}

.agreement-modal-body p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
  color: #333;
  margin-top: 0;
  margin-bottom: 16px;
}

.agreement-modal-body p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
}

.agreement-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.agreement-modal-footer button {
  width: auto;
  padding: 8px 16px;
}

/* 提示框样式 */
.alert-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-100px);
  padding: 12px 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 1200;
}

.alert-container.show {
  transform: translateX(-50%) translateY(0);
}

.alert-container.success {
  background-color: #ecfdf5;
  color: #047857;
}

.alert-container.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.alert-container.error {
  background-color: #fef2f2;
  color: #b91c1c;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-icon {
  width: 20px;
  height: 20px;
}

/* 全局遮罩层样式 */
.global-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1999;
  /* 确保在其他元素之上，但在登录窗口之下 */
  pointer-events: all;
  /* 拦截所有点击事件 */
  transition: opacity 0.3s ease;
}

/* 确保全局遮罩层在不可见时不阻止交互 */
.global-overlay[style*="display: none"],
.global-overlay:not([style*="display"]) {
  pointer-events: none;
}

/* 微信扫码登录专用样式 */
.wechat-agreement {
  margin: 5px 0 10px;
  padding: 8px 10px;
  background-color: #f0f7ff;
  border-radius: 6px;
  border: 1px solid #d0e1fd;
}

.wechat-agreement .agreement-checkbox {
  margin-bottom: 0;
}

.wechat-agreement .agreement-checkbox label {
  font-size: 13px;
  color: #333;
}

@media screen and (max-width: 480px) {
  .qr-code {
    width: 200px;
    height: 200px;
  }
  
  .wx-login-frame {
    transform: scale(0.67);
    margin-top: -60px;
  }
  
  .qr-instructions {
    font-size: 12px;
  }
  
  .wechat-agreement {
    padding: 8px;
    margin: 8px 0 12px;
  }
  
  .wechat-agreement .agreement-checkbox label {
    font-size: 12px;
  }
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-img {
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #666;
}

.loading-icon {
  width: 40px;
  height: 40px;
  animation: rotate 1s linear infinite;
  stroke-dasharray: 90;
  stroke-dashoffset: 90;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes dash {
  0% {
    stroke-dashoffset: 90;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -90;
  }
}

/* 提示框样式 */
.alert-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-100px);
  padding: 12px 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 1200;
}

.alert-container.show {
  transform: translateX(-50%) translateY(0);
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-icon {
  width: 20px;
  height: 20px;
}

/* 不同类型的提示样式 */
.alert-container.success {
  background-color: #ecfdf5;
  color: #047857;
}

.alert-container.error {
  background-color: #fef2f2;
  color: #b91c1c;
}

.alert-container.warning {
  background-color: #fffbeb;
  color: #b45309;
}

@media screen and (max-width: 480px) {
  .captcha-container {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
  
  .captcha-img {
    width: 100%;
    height: auto;
  }
  
  .alert-container {
    width: 90%;
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .alert-icon {
    width: 18px;
    height: 18px;
  }
  
  .alert-content {
    gap: 8px;
  }
}

@media screen and (max-height: 600px) and (orientation: landscape) {
  .modal-container {
    max-height: 98vh;
    transform: translate(-50%, -50%) scale(0.85);
  }
  
  .modal-content {
    padding: 12px 16px;
  }
  
  .modal-title {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  .form-group {
    margin-bottom: 10px;
  }
  
  .login-type-switch {
    margin-bottom: 12px;
  }
  
  .agreement-section {
    margin-bottom: 12px;
  }
  
  .primary-button {
    margin-bottom: 12px;
  }
  
  .qr-code-container {
    margin: 6px 0;
  }
  
  .qr-code {
    width: 180px;
    height: 180px;
  }
  
  .wx-login-frame {
    transform: scale(0.6);
    margin-top: -80px;
  }
  
  .wechat-agreement {
    padding: 5px 8px;
    margin: 3px 0 8px;
  }
  
  .alternative-options {
    margin-top: 8px;
  }
  
  .captcha-container {
    flex-direction: row;
  }
  
  .captcha-img {
    height: 38px;
    width: auto;
  }
}

/* 确认弹窗样式 */
.confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2200;
}

.confirmation-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow-y: auto;
}

/* 移除了手机号绑定弹窗样式 */

.confirmation-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.confirmation-modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.confirmation-modal-body {
  padding: 20px;
}

.confirmation-modal-body p {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 12px;
}

.confirmation-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirmation-modal-footer button {
  width: auto;
  padding: 8px 16px;
  margin-bottom: 0;
}

.agreement-links {
  margin-top: 8px;
  font-size: 14px;
}

.agreement-links a {
  color: #3b82f6;
  text-decoration: none;
}

.agreement-links a:hover {
  text-decoration: underline;
}

@media screen and (max-width: 480px) {
  .confirmation-modal-content {
    width: 90%;
  }
  
  .confirmation-modal-header {
    padding: 12px 16px;
  }
  
  .confirmation-modal-body {
    padding: 16px;
  }
  
  .confirmation-modal-footer {
    padding: 12px 16px;
  }
}

/* 移除了独立绑定弹窗样式 */
</style>