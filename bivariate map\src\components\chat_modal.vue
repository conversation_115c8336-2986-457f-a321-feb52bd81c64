<template>
  <!-- 聊天弹窗遮罩 -->
  <div v-if="isVisible" class="chat-modal-overlay" @click="handleOverlayClick">
    <div class="chat-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="chat-header">
        <div class="chat-user-info">
          <div class="avatar-container">
            <img
              v-if="selectedContact"
              :src="selectedContact.avatar || defaultAvatar"
              :alt="selectedContact?.name || '联系人'"
              class="user-avatar"
            />
    
          </div>
          <div class="user-details">
            <h3 class="user-name">{{ selectedContact?.name || '选择联系人' }}</h3>
            <div class="user-info-row">
              <span class="user-status">{{ selectedContact?.status || '' }}</span>
              <span
                v-if="selectedContact?.userDetails?.companyName"
                class="user-company"
              >
                {{ selectedContact.userDetails.companyName }}
              </span>
            </div>
       
          </div>
        </div>
        <div class="chat-actions">
          <!-- <button class="action-btn reset-btn" @click="resetUnreadCounts" :title="t('chatModal.resetUnreadMessages')">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
              <path d="M21 3v5h-5"></path>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
              <path d="M3 21v-5h5"></path>
            </svg>
          </button> -->
          <button class="action-btn close-btn" @click="closeModal" :title="t('chatModal.closeChat')">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="m18 6-12 12" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 用户详细信息面板 -->
      <div
        v-if="showUserDetails && selectedContact?.userDetails"
        class="user-details-panel"
      >
        <div class="details-header">
          <h4>{{ t('chatModal.contactDetails') }}</h4>
          <button class="close-details-btn" @click="showUserDetails = false">
            ×
          </button>
        </div>
        <div class="details-content">
          <div
            class="details-row"
            v-if="selectedContact?.userDetails?.companyName"
          >
            <span class="details-label">{{ t('chatModal.companyName') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.companyName
            }}</span>
          </div>
          <div
            class="details-row"
            v-if="selectedContact?.userDetails?.introduction"
          >
            <span class="details-label">{{ t('chatModal.introduction') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.introduction
            }}</span>
          </div>
          <div class="details-row" v-if="selectedContact?.userDetails.email">
            <span class="details-label">{{ t('chatModal.email') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.email
            }}</span>
          </div>
          <div
            class="details-row"
            v-if="selectedContact?.userDetails?.phonenumber"
          >
            <span class="details-label">{{ t('chatModal.phone') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.phonenumber
            }}</span>
          </div>
          <div class="details-row" v-if="selectedContact?.userDetails.qq">
            <span class="details-label">{{ t('chatModal.qq') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.qq
            }}</span>
          </div>
          <div class="details-row" v-if="selectedContact?.userDetails.wx">
            <span class="details-label">{{ t('chatModal.wechat') }}:</span>
            <span class="details-value">{{
              selectedContact.userDetails.wx
            }}</span>
          </div>
          <div
            class="details-row"
            v-if="selectedContact?.userDetails?.memberType"
          >
            <span class="details-label">{{ t('chatModal.memberType') }}:</span>
            <span
              class="details-value"
              :class="{
                'super-member-text':
                  selectedContact.userDetails.memberType === t('chatModal.superMember'),
              }"
            >
              {{ selectedContact.userDetails.memberType }}
            </span>
          </div>
        </div>
      </div>

      <div class="chat-body">
        <!-- 联系人列表 -->
        <div class="contacts-sidebar">
          <div class="contacts-header">
            <div class="header-top">
              <div v-if="isSearchMode" class="search-title">
                <button
                  class="return-btn"
                  @click="returnToContacts"
                  :title="t('chatModal.contactList')"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="return-icon"
                  >
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                </button>
                <span>{{ t('chatModal.searchResults') }}</span>
              </div>
              <div v-else class="search-title">
                <span>{{ t('chatModal.contactList') }}</span>
              </div>
            </div>
            <div class="search-box">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="search-icon"
              >
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.35-4.35" />
              </svg>
              <input
                type="text"
                :placeholder="t('chatModal.searchPlaceholder')"
                v-model="searchQuery"
                @keyup.enter="searchContact"
                class="search-input"
                autocomplete="off"
              />
              <button class="search-btn" @click="searchContact" :title="t('chatModal.search')">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="search-btn-icon"
                >
                  <circle cx="11" cy="11" r="8" />
                  <path d="m21 21-4.35-4.35" />
                </svg>
              </button>
            </div>
          </div>

          <div class="contacts-list">
            <div
              v-for="contact in filteredContacts"
              :key="contact.id"
              class="contact-item"
              :class="{ active: selectedContact?.id === contact.id }"
              @click="selectContact(contact)"
            >
              <div class="contact-avatar">
                <img :src="contact.avatar || defaultAvatar" :alt="contact.name || '联系人'" />
              </div>
              <div class="contact-info">
                <div class="contact-header">
                  <h4 class="contact-name">{{ contact.name }}</h4>
                  <span class="contact-time">{{
                    contact.lastMessageTime
                  }}</span>
                </div>
                <div class="contact-message">
                  <span
                    class="last-message"
                    :title="
                      contact.lastMessage ? contact.lastMessage : contact.userDetails?.companyName
                    "
                  >
                    {{
                      contact.lastMessage ? contact.lastMessage :
                      contact.userDetails?.companyName ||
                      t('chatModal.startChat')
                    }}
                  </span>
                  <div 
                    v-if="contact.unreadCount && contact.unreadCount > 0 && !contact.lastMessageIsOwn" 
                    class="unread-badge"
                  >
                    {{ contact.unreadCount > 99 ? "99+" : contact.unreadCount }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <!-- 聊天消息区域 -->
          <div class="chat-messages" ref="messagesContainer">
            <div class="message-list">
              <div
                v-for="message in currentMessages"
                :key="message.id"
                class="message-item"
                :class="{ 'own-message': message.isOwn }"
              >
                <div class="message-avatar" v-if="!message.isOwn">
                  <img
                    :src="selectedContact?.avatar || defaultAvatar"
                    :alt="selectedContact?.name || '联系人'"
                  />
                </div>
                <div class="message-content memessage-ooo">
                  <div
                    class="message-bubble"
                    :class="{ 'own-bubble': message.isOwn }"
                  >
                    <p>{{ message.text }}</p>
                  </div>
                  <div class="message-time">
                    {{ formatTime(message.timestamp) }}
                  </div>
                </div>
                <div class="message-avatar own-avatar" v-if="message.isOwn">
                  <img
                    :src="userAvatarUrl"
                    :alt="userStore.userInfo.nickName || '我'"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input-area">
            <div class="input-container">
              <textarea
                v-model="newMessage"
                @keydown.enter.exact.prevent="sendMessage"
                @keydown.enter.shift.exact="newMessage += '\n'"
                :placeholder="t('chatModal.messageInput')"
                class="message-input"
                rows="3"
              ></textarea>
              <button
                class="send-btn"
                @click="sendMessage"
                :disabled="!newMessage.trim() || isSending"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="m22 2-7 20-4-9-9-4Z" />
                  <path d="M22 2 11 13" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  nextTick,
  watch,
  onMounted,
  computed,
  onBeforeUnmount,
} from "vue";
import Pusher from "pusher-js";
import { useUserStore } from "../store/user";
import { useChatModalStore } from "../store/chatModal";
import { useI18n } from "../composables/useI18n";
import {
  authenticatePusher,
  sendMessage as apiSendMessage,
  getChatHistory,
  getContacts,
  getUnreadCount,
  markAsRead,
  getphuserinfo,
  // getUnreadCount
} from "../api/pusher";
import { ElMessage, ElLoading } from "element-plus";
// import "https://js.pusher.com/8.4.0/pusher.min.js";
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close"]);

const userStore = useUserStore();
const chatModalStore = useChatModalStore();
const { t, currentLocale } = useI18n();
const newMessage = ref("");
const messagesContainer = ref(null);
const searchQuery = ref("");
const pusherInstance = ref(null);
const channel = ref(null);
const showUserDetails = ref(false);
const defaultAvatar = "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/**********.jpg";

const contacts = ref([]);
const originalContacts = ref([]); // 存储原始联系人列表
const isSearchMode = ref(false); // 是否处于搜索模式
const selectedContact = ref(null); // 初始值设为null，避免空对象属性访问错误
const messagesData = ref({}); // { [contactId]: [msg, ...] }
const unreadMap = ref({}); // { [contactId]: count }

// 清理资源的定时器ID
const unreadSyncInterval = ref(null);
const connectionCheckInterval = ref(null);

const isSending = ref(false); // 添加发送状态的响应式变量
const userAvatarUrl = computed(() => {
  return userStore.userInfo.avatar
    ? userStore.userInfo.avatar.startsWith("http")
      ? userStore.userInfo.avatar
      : import.meta.env.VITE_BASE_API + userStore.userInfo.avatar
    : "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/**********.jpg";
});
const currentMessages = computed(() => {
  return messagesData.value[selectedContact.value?.id] || [];
});

// 不再需要过滤计算属性，我们将直接使用contacts.value
const filteredContacts = computed(() => {
  return contacts.value;
});

function formatLastTime(timeStr) {
  if (!timeStr) return "";
  const d = new Date(timeStr);
  const now = new Date();
  if (
    d.getFullYear() === now.getFullYear() &&
    d.getMonth() === now.getMonth() &&
    d.getDate() === now.getDate()
  ) {
    return d.toLocaleTimeString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US', {
      hour: "2-digit",
      minute: "2-digit",
    });
  }
  return d.toLocaleDateString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US');
}
// 添加移动联系人到顶部的函数
function moveContactToTop(contactId) {
  const index = contacts.value.findIndex((c) => c.id === contactId);
  if (index > 0) {
    const contact = contacts.value.splice(index, 1)[0];
    contacts.value.unshift(contact);
  }
}
function formatTime(timestamp) {
  if (!timestamp) return t('chatModal.justNow');

  const now = new Date();
  const messageTime = new Date(timestamp);

  // 检查日期是否有效
  if (isNaN(messageTime.getTime())) return t('chatModal.justNow');

  const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));

  if (diffInMinutes < 1) return t('chatModal.justNow');
  if (diffInMinutes < 60) return t('chatModal.minutesAgo', { minutes: diffInMinutes });
  if (diffInMinutes < 1440) return t('chatModal.hoursAgo', { hours: Math.floor(diffInMinutes / 60) });

  // 如果是今天，显示时间
  if (messageTime.toDateString() === now.toDateString()) {
    return messageTime.toLocaleTimeString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US', {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // 如果是昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (messageTime.toDateString() === yesterday.toDateString()) {
    return (
      t('chatModal.yesterday') + " " +
      messageTime.toLocaleTimeString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US', {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  }

  // 其他日期显示完整日期
  return (
    messageTime.toLocaleDateString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US') +
    " " +
    messageTime.toLocaleTimeString(currentLocale.value === 'zh-CN' ? 'zh-CN' : 'en-US', {
      hour: "2-digit",
      minute: "2-digit",
    })
  );
}

// 获取联系人列表
async function fetchContacts() {
  const res = await getContacts();
  const currentUserId = userStore.userInfo.userId;
  
  if (res && res.data) {
    // 只有在非搜索模式下才更新联系人列表
    if (!isSearchMode.value) {
      const contactsList = res.data.map((item) => ({
        id: item.userId,
        name: item.nickName,
        avatar: item.avatar
          ? item.avatar.startsWith("http")
            ? item.avatar
            : import.meta.env.VITE_BASE_API + item.avatar
          : "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/**********.jpg",
        isOnline: true,
        lastMessage: item.lastMessage,
        lastMessageTime: formatLastTime(item.lastTime),
        // 记录最后一条消息是不是自己发的
        lastMessageIsOwn: item.lastMessageFromUserId === currentUserId,
        // 重要：只有当消息不是自己发的时才计入未读数
        unreadCount: item.lastMessageFromUserId !== currentUserId && item.unreadCount && parseInt(item.unreadCount) > 0 ? 
                     parseInt(item.unreadCount) : 0,
        userDetails: {
          companyName: item.companyName || "",
          introduction: item.introduction || item.businessIntroduction || "",
          memberType: item.memberType || "普通会员",
          phonenumber: item.phonenumber || "",
        },
      }));

      contacts.value = contactsList;
      // 保存原始联系人列表，以便搜索后可以返回
      originalContacts.value = [...contactsList];

      // 计算总未读消息数量，只计算非自己发送的未读消息
      let totalUnread = 0;
      contactsList.forEach(contact => {
        totalUnread += contact.unreadCount || 0;
      });
      
      // 更新全局未读消息计数
      chatModalStore.updateWeiduCount(totalUnread);

      if (contacts.value.length > 0) {
        selectedContact.value = contacts.value[0];
      }
    }
  }
}

// 获取未读消息数量
async function fetchUnread() {
  const currentUserId = userStore.userInfo.userId;
  const res = await getUnreadCount();
  
  if (res && res.data) {
    unreadMap.value = res.data; // { [contactId]: count }
    
    // 获取每个联系人的最后一条消息信息，以确定消息方向
    const contactsRes = await getContacts();
    const contactsData = contactsRes && contactsRes.data ? contactsRes.data : [];
    
    // 创建一个映射表，记录每个联系人的最后一条消息是否是自己发送的
    const lastMessageDirectionMap = {};
    contactsData.forEach(item => {
      lastMessageDirectionMap[item.userId] = item.lastMessageFromUserId === currentUserId;
    });
    
    // 更新联系人列表，只有非自己发送的消息才计入未读数
    contacts.value.forEach((c) => {
      const isLastMessageFromSelf = lastMessageDirectionMap[c.id];
      // 如果最后一条消息是自己发送的，则未读数为0，否则使用服务器返回的未读数
      c.unreadCount = (unreadMap.value[c.id] && parseInt(unreadMap.value[c.id]) > 0 && !isLastMessageFromSelf) ? 
                      parseInt(unreadMap.value[c.id]) : 0;
    });
    
    // 重新计算总未读数
    let totalUnread = 0;
    contacts.value.forEach(contact => {
      totalUnread += contact.unreadCount || 0;
    });
    
    // 更新全局未读消息计数
    chatModalStore.updateWeiduCount(totalUnread);
  }
}

// 获取历史消息
async function fetchHistory(contactId) {
  const res = await getChatHistory(contactId);
  const currentUserId = userStore.userInfo.userId;
  
  if (res && res.data) {
    messagesData.value[contactId] = res.data.map((msg) => ({
      id: msg.id || Date.now(),
      text: msg.content,
      isOwn: msg.fromUserId === currentUserId,
      timestamp: msg.createTime, // 修改这里：使用 createTime 而不是 createdAt
      isRead: msg.isRead || (msg.fromUserId === currentUserId) // 自己发送的消息总是视为已读
    }));
  } else {
    messagesData.value[contactId] = [];
  }
}

// 标记已读
async function markContactAsRead(contactId) {
  const contact = contacts.value.find((c) => c.id === contactId);
  const previousUnreadCount = contact ? contact.unreadCount || 0 : 0;
  
  try {
    await markAsRead(contactId);
    if (contact) contact.unreadCount = 0;
    
    // 减少全局未读消息计数
    if (previousUnreadCount > 0) {
      chatModalStore.decrementWeiduCount(previousUnreadCount);
    }
    
    // 使用同步方法更新未读消息数量
    await chatModalStore.syncUnreadCount();
    
    // 标记消息为已读
    if (messagesData.value[contactId]) {
      messagesData.value[contactId] = messagesData.value[contactId].map(msg => {
        // 自己发送的消息或已标记为已读的消息保持不变
        if (msg.isOwn || msg.isRead) return msg;
        
        // 标记非自己发送的未读消息为已读
        return { ...msg, isRead: true };
      });
    }
  } catch (error) {
    console.error('标记消息为已读失败:', error);
  }
}

// 选择联系人
const selectContact = async (contact) => {
  if (!contact) return;
  selectedContact.value = contact;
  // 选择联系人时获取该联系人的历史消息
  await fetchHistory(contact.id);
  // 只有在有历史消息时才标记已读
  if (messagesData.value[contact.id]?.length > 0) {
    await markContactAsRead(contact.id);
  }

  // 确保DOM更新后滚动到底部并聚焦输入框
  await nextTick(() => {
    scrollToBottom();
    // 聚焦输入框
    const inputElement = document.querySelector(".message-input");
    if (inputElement) {
      setTimeout(() => {
        inputElement.focus();
      }, 100);
    }
  });
};

// 发送消息
const sendMessage = async () => {
  if (!newMessage.value.trim() || !selectedContact.value || isSending.value)
    return;
  isSending.value = true;
  try {
    // 发送到后端,指定接收者ID
    await apiSendMessage({
      toUserId: selectedContact.value.id,
      content: newMessage.value.trim(),
    });

    // 本地添加消息
    if (!messagesData.value[selectedContact.value.id])
      messagesData.value[selectedContact.value.id] = [];
    messagesData.value[selectedContact.value.id].push({
      id: Date.now(),
      text: newMessage.value.trim(),
      isOwn: true,
      timestamp: new Date(),
    });
    selectedContact.value.lastMessage = newMessage.value.trim();
    selectedContact.value.lastMessageTime = "刚刚";
    moveContactToTop(selectedContact.value.id);
    newMessage.value = "";
    nextTick(scrollToBottom);
  } catch (error) {
    console.error("发送消息失败:", error);
  } finally {
    isSending.value = false;
  }
};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 关闭弹窗
const closeModal = () => {
  emit("close");
  //清除当前选中的联系人
  selectedContact.value = null;
  // 确保任何可能存在的事件清理都被执行
  document.body.style.overflow = "auto"; // 恢复页面滚动
};

// 点击遮罩关闭
const handleOverlayClick = (event) => {
  if (event.target === event.currentTarget) {
    closeModal();
  }
};

// Pusher 鉴权
function setupPusherAuth() {
  // 如果已经存在实例且状态是connected，则不重新创建
  if (pusherInstance.value && pusherInstance.value.connection.state === 'connected') {
    console.log('Pusher连接已存在且状态为connected，无需重新创建');
    return;
  }
  
  // 如果存在旧实例但状态不是connected，先断开旧连接
  if (pusherInstance.value) {
    console.log('断开旧的Pusher连接并创建新连接');
    pusherInstance.value.disconnect();
  }

  console.log('创建新的Pusher连接');
  userStore.ii = 1;

  pusherInstance.value = new Pusher("b83cfc5bc26a5cab043e", {
    cluster: "us3",
    authorizer: (channel) => {
      return {
        authorize: async (socketId, callback) => {
          try {
            const response = await authenticatePusher({
              socket_id: socketId,
              channel_name: channel.name,
            });
            callback(false, response.data);
          } catch (error) {
            callback(true, error);
          }
        },
      };
    },
  });

  // 使用当前用户ID创建私有频道
  const channelName = `my-channel`;
  channel.value = pusherInstance.value.subscribe(channelName);
  console.log("创建私有频道:", channel);

  // 监听私聊消息事件
  channel.value.bind("my-event", async (data) => {
    // console.log("12321321");
    console.log('看看可不可监听到消息',data ,userStore.userInfo.userId);
    
    // 确保只处理发送给当前用户的消息，且不是当前用户自己发送的
    if (data.toUserId === userStore.userInfo.userId && data.fromUserId !== userStore.userInfo.userId) {
      console.log("收到新消息:", data);
      const contactId = data.fromUserId;
      
      // 使用Vue的nextTick确保在DOM更新后处理消息
      nextTick(() => {
        handleNewMessage(contactId, data);
        // 如果不是当前选中的联系人，增加全局未读数
        if (selectedContact.value?.id !== contactId) {
          // 增加全局未读消息计数
          chatModalStore.incrementWeiduCount();
        }
      });
    }
  });

  // 连接状态监听
  pusherInstance.value.connection.bind("connected", () => {
    console.log("Pusher 已连接");
  });

  pusherInstance.value.connection.bind("disconnected", () => {
    console.log("Pusher 已断开连接");
  });

  pusherInstance.value.connection.bind("error", (err) => {
    console.error("Pusher 连接错误:", err);
  });
}

// 处理新消息的统一函数
async function handleNewMessage(contactId, data) {
  const currentUserId = userStore.userInfo.userId;
  const isFromCurrentUser = data.fromUserId === currentUserId;
  
  // 如果没有这个联系人的消息数组,则初始化
  if (!messagesData.value[contactId]) {
    messagesData.value[contactId] = [];
  }

  // 添加新消息
  messagesData.value[contactId].push({
    id: Date.now(),
    text: data.message,
    isOwn: isFromCurrentUser,
    timestamp: data.createTime || new Date().toISOString(), // 使用 createTime
    isRead: isFromCurrentUser // 自己发送的消息自动标记为已读
  });

  // 更新联系人最后一条消息
  const contact = contacts.value.find((c) => c.id === contactId);
  if (contact) {
    contact.lastMessage = data.message;
    contact.lastMessageTime = formatLastTime(data.createdAt);
    // 记录最后一条消息是否是自己发送的
    contact.lastMessageIsOwn = isFromCurrentUser;
    moveContactToTop(contactId); // 将该联系人移动到顶部
    
    // 仅当不是当前选中的联系人且消息不是自己发送的时增加未读数
    if (selectedContact.value?.id !== contactId && !isFromCurrentUser) {
      contact.unreadCount = (contact.unreadCount || 0) + 1;
    } else if (selectedContact.value?.id === contactId) {
      // 如果是当前选中的联系人，立即标记为已读
      await markContactAsRead(contactId);
    }
  }

  nextTick(scrollToBottom);
}

// 检查Pusher连接状态并在需要时重新连接
const checkPusherConnection = () => {
  if (!pusherInstance.value) {
    // 如果实例不存在，则重新设置
    setupPusherAuth();
    return;
  }
  
  // 如果连接状态不是'connected'，尝试重新连接
  if (pusherInstance.value.connection.state !== 'connected') {
    console.log('Pusher连接已断开，尝试重新连接...');
    pusherInstance.value.connect();
  }
};

// 添加窗口焦点事件处理
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    console.log('页面获得焦点，检查Pusher连接状态');
    // 当页面重新获得焦点时，检查连接状态
    checkPusherConnection();
  }
};

onMounted(async () => {
  setupPusherAuth();
  
  // 设置定时检查Pusher连接状态
  connectionCheckInterval.value = setInterval(checkPusherConnection, 30000); // 每30秒检查一次
  
  // 添加页面可见性变化事件监听
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // 如果用户已登录，获取未读消息数量
  if (userStore.token) {
    try {
      const res = await getUnreadCount();
      if (res && res.data) {
        chatModalStore.updateWeiduCount(res.data);
      }
    } catch (error) {
      console.error("初始化时获取未读消息数量失败:", error);
    }
  }
});

// 在组件销毁时清理定时器和事件监听
onBeforeUnmount(() => {
  // 清理定时器
  if (connectionCheckInterval.value) {
    clearInterval(connectionCheckInterval.value);
  }
  
  if (unreadSyncInterval.value) {
    clearInterval(unreadSyncInterval.value);
  }
  
  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  
  cleanup();
});

// 清理函数，确保不会有残留效果
const cleanup = () => {
  // 不再完全断开Pusher连接，只解绑事件
  if (channel.value) {
    channel.value.unbind_all();
  }
  // 不断开Pusher连接，让它在后台继续监听消息
  // if (pusherInstance.value) {
  //   pusherInstance.value.disconnect();
  // }
};

// 手动重连Pusher
const reconnectPusher = () => {
  console.log('手动触发Pusher重连...');
  
  // 清理现有的事件绑定
  if (channel.value) {
    channel.value.unbind_all();
  }
  
  // 断开现有连接
  if (pusherInstance.value) {
    pusherInstance.value.disconnect();
    // 短暂延迟后重新连接
    setTimeout(() => {
      setupPusherAuth();
    }, 1000);
  } else {
    // 如果不存在实例，直接设置
    setupPusherAuth();
  }
};

// 在watch函数中调用重连方法
watch(
  () => props.isVisible,
  async (newVal) => {
    if (newVal) {
      document.body.style.overflow = "hidden"; // 防止滚动

      // 重置搜索模式
      isSearchMode.value = false;

      // 如果Pusher没有连接，尝试重连
      if (!pusherInstance.value || pusherInstance.value.connection.state !== 'connected') {
        reconnectPusher();
      }

      // Sync unread counts immediately when modal opens
      await syncUnreadCounts();
      await fetchContacts();

      // 优先显示 pinia 中的联系人
      if (chatModalStore.currentSupplier) {
        await initializeChat(chatModalStore.currentSupplier);
      } else if (contacts.value.length > 0) {
        await selectContact(contacts.value[0]);
      }
      nextTick(scrollToBottom);
      
      // Set up periodic sync while modal is open
      if (unreadSyncInterval.value) {
        clearInterval(unreadSyncInterval.value);
      }
      
      unreadSyncInterval.value = setInterval(async () => {
        if (props.isVisible) {
          await syncUnreadCounts();
        } else {
          clearInterval(unreadSyncInterval.value);
        }
      }, 30000); // Sync every 30 seconds
    } else {
      // 当弹窗被关闭时
      document.body.style.overflow = "auto"; // 恢复滚动

      // 重置搜索模式和搜索结果
      isSearchMode.value = false;

      // 触发关闭事件，确保父组件能够处理
      emit("close");
    }
  },
  { immediate: true }
);

// 在 setup 内部添加
defineExpose({
  initializeChat,
});

// 新增：外部调用，传入 supplier（如 avaterCart 传递的对象），置顶并选中
async function initializeChat(supplier) {
  if (!supplier) return;

  // 设置默认头像
  const avatarUrl =
    supplier.avatar ||
    "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/**********.jpg";

  // 先拉取联系人列表
  await fetchContacts();
  // 查找是否已存在
  let idx = contacts.value.findIndex((c) => c.id === supplier.id);
  if (idx === -1) {
    // 不存在则插入到第一位
    contacts.value.unshift({
      id: supplier.id,
      name: supplier.name || supplier.nickName || "未知用户",
      avatar: avatarUrl.startsWith("http")
        ? avatarUrl
        : import.meta.env.VITE_BASE_API + avatarUrl,
      isOnline: true,
      lastMessage: "",
      lastMessageTime: "",
      unreadCount: 0,
      userDetails: {
        companyName: supplier.companyName || "",
        introduction:
          supplier.introduction || supplier.businessIntroduction || "",
        memberType: supplier.memberType || "普通会员",
        businessCard: supplier.businessCard || "",
        businessLicense: supplier.businessLicense || "",
        email: supplier.email || "",
        qq: supplier.qq || "",
        wx: supplier.wx || "",
        phonenumber: supplier.phonenumber || "",
      },
    });
    idx = 0;
  } else if (idx > 0) {
    // 存在但不是第一位，移到第一位
    const contact = contacts.value.splice(idx, 1)[0];

    // 更新联系人详细信息（如果有新信息）
    if (supplier.introduction || supplier.businessIntroduction) {
      contact.userDetails = contact.userDetails || {};
      contact.userDetails.introduction =
        supplier.introduction ||
        supplier.businessIntroduction ||
        contact.userDetails.introduction;
      contact.userDetails.companyName =
        supplier.companyName || contact.userDetails.companyName;
      contact.userDetails.memberType =
        supplier.memberType || contact.userDetails.memberType;
    }

    contacts.value.unshift(contact);
    idx = 0;
  }
  selectedContact.value = contacts.value[idx];
  // console.log(selectedContact.value);
  await fetchHistory(supplier.id);
  // 只有在有历史消息时才标记已读
  if (messagesData.value[supplier.id]?.length > 0) {
    await markContactAsRead(supplier.id);
  }
  nextTick(scrollToBottom);
}

// 添加搜索联系人的方法
async function searchContact() {
  if (!searchQuery.value || searchQuery.value.trim() === "") {
    ElMessage({
      message: "请输入手机号或昵称",
      type: "warning",
      duration: 2000,
    });
    return;
  }

  try {
    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: "搜索中...",
      background: "rgba(0, 0, 0, 0.7)",
    });

    // 调用API查询联系人
    const res = await getphuserinfo(searchQuery.value.trim());

    // 关闭加载状态
    loading.close();

    if (res.code === 200 && res.data && res.data.length > 0) {
      // 如果找到联系人 - 处理所有返回的结果

      // 进入搜索模式
      isSearchMode.value = true;

      // 保存原始联系人列表
      if (originalContacts.value.length === 0) {
        originalContacts.value = [...contacts.value];
      }

      // 清空当前联系人列表，替换为搜索结果
      contacts.value = [];

      // 将所有搜索结果添加到联系人列表
      res.data.forEach((userData) => {
        contacts.value.push({
          id: userData.userId,
          name: userData.nickName || userData.userName || "未知用户",
          avatar: userData.avatar
            ? userData.avatar.startsWith("http")
              ? userData.avatar
              : import.meta.env.VITE_BASE_API + userData.avatar
            : "https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/**********.jpg",
          isOnline: true,
          lastMessage: "",
          lastMessageTime: "",
          unreadCount: 0,
          isSearchResult: true, // 标记为搜索结果
          userDetails: {
            companyName: userData.companyName || "",
            introduction:
              userData.introduction || userData.businessIntroduction || "",
            memberType: userData.memberType || "普通会员",
            businessCard: userData.businessCard || "",
            businessLicense: userData.businessLicense || "",
            email: userData.email || "",
            qq: userData.qq || "",
            wx: userData.wx || "",
            phonenumber: userData.phonenumber || "",
          },
        });
      });

      // 如果有搜索结果，选中第一个
      if (contacts.value.length > 0) {
        selectContact(contacts.value[0]);
      }

      ElMessage({
        message: `找到 ${contacts.value.length} 个联系人`,
        type: "success",
        duration: 2000,
      });

      // 清空搜索框
      searchQuery.value = "";
    } else {
      ElMessage({
        message: "未找到该手机号对应的用户",
        type: "error",
        duration: 2000,
      });
    }
  } catch (error) {
    console.error("搜索联系人失败:", error);
    ElMessage({
      message: "搜索失败，请稍后再试",
      type: "error",
      duration: 2000,
    });
  }
}

// 返回原始联系人列表
function returnToContacts() {
  if (isSearchMode.value && originalContacts.value.length > 0) {
    contacts.value = [...originalContacts.value];
    isSearchMode.value = false;

    // 如果有联系人，选中第一个
    if (contacts.value.length > 0) {
      selectContact(contacts.value[0]);
    }
  }
}

// Add these functions to better handle unread counts

// Synchronize unread counts with the server
const syncUnreadCounts = async () => {
  const currentUserId = userStore.userInfo.userId;
  if (!currentUserId) return;
  
  try {
    // 先获取未读消息数量
    const res = await getUnreadCount();
    
    if (res && res.code === 200 && res.data) {
      let contactsData = [];
      
      try {
        // 再获取联系人列表，以确定消息的方向
        const contactsRes = await getContacts();
        contactsData = contactsRes && contactsRes.data ? contactsRes.data : [];
      } catch (innerError) {
        console.error('获取联系人列表失败:', innerError);
        contactsData = [];
      }
      
      // 创建一个映射表，记录每个联系人的最后一条消息是否是自己发送的
      const lastMessageDirectionMap = {};
      contactsData.forEach(item => {
        if (item && item.userId) {
          lastMessageDirectionMap[item.userId] = item.lastMessageFromUserId === currentUserId;
        }
      });
      
      // 只处理真正的未读消息（来自其他用户的）
      let totalUnread = 0;
      
      // 更新各联系人未读消息数
      Object.entries(res.data).forEach(([contactId, count]) => {
        const unreadCount = parseInt(count);
        // 检查该联系人是否有方向信息，如果没有则假设不是自己发送的
        const isLastMessageFromSelf = lastMessageDirectionMap[contactId] === true;
        
        // 只有当消息不是自己发的，且未读数大于0时才计入
        if (!isNaN(unreadCount) && unreadCount > 0 && !isLastMessageFromSelf) {
          // 更新联系人未读消息数
          const contact = contacts.value.find(c => c.id === contactId);
          if (contact) {
            contact.unreadCount = unreadCount;
            // 确保记录这条消息不是自己发的
            contact.lastMessageIsOwn = false;
          }
          totalUnread += unreadCount;
        } else if (isLastMessageFromSelf) {
          // 如果最后一条消息是自己发的，则将未读数设为0
          const contact = contacts.value.find(c => c.id === contactId);
          if (contact) {
            contact.unreadCount = 0;
            contact.lastMessageIsOwn = true;
          }
        }
      });
      
      // 更新全局未读消息计数
      chatModalStore.updateWeiduCount(totalUnread);
    }
  } catch (error) {
    console.error('同步未读消息数量失败:', error);
  }
};

// Force reset all unread counts, including server-side
const forceResetUnreadCount = async () => {
  try {
    // First mark all contacts as read
    const resetPromises = contacts.value.map(contact => {
      return markContactAsRead(contact.id);
    });
    
    await Promise.all(resetPromises);
    
    // Reset all local counts to zero
    contacts.value.forEach(contact => {
      contact.unreadCount = 0;
    });
    
    // Reset global count
    await chatModalStore.updateWeiduCount(0);
    
    // Sync with server to ensure consistency
    await chatModalStore.syncUnreadCount();
    
    ElMessage({
      message: t('chatModal.unreadMessagesReset'),
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('强制重置未读消息计数失败:', error);
  }
};

// Modify the resetUnreadCounts function to use the new forceReset
const resetUnreadCounts = async () => {
  await forceResetUnreadCount();
};
</script>

<style scoped>
.chat-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  backdrop-filter: blur(4px);
  /* Ensure overlay doesn't block interactions when not visible */
  pointer-events: auto;
}

/* Ensure overlay doesn't block interactions when not visible */
/* .chat-modal-overlay[style*="display: none"],
.chat-modal-overlay:not([style*="display"]) {
  pointer-events: none;
} */

.chat-modal {
  width: 800px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.user-status {
  font-size: 12px;
  opacity: 0.8;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.8);
}

.chat-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 联系人侧边栏 */
.contacts-sidebar {
  width: 280px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.contacts-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.header-top {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.search-title {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #334155;
  background: linear-gradient(to right, #667eea, #764ba2);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 4px 0;
}

.return-icon {
  filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.2));
}

.return-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  margin-right: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(102, 126, 234, 0.4);
}

.return-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.6);
}

.return-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.4);
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #64748b;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 48px 10px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  background: white;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  min-width: 38px;
  height: 38px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(102, 126, 234, 0.4);
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.search-btn:hover {
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.6);
}

.search-btn:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.4);
}

.search-btn-icon {
  filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.2));
}

.contacts-list {
  flex: 1;
  overflow-y: auto;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f1f5f9;
}

.contact-item:hover {
  background: #f1f5f9;
}

.contact-item.active {
  background: #667eea;
  color: white;
}

.contact-avatar {
  position: relative;
  flex-shrink: 0;
}

.contact-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.contact-info {
  flex: 1;
  min-width: 0;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.contact-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: inherit;
}

.contact-time {
  font-size: 12px;
  opacity: 0.7;
}

.contact-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 13px;
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.unread-badge {
  background: #ef4444;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
  margin-left: 8px;
}

.contact-item.active .unread-badge {
  background: rgba(255, 255, 255, 0.3);
}

/* 聊天区域 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: white;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-direction: row; /* 默认左侧消息 */
}

.message-item.own-message {
  flex-direction: row-reverse; /* 自己的消息反向显示 */
}

.message-avatar {
  flex-shrink: 0;
}

.message-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

/* 消息内容样式 */
.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start; /* 默认左对齐 */
}

.own-message .message-content {
  align-items: flex-end; /* 自己的消息右对齐 */
}

/* 消息气泡样式 */
.message-bubble {
  background: #f1f5f9;
  padding: 12px 16px;
  border-radius: 18px 18px 18px 4px; /* 修改左下角为尖角 */
  position: relative;
}

.own-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 18px 18px 4px 18px; /* 修改右下角为尖角 */
}

/* 消息箭头样式 */
.message-bubble::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 12px;
  height: 12px;
  background: #f1f5f9;
  clip-path: polygon(100% 0, 100% 100%, 0 100%);
}

.own-bubble::before {
  left: auto;
  right: -8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(-1);
}

/* 消息时间 */
.message-time {
  font-size: 11px;
  color: #64748b;
  padding: 0 4px;
}

.chat-input-area {
  border-top: 1px solid #e2e8f0;
  background: white;
}

.input-toolbar {
  display: flex;
  gap: 8px;
  padding: 12px 20px 8px;
  border-bottom: 1px solid #f1f5f9;
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #f8fafc;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.input-container {
  display: flex;
  gap: 12px;
  padding: 12px 20px 16px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  font-family: inherit;
  line-height: 1.4;
}

.message-input:focus {
  border-color: #667eea;
}

.message-input::placeholder {
  color: #94a3b8;
}

.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.contacts-list::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.contacts-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.chat-messages::-webkit-scrollbar-thumb,
.contacts-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.contacts-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 会员徽章样式 */
.member-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  border: 2px solid white;
}

.super-member {
  background-color: #f59e0b;
}

.regular-member {
  background-color: #64748b;
}

.header-member-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  border: 2px solid white;
}

.avatar-container {
  position: relative;
}

.user-info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-company {
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
}

.user-contact-info {
  font-size: 11px;
  margin-top: 2px;
}

.contact-label {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 4px;
}

.contact-value {
  color: white;
}

/* 用户详细信息面板样式 */
.user-details-panel {
  position: absolute;
  top: 72px;
  right: 0;
  width: 300px;
  background: white;
  border-left: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.details-header h4 {
  margin: 0;
  font-size: 14px;
  color: #334155;
}

.close-details-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
}

.details-content {
  padding: 12px 16px;
  max-height: 300px;
  overflow-y: auto;
}

.details-row {
  margin-bottom: 10px;
}

.details-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
}

.details-value {
  font-size: 14px;
  color: #334155;
  word-break: break-word;
}

.super-member-text {
  color: #f59e0b;
  font-weight: 600;
}

/* 移动端横屏适配 */
@media screen and (max-width: 926px) and (orientation: landscape) {
  .chat-modal {
    width: 90vw;
    height: 80vh;
    margin: auto;
  }

  .contacts-sidebar {
    width: 200px;
  }

  .contact-item {
    padding: 8px;
  }

  .message-input {
    max-height: 60px;
  }

  .user-details-panel {
    width: 250px;
  }
}

.reset-btn {
  background: rgba(255, 255, 255, 0.2);
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  animation: spin 1s linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>