@import './base.css';

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}

/* 移动端强制横屏显示 */
@media (max-width: 900px) and (orientation: portrait) {
  html, body {
    width: 100vh;
    height: 100vw;
    min-width: 100vh;
    min-height: 100vw;
    overflow-x: auto;
    overflow-y: hidden;
    position: fixed;
    top: 0;
    left: 0;
    transform: rotate(90deg);
    transform-origin: left top;
    background: #fff;
  }
  #app {
    width: 100vh;
    height: 100vw;
    min-width: 100vh;
    min-height: 100vw;
    overflow-x: auto;
    overflow-y: hidden;
    display: block;
    padding: 0;
    margin: 0;
  }
}

/* 移动端横屏适配 */
@media (max-width: 900px) and (orientation: landscape) {
  html, body {
    width: 100vw;
    height: 100vh;
    min-width: 100vw;
    min-height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    transform: none;
    background: #fff;
  }
  #app {
    width: 100vw;
    height: 100vh;
    min-width: 100vw;
    min-height: 100vh;
    overflow: hidden;
    display: block;
    padding: 0;
    margin: 0;
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  /* 强制横屏 */
  html, body {
    height: 100vh;
    overflow: hidden;
  }

  /* 基础布局适配 */
  .container {
    width: 100%;
    padding: 10px;
  }

  /* 字体大小调整 */
  body {
    font-size: 14px;
  }

  h1 { font-size: 20px; }
  h2 { font-size: 18px; }
  h3 { font-size: 16px; }

  /* 按钮和输入框放大点击区域 */
  button, 
  input,
  .select-container {
    min-height: 44px;
    min-width: 44px;
  }

  /* 间距调整 */
  .button-group {
    gap: 8px;
  }

  /* 弹窗适配 */
  .modal-container {
    width: 90%;
    max-height: 80vh;
  }
}

/* 横屏模式特殊处理 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .route-info-container {
    max-width: 300px;
  }
  
  .map-tools {
    right: 10px;
  }
  
  .nav-container {
    top: 10px;
    right: 10px;
  }
}

/* 移动端横屏适配基础样式 */
@media screen and (max-width: 926px) and (orientation: landscape) {
  html, body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  #app {
    width: 100vw;
    height: 100vh;
    max-width: none;
    margin: 0;
    padding: 0;
  }

  /* 调整基础字体大小 */
  :root {
    --base-font-size: 14px;
    --small-font-size: 12px;
  }

  /* 基础间距调整 */
  :root {
    --base-spacing: 8px;
    --large-spacing: 16px;
  }

  /* 基础点击区域大小 */
  button, 
  input[type="button"],
  .clickable {
    min-height: 36px;
    min-width: 36px;
  }

  /* 滚动条美化 */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
}
