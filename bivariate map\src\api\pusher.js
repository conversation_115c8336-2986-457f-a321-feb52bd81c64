import request from '../utils/request'


export function authenticate<PERSON>usher(data) {
  return request.post('/chat/auth', data)
}

export function sendMessage(data) {
  return request.post('/chat/send', data)
}

export function getChatHistory(withUserId) {
  return request.get('/chat/history', { params: { withUserId } })
}

export function getContacts() {
  return request.get('/chat/contacts')
}

export function getUnreadCount() {
  return request.get('/chat/unread-count') 
}

export function markAsRead(toUserId) {
    return request({
        url:'/chat/mark-as-read?fromUserId=' + toUserId,
        method: 'get'
    })
}

export function getphuserinfo(phone){
  return request({
    url:'/chat/findContact?keyword=' + phone,
    method: 'get'
  })
}

//随机获取客服
export function getRandomCustomer() {
  return request({
    url:'/chat/findCustomer',
    method:'get'
  })
}