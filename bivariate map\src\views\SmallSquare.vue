<template>
  <div class="plaza-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <!-- 左侧Logo -->
        <div class="header-left">
          <img src="../../public/image/newMaptitlelogo.png" alt="New Map" class="logo-image">
        </div>

        <!-- 中间区域 - 搜索框 (确保显示) -->
        <div class="header-center">
          <div class="search-container">
            <input type="text" class="search-input" v-model="searchQuery.keyword" :placeholder="t('plaza.searchPlaceholder')"
              @keydown.enter="performSearch">
            <button class="search-button" @click="performSearch">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </button>
            <button v-if="searchQuery.keyword" class="clear-search-button" @click="clearSearch">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- 右侧按钮 - 修改样式 -->
        <div class="header-right">
          <button class="publish-button" @click="handleCreatePost">
            <span class="icon-plus">+</span>
            <span>{{ t('plaza.publish') }}</span>
          </button>
          <button class="exit-button" @click="router.push('/')">
            <span class="icon-close">×</span>
            <span style="margin-top: -2px;">{{ t('plaza.exitPlaza') }}</span>
          </button>
        </div>
      </div>
    </header>

    <!-- 分类标签栏 -->
    <div class="category-tabs">
      <!-- 标签列表 -->
      <div class="tab-group">
        <div class="tab-group-title">{{ t('plaza.tags') }}:</div>
        <div class="tab" :class="{ active: !selectedTag }" @click="selectTag(null)">
          {{ t('plaza.all') }}
        </div>
        <div v-for="tag in tagList" :key="tag.tid" class="tab"
          :class="{ active: selectedTag && selectedTag.tid === tag.tid }" @click="selectTag(tag)">
          {{ tag.name }}
        </div>
      </div>
    </div>

    <!-- 帖子列表 -->
    <div class="posts-container">
      <div class="card-grid">
        <ArticleCard v-for="post in filteredPosts" :key="post.id" :article="post">
          <!-- 添加置顶标识插槽 -->
          <template v-if="Number(post.isTop) === 1" #topBadge>
            <div class="top-badge">{{ t('plaza.topPost') }}</div>
          </template>
        </ArticleCard>
      </div>
      
      <div v-if="filteredPosts.length === 0 && !isLoading" class="no-results">
        <div class="empty-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            <line x1="8" y1="11" x2="14" y2="11"></line>
          </svg>
        </div>
        <h3 class="empty-search-title">{{ searchQuery.keyword ? t('plaza.searchResults.notFound') : t('plaza.searchResults.noContent') }}</h3>
        <p class="empty-search-tip">{{ searchQuery.keyword ? t('plaza.searchResults.tryOtherKeywords', { keyword: searchQuery.keyword }) : t('plaza.searchResults.noContent') }}</p>
      </div>

      <!-- 加载更多容器 -->
      <div v-if="filteredPosts.length > 0" class="loading-more-container">
        <!-- 加载中动画 -->
        <div v-if="isLoading" class="loading-content">
          <div class="loading-spinner"></div>
          <span class="loading-text">{{ t('plaza.loading') || '加载中...' }}</span>
        </div>
        <!-- 全部加载完成提示 -->
        <div v-else-if="allLoaded" class="all-loaded">
          <span class="all-loaded-text">{{ t('plaza.allLoaded') || '已加载全部内容' }}</span>
        </div>
        <!-- 加载更多提示 -->
        <div v-else class="load-more-hint">
          <span class="load-more-text">{{ t('plaza.scrollToLoadMore') || '向下滑动加载更多' }}</span>
        </div>
      </div>
      </div>

    <!-- 帖子详情弹窗 -->
    <div v-if="selectedPostDetail" class="post-detail-modal" @click="closePostDetail">
      <div class="post-detail-container" @click.stop>
        <button class="close-button" @click="closePostDetail">&times;</button>
        <div class="post-detail-header">
          <div class="post-user-info" @click="showSupplierInfo(selectedPostDetail.supplier)">
            <div class="user-avatar-container">
              <img :src="getAvatarUrl(selectedPostDetail.userAvatar)" :alt="t('plaza.supplierInfo.contactPerson')" class="user-avatar">
              <!-- 根据会员等级显示VIP徽章 -->
              <div v-if="selectedPostDetail.memberType === '普通会员'" class="vip-badge vip-regular">
                VIP
              </div>
              <div v-else-if="selectedPostDetail.memberType === '超级会员'" class="vip-badge vip-super">
                SVIP
              </div>
            </div>
            <div class="user-info">
              <span class="username">{{ selectedPostDetail.nickName || selectedPostDetail.phone || selectedPostDetail.username || t('plaza.notProvided') }}</span>
              <span class="company-name" v-if="selectedPostDetail.supplier">{{ selectedPostDetail.supplier.companyName || t('plaza.notProvided') }}</span>
            </div>
          </div>
          <div class="post-title-container">
            <h2 class="post-title">{{ selectedPostDetail.title }}</h2>
            <div class="post-tags">
              <span v-for="tag in selectedPostDetail.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>
          <div class="post-meta">
            <span class="post-time">{{ formatTime(selectedPostDetail.time) }}</span>
            <span v-if="selectedPostDetail.location" class="post-location">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              {{ selectedPostDetail.location }}
            </span>
          </div>
        </div>
        <div class="post-detail-content">
          <p class="content-text">{{ selectedPostDetail.content }}</p>
          <div v-if="selectedPostDetail.images && selectedPostDetail.images.length > 0"
            :class="['post-images', getImageLayoutClass(selectedPostDetail.images.length)]">
            <div v-for="(image, index) in selectedPostDetail.images" :key="index" class="image-wrapper"
              @click="previewImage(selectedPostDetail.images, index)">
              <img :src="getImageUrl(image.imgUrl || image.url)" :alt="image.description || t('plaza.postImage')" class="post-image">
            </div>
          </div>
          <div class="business-info" v-if="selectedPostDetail.introduction">
            <div class="info-row">
              <span class="info-label">{{ t('plaza.companyIntro') }}:&nbsp;&nbsp;</span>
              <span class="business-intro" :title="selectedPostDetail.introduction">
                {{ truncatedPostBusinessIntro }}
              </span>
              <!-- 根据会员等级显示VIP徽章 -->
              <div v-if="selectedPostDetail.memberType === '普通会员'" class="vip-badge vip-regular" style="position:relative; bottom: auto; right: auto; margin-left: 8px; animation: none;">
                VIP
              </div>
              <div v-else-if="selectedPostDetail.memberType === '超级会员'" class="vip-badge vip-super" style="position:relative; bottom: auto; right: auto; margin-left: 8px; animation: none;">
                SVIP
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <div v-if="previewState.show" class="image-preview-modal" @click="closeImagePreview">
      <button class="preview-close" @click="closeImagePreview">×</button>
      <div class="preview-container" @click.stop>
        <img :src="getImageUrl(currentPreviewImage.imgUrl || currentPreviewImage.url)" :alt="currentPreviewImage.description || t('plaza.previewImage')" class="preview-image">
        <div v-if="previewState.images.length > 1" class="preview-controls">
          <button class="preview-button" @click="prevImage" :disabled="previewState.currentIndex === 0">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <span class="preview-counter">{{ previewState.currentIndex + 1 }} / {{ previewState.images.length }}</span>
          <button class="preview-button" @click="nextImage"
            :disabled="previewState.currentIndex === previewState.images.length - 1">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 帖子菜单弹窗 -->
    <div v-if="showMenu" class="post-menu-modal" @click="closePostMenu">
      <div class="menu-container" @click.stop>
        <div class="menu-item" v-if="selectedPost && selectedPost.userId === currentUserId">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          <span>{{ t('plaza.edit') }}</span>
        </div>
        <div class="menu-item" v-if="selectedPost && selectedPost.userId === currentUserId">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
            stroke="#ff4d4f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
          </svg>
          <span class="delete-text">{{ t('plaza.delete') }}</span>
        </div>
        <div class="menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
            <line x1="4" y1="22" x2="4" y2="15"></line>
          </svg>
          <span>{{ t('plaza.report') }}</span>
        </div>
      </div>
    </div>

    <!-- 优惠券礼盒弹窗 -->
    <VoucherGiftModal v-if="showVoucherModal" :voucherData="currentVoucher" :closeOnOverlayClick="true"
      @close="closeVoucherModal" @accept="useVoucher" />

    <!-- 供应商信息弹窗 -->
    <div v-if="selectedSupplier" class="supplier-modal" @click="closeSupplierInfo">
      <div class="supplier-card" @click.stop>
        <button class="close-button" @click="closeSupplierInfo">&times;</button>
        <div class="user-info-content-new">
          <!-- 用户信息区域重新布局：头像在左侧，信息在右侧 -->
          <div class="user-info-layout">
            <!-- 供应商头像 -->
            <div class="user-avatar-container">
              <img :src="getAvatarUrl(selectedSupplier.logo)" :alt="t('plaza.supplierInfo.title')" class="user-avatar-new">
              <!-- 根据会员等级显示VIP徽章 -->
              <div v-if="selectedSupplier.memberType === '普通会员'" class="vip-badge vip-regular">
                {{ t('avaterCart.normalMember') }}
              </div>
              <div v-else-if="selectedSupplier.memberType === '超级会员'" class="vip-badge vip-super">
                {{ t('avaterCart.superMember') }}
              </div>
            </div>
            
            <!-- 供应商基本信息 -->
            <div class="user-info-side">
              <!-- 名字和职位行 -->
              <div class="info-row name-row">
                <span class="user-name-new">{{ selectedSupplier.contactPerson || t('plaza.notProvided') }}</span>
                <span class="post-info" style="margin-left: -5rem;">{{ t('plaza.supplierInfo.position') }}: {{ article.post || t('plaza.notProvided') }}</span>
              </div>
              <div class="business-intro" style="margin-left: -1px;" :title="selectedSupplier.businessScope">{{ t('plaza.supplierInfo.businessScope') }}：{{ truncatedBusinessScope }}</div>
            </div>
          </div>
          
          <div class="user-info-rows">
            <!-- 公司行 -->
            <div class="info-row">
              <span class="info-label">{{ t('plaza.supplierInfo.companyName') }}:&nbsp;&nbsp;  {{ selectedSupplier.companyName || t('plaza.notProvided') }}</span>
            </div>  
            
            <!-- 电话行 -->
            <div class="info-row">
              <span class="info-label">{{ t('plaza.supplierInfo.phone') }}:&nbsp;&nbsp;{{ selectedSupplier.phone || t('plaza.notProvided') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { getTag, getArticleList } from '@/api/SmallSquare';
import axios from 'axios';
import ArticleCard from '@/components/ArticleCard.vue';
import { useUserStore } from '@/store/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import request from '@/utils/request';
import VoucherGiftModal from '@/components/VoucherGiftModal.vue';
import { getvoucher } from '@/api/voucher';
import { useI18n } from '@/composables/useI18n'; // Import i18n composable
import imageCache from '@/utils/imageCache';

const { t } = useI18n(); // Initialize i18n
const router = useRouter();
const userStore = useUserStore();
const baseUrl = import.meta.env.VITE_BASE_API || '';
const posts = ref([]);

// 获取头像URL的函数 - 使用缓存服务
const getAvatarUrl = (avatarUrl) => {
  return imageCache.getAvatarUrl(avatarUrl, baseUrl);
};

// 获取图片URL的函数 - 使用缓存服务
const getImageUrl = (imgUrl) => {
  return imageCache.getImageUrl(imgUrl, baseUrl);
};
const previewImageUrl = ref(null);
const showMenu = ref(false);
const selectedPost = ref(null);
const selectedPostDetail = ref(null); // 新增：当前选中查看详情的帖子
const currentUserId = ref(1); // 模拟当前用户ID
const showVoucherModal = ref(false); // 优惠券弹窗状态
const currentVoucher = ref(null); // 优惠券数据
const searchQuery = ref({
  keyword: '', // 使用keyword匹配后端API
  category: '',
  tagIds: [],
});
const selectedSupplier = ref(null);

// 添加懒加载相关的ref
const pageNum = ref(1);
const pageSize = ref(15); // 每次加载15条
const total = ref(0);
const isLoading = ref(false);
const allLoaded = ref(false);
const loadingObserver = ref(null); // 用于观察加载更多元素

// 计算属性：截断的主营业务
const truncatedBusinessScope = computed(() => {
  if (!selectedSupplier.value || !selectedSupplier.value.businessScope) return '';
  return selectedSupplier.value.businessScope.length > 10
    ? selectedSupplier.value.businessScope.substring(0, 10) + '...'
    : selectedSupplier.value.businessScope;
});

// 计算属性：截断的帖子公司简介
const truncatedPostBusinessIntro = computed(() => {
  if (!selectedPostDetail.value || !selectedPostDetail.value.introduction) return '';
  return selectedPostDetail.value.introduction.length > 10
    ? selectedPostDetail.value.introduction.substring(0, 10) + '...'
    : selectedPostDetail.value.introduction;
});

// 定义不同类别的标签
const methodTabs = ref([]);
const regionTabs = ref([]);

// 选中的标签（每个类别只能选一个）
const selectedTags = ref({
  method: '',
  region: ''
});

// 新的标签数据结构
const tagList = ref([]);
const selectedTag = ref(null);

// 选择标签
const selectTag = (tag) => {
  selectedTag.value = tag;
  resetAndSearch();
};

// 检查用户是否是会员
const checkMemberStatus = () => {
  const memberType = userStore.userInfo?.memberType;
  return memberType === '普通会员' || memberType === '超级会员';
};

// 处理发布按钮点击
const handleCreatePost = async () => {
  // 首先检查用户是否登录
  if (!userStore.isLogin) {
    ElMessage.warning('请先登录后再发布内容');
    userStore.showLoginDialog = true;
    return; // 未登录时只提示登录，不进行任何跳转
  }

  // 用户已登录，继续检查会员状态
  try {
    // 调用会员类型查询接口
    const memberRes = await request({
      url: '/userMember/findUserMemberType',
      method: 'get'
    });

    console.log('会员状态查询结果:', memberRes);

    if (memberRes.code === 200 &&
      (memberRes.data === '普通会员' || memberRes.data === '超级会员')) {
      // 是会员，直接跳转到发布页面
      router.push('/create-post');
    } else {
      // 不是会员，使用Element UI弹窗提示（只有确认按钮）
      ElMessageBox.alert(
        t('plaza.membersOnlyMessage'),
        t('plaza.tip'),
        {
          confirmButtonText: t('plaza.confirm'),
          type: 'info',
          callback: () => {
            // 用户点击确认，有100%概率获取优惠券
            const randomValue = Math.random();
            console.log('优惠券随机值:', randomValue);
            const shouldGetVoucher = randomValue < 1.0; // 改为100%概率
            if (shouldGetVoucher) {
              handleMemberConfirm();
            } else {
              console.log('未触发优惠券发放，随机值为:', randomValue);
            }
          }
        });
    }
  } catch (error) {
    console.error('会员状态查询失败:', error);
    ElMessage({
      message: t('plaza.systemBusy'),
      type: 'error',
      duration: 3000
    });
  }
};

// 处理用户确认开通会员（已确定有100%的概率触发）
const handleMemberConfirm = async () => {
  if (userStore.userInfo?.userId) {
    try {
      // 调用优惠券接口
      const voucherRes = await getvoucher({
        userId: userStore.userInfo.userId,
        issuanceSource: '小广场'
      });

      console.log('优惠券获取结果:', voucherRes);

      if (voucherRes.code === 200 && voucherRes.data) {
        // 存储优惠券数据并显示弹窗
        currentVoucher.value = voucherRes;
        showVoucherModal.value = true;
      }
    } catch (error) {
      console.error('获取优惠券失败:', error);
    }
  }
};

// 关闭优惠券弹窗
const closeVoucherModal = () => {
  showVoucherModal.value = false;
  // 不跳转，只关闭弹窗
};

// 使用优惠券（跳转到会员页面）
const useVoucher = () => {
  showVoucherModal.value = false;
  // 跳转到会员页面
  router.push('/member');
};

// 清空搜索框
const clearSearch = () => {
  searchQuery.value.keyword = '';
  resetAndSearch();
};

// 移除了handleSearch、handleSearchBlur和selectSuggestion函数，因为现在不再需要搜索建议功能

// 执行搜索 - 点击搜索按钮触发
const performSearch = () => {
  // 确保搜索内容不为空时才进行搜索
  if (searchQuery.value.keyword.trim()) {
    console.log('执行搜索，关键词:', searchQuery.value.keyword);
    resetAndSearch();
  } else {
    console.log('搜索关键词为空，不执行搜索');
  }
};

// 获取文章列表
const fetchArticles = async (reset = false) => {
  // 如果已经全部加载完成且不是重置操作，则不再请求
  if (allLoaded.value && !reset) {
    return;
  }

  // 如果是重置操作，重置页码和状态
  if (reset) {
    pageNum.value = 1;
    pageSize.value = 15; // 重置pageSize为初始值
    posts.value = [];
    allLoaded.value = false;
  } else {
    // 懒加载时增加pageSize
    pageSize.value += 15;
  }

  // 设置加载状态
  isLoading.value = true;

  try {
    // 构建查询参数，匹配后端 QueryArticleDto 结构
    const params = {
      // 关键字搜索 (可以同时匹配标题或内容)
      keyword: searchQuery.value.keyword || undefined,

      // 分类条件
      ...(searchQuery.value.category ? { category: searchQuery.value.category } : {}),

      // 标签条件 - 确保使用正确的tagIds数组格式
      ...(selectedTag.value ? { tagIds: [selectedTag.value.tid] } : {}),

      // 分页参数 - 始终使用第1页，但增加pageSize
      pageNum: 1,
      pageSize: pageSize.value
    };

    console.log('查询参数:', params);
    const response = await getArticleList(params);
    console.log('API响应:', response);

    // 检查响应是否有效且包含rows数据
    if (response && response.rows) {
      // 处理每篇文章的isTop值，确保是数字类型
      response.rows.forEach(article => {
        // 确保isTop字段是数字类型
        if (article.isTop !== undefined) {
          // 使用Number转换确保isTop是数字类型
          article.isTop = Number(article.isTop);
        }
      });

      // 直接替换数据，因为我们总是请求第1页但增加pageSize
      posts.value = response.rows;

      total.value = response.total || 0; // 更新总数
      console.log('获取文章列表成功:', response.rows.length, '条数据，当前pageSize:', pageSize.value);

      // 判断是否已加载全部数据
      // 如果这次请求的pageSize大于实际返回的数据量，说明没有更多数据了
      // 或者如果返回的数据量等于总数且小于当前pageSize，也说明没有更多数据了
      if (response.rows.length < pageSize.value) {
        allLoaded.value = true;
        console.log('已加载全部数据，请求pageSize:', pageSize.value, '实际返回:', response.rows.length, '总数:', total.value);
      } else {
        allLoaded.value = false;
        console.log('可能还有更多数据，请求pageSize:', pageSize.value, '实际返回:', response.rows.length, '总数:', total.value);
      }

      if (response.rows.length === 0 && reset) {
        console.warn('文章列表为空');
      }
    } else {
      console.error('获取文章列表失败:', response ? response.msg : '无响应');

      // 如果是401错误（未登录），不显示错误提示
      if (response && response.code !== 401) {
        ElMessage.error(response.msg || '获取文章列表失败');
      }

      if (reset) {
        posts.value = [];
      }

      allLoaded.value = true;
    }
  } catch (error) {
    console.error('获取文章列表出错:', error);

    // 如果是401错误（未登录），不显示错误提示
    if (error.code !== 401) {
      ElMessage.error('获取文章列表失败: ' + (error.message || '未知错误'));
    }

    if (reset) {
      posts.value = [];
    }

    allLoaded.value = true;
  } finally {
    isLoading.value = false;
  }
};

// 过滤后的帖子列表
const filteredPosts = computed(() => {
  // 如果posts为空，返回空数组，让页面显示"没有找到相关内容"提示
  if (!posts.value || posts.value.length === 0) {
    return [];
  }

  let result = [...posts.value]; // 创建副本，避免修改原始数据
  
  // 先根据置顶状态排序，再根据发布时间排序
  result = result.sort((a, b) => {
    // 先比较置顶状态（isTop: 1置顶, 0未置顶）
    // 确保转换为数字进行比较
    const topA = Number(a.isTop) || 0;
    const topB = Number(b.isTop) || 0;
    
    if (topA !== topB) {
      // 置顶的排在前面
      return topB - topA;
    }
    
    // 置顶状态相同，则按照发布时间排序（最新发布的在最前面）
    return new Date(b.createTime) - new Date(a.createTime);
  });

  return result;
});

// 防抖函数
function debounce(fn, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return t('plaza.dateFormat', {
    year,
    month,
    day,
    hours,
    minutes
  });
};

// 显示帖子菜单
const showPostMenu = (post) => {
  selectedPost.value = post;
  showMenu.value = true;
};

// 关闭帖子菜单
const closePostMenu = () => {
  showMenu.value = false;
  selectedPost.value = null;
};

// 预览状态
const previewState = ref({
  show: false,
  images: [],
  currentIndex: 0
});

// 获取当前预览图片
const currentPreviewImage = computed(() => {
  if (!previewState.value.show || previewState.value.images.length === 0) {
    return { url: '', description: '' };
  }
  return previewState.value.images[previewState.value.currentIndex];
});

// 获取图片布局类名
const getImageLayoutClass = (count) => {
  switch (count) {
    case 1: return 'single';
    case 2: return 'double';
    default: return 'triple';
  }
};

// 预览图片
const previewImage = (images, startIndex = 0) => {
  previewState.value = {
    show: true,
    images: images,
    currentIndex: startIndex
  };
};

// 关闭预览
const closeImagePreview = () => {
  previewState.value.show = false;
};

// 上一张图片
const prevImage = () => {
  if (previewState.value.currentIndex > 0) {
    previewState.value.currentIndex--;
  }
};

// 下一张图片
const nextImage = () => {
  if (previewState.value.currentIndex < previewState.value.images.length - 1) {
    previewState.value.currentIndex++;
  }
};

// 显示供应商信息
const showSupplierInfo = (supplier) => {
  selectedSupplier.value = supplier;
};

// 关闭供应商信息
const closeSupplierInfo = () => {
  selectedSupplier.value = null;
};

// 查看帖子详情 - 跳转到新页面
const viewPostDetail = (post) => {
  // 处理文章详情数据
  if (post.articleImgList && post.articleImgList.length > 0) {
    // 将articleImgList转换为images数组
    post.images = post.articleImgList.map(img => ({
      imgUrl: img.imgUrl,
      url: getImageUrl(img.imgUrl), // 使用getImageUrl处理图片路径
      description: post.title || t('plaza.postImage')
    }));
  }
  
  // 设置当前选中的文章详情
  selectedPostDetail.value = post;
  
  // 跳转到详情页面
  router.push({
    path: `/article-detail/${post.id}`,
    // 可以通过query或params传递一些基本信息，减少详情页的加载时间
    query: {
      title: post.title,
      createTime: post.createTime
    }
  });
};

// 关闭帖子详情
const closePostDetail = () => {
  selectedPostDetail.value = null;
};

// 设置交叉观察器，监听滚动加载
const setupIntersectionObserver = () => {
  // 如果已经存在观察器，先销毁
  if (loadingObserver.value) {
    loadingObserver.value.disconnect();
  }
  
  // 创建新的观察器
  loadingObserver.value = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      console.log('IntersectionObserver触发:', {
        isIntersecting: entry.isIntersecting,
        isLoading: isLoading.value,
        allLoaded: allLoaded.value,
        pageSize: pageSize.value,
        postsLength: posts.value.length,
        total: total.value
      });

      // 如果加载更多元素进入视口，并且不在加载中，且未全部加载完成
      if (entry.isIntersecting && !isLoading.value && !allLoaded.value) {
        console.log('触发加载更多，当前pageSize:', pageSize.value);
        fetchArticles();
      }
    });
  }, {
    root: null, // 使用视口作为根
    rootMargin: '0px 0px 100px 0px', // 提前100px触发
    threshold: 0.1 // 当10%的目标元素可见时触发
  });
  
  // 开始观察加载更多容器
  const observeContainer = () => {
    const loadingContainer = document.querySelector('.loading-more-container');
    console.log('查找加载容器:', loadingContainer);
    if (loadingContainer && loadingObserver.value) {
      loadingObserver.value.observe(loadingContainer);
      console.log('开始观察加载容器，当前状态 - isLoading:', isLoading.value, 'allLoaded:', allLoaded.value);
    } else {
      console.log('容器未找到，1秒后重试');
      setTimeout(observeContainer, 1000);
    }
  };

  // 延迟观察，确保DOM已更新
  setTimeout(observeContainer, 100);
};

// 处理搜索或标签筛选时的重置
const resetAndSearch = () => {
  fetchArticles(true); // 重置并重新加载
  // 重新设置观察器
  setTimeout(() => {
    setupIntersectionObserver();
  }, 100);
};

// 初始化
onMounted(() => {
  console.log('小广场页面 - 组件挂载');

  // 获取标签列表 - 不传categoryId参数来获取所有标签
  getTag().then(response => {
    console.log('小广场页面 - 标签数据:', response);

    if (response && response.code === 200 && response.rows && response.rows.length > 0) {
      // 处理标签数据
      tagList.value = response.rows;
      console.log('小广场页面 - 处理后的标签数据:', tagList.value);
    } else {
      // 如果API失败，使用硬编码的标签数据
      console.log('小广场页面 - API返回无效，使用硬编码数据');
      tagList.value = [
        { name: '铁路', tid: 1 },
        { name: '汽运', tid: 2 },
        { name: '地区', tid: 3 }
      ];
    }
  }).catch(error => {
    console.error('小广场页面 - 获取标签失败:', error);

    // 如果不是401错误，显示错误提示
    if (error.code !== 401) {
      ElMessage.error('获取标签列表失败，使用默认标签');
    }

    // 使用硬编码的标签数据作为备份
    tagList.value = [
      { name: '铁路', tid: 1 },
      { name: '汽运', tid: 2 },
      { name: '地区', tid: 3 }
    ];
  });

  // 获取文章列表 - 初始不带任何分类和标签筛选
  fetchArticles(true);

  // 设置滚动观察器
  setupIntersectionObserver();

  // 调试信息
  setTimeout(() => {
    console.log('页面加载完成后的状态:', {
      postsLength: posts.value.length,
      isLoading: isLoading.value,
      allLoaded: allLoaded.value,
      total: total.value,
      pageSize: pageSize.value
    });
  }, 2000);
});

// 组件卸载前清理观察器
onBeforeUnmount(() => {
  if (loadingObserver.value) {
    loadingObserver.value.disconnect();
    loadingObserver.value = null;
  }
});
</script>

<style scoped>
.plaza-container {
  width: 80%;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding-top: 80px;
  /* 为固定定位的header预留空间 */
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 64px;
}

.header-content {
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
  width: 180px;
}

.logo-image {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: transform 0.3s;
}

.logo-image:hover {
  transform: scale(1.05);
}



.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 15px;
  max-width: 500px;
}

.search-container {
  position: relative;
  width: 100%;
  display: flex;
  height: 36px;
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  width: 85%;
  padding: 0 16px;
  border: none;
  border-radius: 24px 0 0 24px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s;
  background-color: #f5f7fa;
  color: #606266;
  display: block;
  height: 36px;
}

.search-input:focus {
  background-color: #f5f7fa;
}

.search-input::placeholder {
  color: #909399;
}

.search-button {
  width: 15%;
  height: 36px;
  background-color: #8a7dff;
  border: none;
  border-radius: 0 24px 24px 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  transition: background-color 0.3s;
}

.search-button:hover {
  background-color: #766ade;
}

.clear-search-button {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
  z-index: 5;
  width: 18px;
  height: 18px;
  padding: 0;
}

.clear-search-button:hover {
  color: #606266;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.2s;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background: #f5f7fa;
}

.suggestion-text {
  font-size: 14px;
  color: #333;
}

.suggestion-category {
  font-size: 12px;
  color: #999;
  padding: 2px 8px;
  background: #f0f0f0;
  border-radius: 10px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  min-width: 200px;
}

.publish-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  padding: 9px 18px;
  border-radius: 24px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.35);
  height: 40px;
}

.publish-button:hover {
  background: linear-gradient(135deg, #5258ef, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.45);
}

.icon-plus {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.25);
  margin-right: 2px;
}

.exit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  padding: 9px 18px;
  border-radius: 24px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.35);
  height: 40px;
}

.exit-button:hover {
  background: linear-gradient(135deg, #5258ef, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.45);
}

.icon-close {
  font-size: 18px;
  line-height: 1;
  font-weight: bold;
  display: inline-block;
}

/* 调整分类标签的位置 */
.category-tabs {
  position: sticky;
  top: 64px;
  z-index: 999;
  background: white;
  width: 100%;
  border-bottom: 1px solid #eee;
  padding: 16px;
  overflow-x: auto;
}

.tab-group {
  display: flex;
  align-items: center;
  gap: 12px;
  white-space: nowrap;
}

.tab-group-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.tab {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  background: #f5f7fa;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  user-select: none;
}

.tab.active {
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
}

.tab:hover:not(.active) {
  background: #edf2f7;
  transform: translateY(-2px);
}

.post-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #1890ff;
  background: #e6f7ff;
}

.posts-container {
  padding: 16px;
  margin-bottom: 20px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-image {
  position: relative;
  width: 100%;
  padding-top: 56.25%;
  /* 16:9 比例 */
  overflow: hidden;
  background-color: #f5f7fa;
}

.card-thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-no-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.card-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  height: 2.8em;
}

.card-user {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.card-username {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.tag-more {
  font-size: 12px;
  color: #999;
}

.card-meta {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-time {
  font-size: 12px;
  color: #999;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  width: 100%;
}

.empty-search-icon {
  margin-bottom: 20px;
  color: #c0c4cc;
  background-color: #f5f7fa;
  border-radius: 50%;
  padding: 20px;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-search-title {
  font-size: 20px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: 500;
}

.empty-search-tip {
  font-size: 14px;
  color: #909399;
  max-width: 300px;
}

/* 帖子详情弹窗 */
.post-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  overflow-y: auto;
  padding: 20px;
}

.post-detail-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  padding: 24px;
}

/* 为帖子详情添加特定的关闭按钮样式 */
.post-detail-container .close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  font-weight: bold;
  z-index: 10;
}

.post-detail-header {
  margin-bottom: 20px;
}

.post-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.user-avatar-container {
  position: relative;
  width: 60px;
  height: 60px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.company-name {
  color: #666;
  font-size: 14px;
}

.post-detail-content {
  margin-bottom: 20px;
}

.preview-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 10px;
  z-index: 1;
}

.preview-close:hover {
  opacity: 0.8;
}

.post-menu-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.menu-container {
  background: white;
  border-radius: 12px;
  width: 80%;
  max-width: 300px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.menu-item:hover {
  background: #f5f7fa;
}

.delete-text {
  color: #ff4d4f;
}

/* 供应商信息弹窗样式 */
.supplier-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.supplier-card {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 16px;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.user-info-content-new {
  padding: 15px 10px;
}

.user-info-layout {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
  gap: 15px;
}

.user-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar-new {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-info-side {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.vip-badge {
  position: absolute;
  bottom: 0;
  right: -5px;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 20;
  line-height: 1;
  min-width: 18px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #fbbf24;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border-color: #fbbf24;
  animation: svip-glow 2s ease-in-out infinite alternate;
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(59, 130, 246, 0.5);
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(251, 191, 36, 0.5);
  }
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-row {
  margin-bottom: 5px;
}

.user-name-new {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.user-title {
  font-size: 14px;
  color: #666;
  margin-left: 10px;
}

.intro-row {
  margin-bottom: 10px;
}

.business-intro {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
  position: relative;
  cursor: default;
}

.business-intro[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-label {
  color: #666;
  min-width: 70px;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-size: 14px;
}

.info-id {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  font-weight: bold;
  z-index: 10;
}

@media (max-width: 1200px) {
  .plaza-container {
    width: 90%;
  }

  .header-content {
    width: 90%;
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .plaza-container {
    width: 100%;
    padding-top: 64px;
  }

  .header-content {
    width: 100%;
    padding: 0 12px;
  }

  .header {
    padding: 12px 0;
  }

  .header-left {
    width: auto;
  }

  .logo-image {
    height: 32px;
    margin-right: 5px;
  }



  .header-center {
    margin: 0 12px;
    max-width: none;
  }

  .publish-button {
    padding: 8px 12px;
  }

  .exit-button {
    padding: 8px 12px;
  }

  .posts-container {
    padding: 12px;
  }

  .card-grid {
    grid-template-columns: 1fr;
  }

  .post-detail-container {
    width: 95%;
    padding: 16px;
  }

  .header-right {
    display: flex;
    flex-shrink: 0;
  }

  .search-container {
    display: block;
    /* 确保移动端也显示 */
  }
}

.business-info {
  margin-top: 16px;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.loading-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: #666;
  font-size: 14px;
  margin-top: 8px;
}

.all-loaded {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.all-loaded-text {
  color: #999;
  font-size: 14px;
  position: relative;
}

.all-loaded-text::before,
.all-loaded-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 50px;
  height: 1px;
  background: #ddd;
}

.all-loaded-text::before {
  left: -60px;
}

.all-loaded-text::after {
  right: -60px;
}

.load-more-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.load-more-text {
  color: #999;
  font-size: 14px;
}

.all-loaded-container {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  border-top: 1px dashed #eee;
  margin-top: 20px;
}

/* 置顶标签样式 */
.top-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 5;
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
  animation: topBadgePulse 2s infinite alternate;
}

@keyframes topBadgePulse {
  from { box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3); }
  to { box-shadow: 0 4px 10px rgba(255, 77, 79, 0.5); }
}
</style>