<template>
  <div class="article-data-test">
    <header class="header">
      <div class="header-content">
        <!-- 左侧按钮组 -->
        <div class="header-left">
          <!-- 返回按钮 -->
          <div class="back-button" @click="router.go(-1)">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="19" y1="12" x2="5" y2="12"></line>
              <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            <span>返回</span>
          </div>
        </div>

        <div class="header-center">
          <h1>文章数据测试</h1>
        </div>

        <div class="header-right"></div>
      </div>
    </header>

    <div class="main-content">
      <div class="section">
        <h2>查看文章数据</h2>
        <p>下方显示了文章的预览，点击"预览文章"按钮可以查看完整的文章页面。</p>
        
        <ArticleDataViewer :articleData="articleData" />
      </div>
      
      <div class="section">
        <h2>操作说明</h2>
        <div class="instructions">
          <p>您可以通过以下方式使用这些组件：</p>
          <ol>
            <li>在新页面打开文章预览</li>
            <li>复制文章数据到剪贴板</li>
            <li>直接在当前页面渲染文章</li>
          </ol>
          <div class="action-buttons">
            <button @click="openInCurrentPage">在当前页面查看</button>
            <button @click="goToCreateArticle">创建新文章</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import ArticleDataViewer from '../components/ArticleDataViewer.vue';
import { previewArticleInPage } from '../utils/articleUtils';

const router = useRouter();

// 文章数据
const articleData = ref({
  id: 30, 
  userId: 108, 
  username: "***********",
  avatar: "/profile/avatar/2025/06/11/th_20250611202635A001.jpg",
  businessIntroduction: "xxxxx",
  companyName: "123",
  content: "123",
  createTime: "2025-06-24T13:27:41.000+08:00",
  location: "",
  memberType: "超级会员",
  post: "13",
  tags: [
    {name: "中亚", categoryId: 19, createTime: "2025-06-24T11:22:08.000+08:00", updateTime: null, tid: 15},
    {name: "中欧", categoryId: 19, createTime: "2025-06-24T11:22:23.000+08:00", updateTime: null, tid: 16}
  ],
  title: "123",
  articleImgList: [
    {id: 59, articleId: 30, imgUrl: "/images/article/99dfa5a2-21be-45aa-8a36-b295769540fd.jpg"}
  ],
  viewCount: 0
});

// 在当前页面查看
const openInCurrentPage = () => {
  previewArticleInPage(articleData.value, router);
};

// 前往创建文章页面
const goToCreateArticle = () => {
  router.push('/create-post');
};

onMounted(() => {
  // 存储示例数据供预览页面使用
  localStorage.setItem('previewArticleData', JSON.stringify(articleData.value));
  console.log('测试数据已加载');
});
</script>

<style scoped>
.article-data-test {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-top: 80px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 64px;
}

.header-content {
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
}

.header-left, .header-right {
  flex: 1;
}

.header-center {
  flex: 2;
  text-align: center;
}

.header-center h1 {
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  color: #6b7afc;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  opacity: 0.8;
}

.main-content {
  width: 80%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.section p {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.instructions {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.instructions ol {
  margin-left: 20px;
  margin-bottom: 20px;
}

.instructions li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #6b7afc;
  color: white;
}

.action-buttons button:hover {
  background-color: #5c6bc0;
}

@media (max-width: 768px) {
  .main-content {
    width: 95%;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style> 