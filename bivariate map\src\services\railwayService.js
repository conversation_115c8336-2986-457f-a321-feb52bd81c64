// 使用天地图API进行路线规划

const TDT_KEY = 'a9140837d111b6d596b611e6fdedd838';

/**
 * 通过天地图API进行路线规划
 * @param {Object} start - { lat, lng }
 * @param {Object} end - { lat, lng }
 * @returns {Promise<Object>} 路线规划结果
 */

// 添加 OSM 铁路数据查询
async function fetchRailwayData(bounds) {
  const query = `
    [out:json][timeout:25];
    (
      way["railway"="rail"](${bounds.south},${bounds.west},${bounds.north},${bounds.east});
    );
    out body;
    >;
    out skel qt;
  `;
  
  const response = await fetch('https://overpass-api.de/api/interpreter', {
    method: 'POST',
    body: query
  });
  
  return await response.json();
}

// 修改路线规划函数
export async function planRailwayRoute(start, end) {
  try {
    // 获取包含起终点的边界框
    const bounds = {
      north: Math.max(start.lat, end.lat) + 0.1,
      south: Math.min(start.lat, end.lat) - 0.1,
      east: Math.max(start.lng, end.lng) + 0.1,
      west: Math.min(start.lng, end.lng) - 0.1
    };

    // 获取区域内的铁路数据
    const railwayData = await fetchRailwayData(bounds);
    const path = findRailwayPath(railwayData, start, end);

    return {
      start,
      end,
      path,
      distance: calculateTotalDistance(path),
      stations: findStations(railwayData, path),
      provider: 'openstreetmap'
    };
  } catch (error) {
    console.error('铁路路线规划失败:', error);
    return {
      start,
      end,
      path: [start, end],
      distance: calculateDistance(start, end),
      stations: [
        { name: '起点', ...start },
        { name: '终点', ...end }
      ],
      isFallback: true,
      error: error.message
    };
  }
}

// 查找最佳铁路路径
function findRailwayPath(railwayData, start, end) {
  const nodes = railwayData.elements.filter(e => e.type === 'node');
  const ways = railwayData.elements.filter(e => e.type === 'way');
  
  // 找到最近的铁路节点
  const startNode = findNearestNode(nodes, start);
  const endNode = findNearestNode(nodes, end);
  
  // 使用 A* 算法寻找路径
  return findPathAStar(startNode, endNode, ways, nodes);
}

// 查找最近的铁路节点
function findNearestNode(nodes, point) {
  return nodes.reduce((nearest, node) => {
    const dist = calculateDistance(point, node);
    return dist < nearest.distance ? { node, distance: dist } : nearest;
  }, { node: null, distance: Infinity }).node;
}

function findStations(railwayData, path) {
  return railwayData.elements
    .filter(e => e.type === 'node' && e.tags && e.tags.railway === 'station')
    .map(station => ({
      name: station.tags.name || '未命名站',
      lat: station.lat,
      lng: station.lon
    }));
}

// 计算两点间距离（米）
function calculateDistance(start, end) {
  const R = 6371e3;
  const φ1 = start.lat * Math.PI / 180;
  const φ2 = end.lat * Math.PI / 180;
  const Δφ = (end.lat - start.lat) * Math.PI / 180;
  const Δλ = (end.lng - start.lng) * Math.PI / 180;
  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function calculateTotalDistance(path) {
  let totalDistance = 0;
  for (let i = 0; i < path.length - 1; i++) {
    totalDistance += calculateDistance(path[i], path[i + 1]);
  }
  return totalDistance;
}

function findPathAStar(startNode, endNode, ways, nodes) {
  // A* 算法实现
  // 此处省略具体实现
  return [];
}