import { createRouter, createWebHistory } from 'vue-router'
// import Map3D from '../views/3dmap.vue'
import WorldMap from '../views/WorldMap.vue'
import Earth3D from '../components/Earth3D.vue'
// import Earth3D from '../components/Earth3D.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: WorldMap
    },{
      path:'/userinfo',
      name: 'userinfo',
      component: () => import('../views/userinfo.vue'),
      meta: {
        requiresAuth: true // 添加需要认证的标记
      }
    }, {
      path: '/earth3d',
      name: 'Earth3D',
      component: Earth3D
    },{
      path:'/VoucherGiftModal',
      name: 'VoucherGiftModal',
      component:()=>import('../components/VoucherGiftModal.vue')
    },{
      path: '/member',
      name: 'Member',
      component: () => import('@/views/Member.vue'),
      meta: {
        requiresAuth: true // 需要登录才能访问
      }
    },{
      path: '/small-square',
      name: 'SmallSquare',
      component: () => import('@/views/SmallSquare.vue')
    },{
      path: '/create-post',
      name: 'CreatePost',
      component: () => import('@/views/CreatePost.vue')
    },{
      path: '/article-detail/:id',
      name: 'ArticleDetail',
      component: () => import('@/views/ArticleDetail.vue'),
      props: true
    },{
      path: '/article-preview',
      name: 'ArticlePreview',
      component: () => import('@/views/ArticlePreview.vue')
    },{
      path: '/article-test',
      name: 'ArticleDataTest',
      component: () => import('@/views/ArticleDataTest.vue')
    }
  ],
})

// 全局前置守卫：处理微信登录重定向
let wechatLoginHandled = false; // 防止重复处理

// 第一个路由守卫：专门处理微信登录回调
router.beforeEach(async (to, from, next) => {
  // 检测URL中是否有openid参数（微信登录重定向）
  const openid = to.query.openid;
  const bind = to.query.bind;
  const token = to.query.token;
  const msg = to.query.msg 
  const status = to.query.status
  
  // 处理status参数，当status为true时，显示绑定成功消息并跳转到userinfo页面
  // 只有在有明确的status参数时才执行此逻辑，避免与showStationSelector等参数混淆
  if (status === 'true' && to.query.hasOwnProperty('status')) { //点击跳转个人中心部分，绑定成功
    try {
      const { ElMessage } = await import('element-plus');
      // 使用延时确保消息在页面加载后显示
      setTimeout(() => {
        ElMessage({
          message: '绑定成功',
          type: 'success',
          duration: 3000
        });
      }, 500);
      
      // 如果当前不在userinfo页面，则跳转到userinfo页面
      if (to.path !== '/userinfo') {
        next({ path: '/userinfo', replace: true });
        return;
      }
    } catch (error) {
      console.error('处理status参数时出错:', error);
    }
  }
 
  // 处理msg参数，显示提示消息并跳转到userinfo页面
  if (msg && !from.path.includes('/userinfo')) {
    try {
      const { ElMessage } = await import('element-plus');
      // 使用延时确保消息在页面加载后显示
      setTimeout(() => {
        ElMessage({
          message: decodeURIComponent(msg),
          type: 'warning',
          duration: 5000
        });
      }, 500);
      
      // 如果当前不在userinfo页面，则跳转到userinfo页面
      if (to.path !== '/userinfo') {
        next({ path: '/userinfo', replace: true });
        return;
      }
    } catch (error) {
      console.error('处理消息参数时出错:', error);
    }
  }
  
  // 如果没有微信登录相关参数，直接进入下一个守卫
  if (!openid) {
    next();
    return;
  }
  
  // 防止重复处理
  if (wechatLoginHandled) {
    next();
    return;
  }
  
  wechatLoginHandled = true; // 标记为已处理，防止重复处理
  console.log('路由检测到openid参数', openid, '绑定状态:', bind);
  
  try {
    // 动态导入用户存储
    const { useUserStore } = await import('@/store/user');
    const store = useUserStore();
    
          // 根据bind参数决定不同的处理方式
      if (bind === 'true') {
        // 需要绑定手机号，显示绑定弹窗
        console.log('需要绑定手机号，设置待绑定的微信openid');
        store.setPendingWechatOpenid(openid);
        store.setShowBindPhoneModal(true);
        
        // 设置一个特殊标记，表示这是微信绑定流程
        store.isWechatBinding = true;
        
        // 添加调试信息
        console.log('已设置showBindPhoneModal为true');
        console.log('store状态:', {
          pendingWechatOpenid: store.pendingWechatOpenid,
          showBindPhoneModal: store.showBindPhoneModal,
          isWechatBinding: store.isWechatBinding
        });
        
        // 延迟一下，确保状态更新
        setTimeout(() => {
          try {
            // 触发一个自定义事件，直接显示手机号绑定弹窗
            window.dispatchEvent(new CustomEvent('show-bind-phone-modal', { 
              detail: { openid, skipLoginDialog: true } 
            }));
          } catch (err) {
            console.error('显示绑定弹窗失败:', err);
          }
        }, 100);
    } else if (token) {
      // 不需要绑定，直接使用token登录
      console.log('无需绑定，直接使用token登录');
      try {
        await store.loginAction({ token });
        // 登录成功后显示提示
        const { ElMessage } = await import('element-plus');
        ElMessage.success('微信登录成功');
      } catch (error) {
        console.error('使用token登录失败:', error);
        const { ElMessage } = await import('element-plus');
        ElMessage.error('登录失败，请重试');
      }
    }
    
    // 清除URL中的参数但保持当前路由
    const { path, query, hash } = to;
    const newQuery = { ...query };
    delete newQuery.openid;
    delete newQuery.bind;
    delete newQuery.token;
    delete newQuery.msg;
    delete newQuery.status;
    
    // 如果有查询参数变更，则导航到清除后的URL
    if (Object.keys(query).length !== Object.keys(newQuery).length) {
      next({
        path,
        query: newQuery,
        hash,
        replace: true // 替换当前历史记录，不新增历史记录
      });
      return;
    }
    
    next();
  } catch (error) {
    console.error('处理微信登录参数时出错:', error);
    next();
  } finally {
    // 延迟重置标记，允许下次处理
    setTimeout(() => {
      wechatLoginHandled = false;
    }, 1000);
  }
})

// 添加权限控制逻辑
// 白名单页面 - 不需要token就能访问的页面
const whiteList = ['/',"/earth3d", "/small-square", "/article-detail", "/article-preview"]

// 权限控制守卫
router.beforeEach(async (to, from, next) => {
  // 如果URL中有openid参数，说明是微信登录回调，已经在前面处理过了
  if (to.query.openid || to.query.bind || to.query.token) {
    console.log('权限控制: 检测到微信登录参数，跳过权限检查和登录提示')
    next()
    return
  }
  
  // 动态导入用户存储
  const { useUserStore } = await import('@/store/user')
  const userStore = useUserStore()
  const hasToken = userStore.token
  
  if (hasToken) {
    next()
  } else {
    // 检查是否在白名单中，支持动态路由
    const isInWhiteList = whiteList.some(path => {
      if (path === to.path) return true;
      // 检查动态路由，如 /article-detail/:id
      if (path === '/article-detail' && to.path.startsWith('/article-detail/')) return true;
      return false;
    });

    if (isInWhiteList) {
      next()
    } else {
      // 如果正在处理微信登录回调，则不显示"请先登录"提示
      if (!userStore.pendingWechatOpenid) {
        const { ElMessage } = await import('element-plus')
        ElMessage.warning('请先登录')
      }
      next('/')
    }
  }
})

export default router
