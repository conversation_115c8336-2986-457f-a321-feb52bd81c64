<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from '@/composables/useI18n';

const router = useRouter(); // Moved to the top level as per the lint rule
const { t } = useI18n();

const props = defineProps({
    size: {
        type: Number,
        default: 200
    },
    ringCount: {
        type: Number,
        default: 5
    },
    textColor: {
        type: String,
        default: 'white'
    },
    ringColor: {
        type: String,
        default: 'rgba(255, 255, 255, 0.7)'
    },
    text: {
        type: String,
        default: '小广场'
    },
    animationSpeed: {
        type: Number,
        default: 0.5
    },
    textSize: {
        type: Number,
        default: 0.3 // 文本大小占整体的比例
    },
    innerCircleSize: {
        type: Number,
        default: 0.35 // 内圆大小占整体的比例
    }
});

const canvasRef = ref(null);
const animationFrameId = ref(null);
const time = ref(0);

const canvasSize = computed(() => props.size * window.devicePixelRatio);
const center = computed(() => canvasSize.value / 2);

// Use translated text or fall back to props.text
const displayText = computed(() => t('plaza.smallPlaza') || props.text);

// 绘制内部圆形（文本区域）
const drawInnerCircle = (ctx) => {
    const innerRadius = center.value * props.innerCircleSize;

    ctx.beginPath();
    ctx.arc(center.value, center.value, innerRadius, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';
    ctx.fill();
};

// 绘制围绕文本的波纹环
const drawSurroundingWaves = (ctx) => {
    const innerRadius = center.value * props.innerCircleSize; // 内圆半径
    const outerRadius = center.value * 0.9; // 最外圈半径
    const availableSpace = outerRadius - innerRadius;
    const radiusStep = availableSpace / props.ringCount;

    ctx.strokeStyle = props.ringColor;
    ctx.lineWidth = 1 * window.devicePixelRatio;

    for (let i = 0; i < props.ringCount; i++) {
        const baseRadius = innerRadius + radiusStep * (i + 0.5);

        // 每个环的相位偏移，创造流动效果
        const phaseOffset = time.value * props.animationSpeed * (0.5 + i * 0.1);

        ctx.beginPath();

        // 绘制波浪环
        for (let angle = 0; angle <= Math.PI * 2; angle += 0.01) {
            // 使用多个正弦函数叠加创造更自然的波动
            const wave1 = Math.sin(angle * 6 + phaseOffset) * (2 * window.devicePixelRatio);
            const wave2 = Math.sin(angle * 8 - phaseOffset * 0.7) * (1 * window.devicePixelRatio);
            const wave3 = Math.sin(angle * 4 + phaseOffset * 1.3) * (1.5 * window.devicePixelRatio);

            const waveOffset = wave1 + wave2 + wave3;
            const radius = baseRadius + waveOffset;

            const x = center.value + radius * Math.cos(angle);
            const y = center.value + radius * Math.sin(angle);

            if (angle === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.closePath();
        ctx.stroke();
    }
};

// 绘制文本
const drawText = (ctx) => {
    const fontSize = props.size * props.textSize * window.devicePixelRatio;
    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
    ctx.fillStyle = props.textColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(displayText.value, center.value, center.value);
};

// 动画循环
const animate = () => {
    const canvas = canvasRef.value;
    const ctx = canvas.getContext('2d');

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制背景
    ctx.save();
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#4285f4');
    gradient.addColorStop(0.5, '#8b5cf6');
    gradient.addColorStop(1, '#d946ef');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.restore();

    // 绘制内部圆形（文本区域）
    drawInnerCircle(ctx);

    // 绘制围绕文本的波纹环
    drawSurroundingWaves(ctx);

    // 绘制文本
    drawText(ctx);

    // 更新时间
    time.value += 0.02;

    // 继续动画循环
    animationFrameId.value = requestAnimationFrame(animate);
};

// 初始化画布
const initCanvas = () => {
    const canvas = canvasRef.value;
    const ctx = canvas.getContext('2d');

    // 设置画布大小，考虑设备像素比以支持高分辨率屏幕
    canvas.width = canvasSize.value;
    canvas.height = canvasSize.value;

    // 开始动画
    animate();
};

// 组件挂载时初始化
onMounted(() => {
    initCanvas();

    // 监听窗口大小变化，重新初始化画布
    window.addEventListener('resize', initCanvas);
});

// 组件卸载时清理
onUnmounted(() => {
    if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value);
    }

    window.removeEventListener('resize', initCanvas);
});

const handleClick = () => {
    router.push('/small-square');
};
</script>

<template>
    <div class="text-surround-wave-container" 
         :style="{ width: `${size}px`, height: `${size}px` }"
         @click="handleClick"
         style="cursor: pointer;">
        <canvas ref="canvasRef" :style="{ width: '100%', height: '100%', borderRadius: '50%' }"></canvas>
    </div>
</template>

<style scoped>
.text-surround-wave-container {
    position: relative;
    overflow: hidden;
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease-in-out;
}

.text-surround-wave-container:hover {
    transform: scale(1.05);
}
</style>