import axios from 'axios'
import { useUserStore } from '../store/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 150000
  // 移除默认设置的 Content-Type，让请求根据数据类型自动设置
})

// 配置全局 transformRequest，确保 FormData 不被错误处理
service.defaults.transformRequest = [(data, headers) => {
  // 如果数据是 FormData，删除 Content-Type 让浏览器自动添加（带 boundary）
  if (data instanceof FormData) {
    delete headers['Content-Type'];
    console.log('检测到 FormData，已移除 Content-Type 头');
    return data;
  }
  
  // 对于 JSON 数据，使用标准处理
  if (data && typeof data === 'object' && !(data instanceof FormData) && !(data instanceof File) && !(data instanceof Blob)) {
    // 过滤掉值为 null 或 undefined 的属性
    const filteredData = {};
    Object.keys(data).forEach(key => {
      const value = data[key];
      if (value !== null && value !== undefined) {
        filteredData[key] = value;
      }
    });
    headers['Content-Type'] = 'application/json';
    return JSON.stringify(filteredData);
  }
  
  return data;
}];

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    // 只有在有token且不是跳过认证检查的接口时才添加Authorization头
    if (userStore.token && !config.skipAuthCheck) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }
    
    // 再次检测 FormData 对象确保 Content-Type 被正确处理
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
      // console.log('拦截器检测到 FormData，已确保移除 Content-Type 头');
    }
    
    // 对GET请求的params做同样处理
    if (config.params && typeof config.params === 'object') {
      const filteredParams = {};
      Object.keys(config.params).forEach(key => {
        const value = config.params[key];
        if (value !== null && value !== undefined) {
          filteredParams[key] = value;
        }
      });
      config.params = filteredParams;
    }
    
    // 添加日志，帮助调试
    // console.log(`发送请求: ${config.method.toUpperCase()} ${config.url}`, config);
    return config
  },
  error => {
    console.error('请求错误:', error)
    ElMessage.error(error.message || '请求发送失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  
  response => {
      // console.log(`接收响应: ${response.config.url}`, response.data);
    const res = response.data
      
    // 特殊处理文章列表API
    if (response.config.url.includes('/article/list')) {
      console.log('检测到文章列表API响应:', res);
      return res;
    }
      
    if (res.code !== 200 && res.msg !=="未支付") {


      // 处理特殊错误码
      if (res.code === 401) {
        console.warn('认证失败，用户未登录或登录已过期');

        // 检查是否是小广场相关的接口，如果是则不触发登出
        const isPlazaRelatedAPI = response.config.url.includes('/article/list') ||
                                 response.config.url.includes('/tag/list') ||
                                 response.config.url.includes('/category/list') ||
                                 response.config.url.includes('/article/info');

        if (!isPlazaRelatedAPI) {
          const userStore = useUserStore()
          userStore.logoutAction() // 清除用户信息
        } else {
          console.log('小广场相关接口401错误，不触发登出逻辑');
        }
        // 不弹出登录框，避免影响用户体验
      } else {
        // 检查是否是优惠券相关的错误，如果是则不显示错误提示
        const isVoucherError = response.config.url.includes('/coupon') ||
                              (res.msg && (res.msg.includes('优惠券') || res.msg.includes('类型不可用')));

        if (!isVoucherError) {
          ElMessage.error(res.msg || '操作失败')
        } else {
          console.log('优惠券获取失败，不显示错误提示:', res.msg);
        }
      }
      return Promise.reject(res)
    }
    return res
  },
  error => {
    console.error('响应错误:', error.config?.url, error);
    
    // 特殊处理路线查询的401错误，直接返回给调用函数处理
    if (error.response?.status === 401 && error.config?.url.includes('/route/list')) {
      console.log('路线查询需要登录，将401错误传递给调用函数');
      return Promise.reject(error);
    }
    
    // 其他401错误，执行登出操作
    if(error.response?.status === 401) {
      // 检查是否是小广场相关的接口，如果是则不触发登出
      const isPlazaRelatedAPI = error.config?.url.includes('/article/list') ||
                               error.config?.url.includes('/tag/list') ||
                               error.config?.url.includes('/category/list') ||
                               error.config?.url.includes('/article/info');

      if (!isPlazaRelatedAPI) {
        const userStore = useUserStore()
        userStore.logoutAction()
      } else {
        console.log('小广场相关接口401错误，不触发登出逻辑');
      }
    } else {
      ElMessage.error(error.response?.data?.msg || '服务器响应失败')
    }
    return Promise.reject(error)
  }
)

export default service
