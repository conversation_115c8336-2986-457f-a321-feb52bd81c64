<template>
  <div class="comment-form-container">
    <h3 class="form-title">发表评论</h3>
    <div class="form-content">
      <textarea 
        v-model="content" 
        class="comment-textarea" 
        placeholder="写下你的评论..."
        :disabled="isSubmitting"
      ></textarea>
      <div class="form-footer">
        <button 
          class="submit-btn" 
          :disabled="!content.trim() || isSubmitting" 
          @click="submitComment"
        >
          {{ isSubmitting ? "提交中..." : "发表评论" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { postComment } from "../api/comment";
import { useUserStore } from "../store/user";

const props = defineProps({
  // 评论目标ID
  targetId: {
    type: Number,
    required: true
  },
  // 父评论ID，不设默认值
  parentId: {
    type: Number
  }
});

const emit = defineEmits(["comment-success"]);

// 用户信息
const userStore = useUserStore();
const userId = computed(() => userStore.userInfo?.userId || '');

// 评论内容
const content = ref("");
// 提交状态
const isSubmitting = ref(false);

// 提交评论
const submitComment = async () => {
  if (!content.value.trim()) {
    ElMessage.warning("评论内容不能为空");
    return;
  }

  try {
    isSubmitting.value = true;
    
    // 构建评论数据 - 使用完全重构的方式来确保只包含需要的字段
    let commentData = {};
    
    // 1. 添加必要字段
    commentData.content = content.value.trim();
    commentData.targetId = props.targetId;
    commentData.targetType = "article";
    commentData.userId = userId.value;
    
    // 2. 只有当父评论ID存在且有意义时才添加
    if (props.parentId && props.parentId !== 0) {
      commentData.parentId = props.parentId;
    }
    
    // 打印请求数据检查
    console.log("发送的评论数据:", commentData);
    
    // 直接使用API调用后端的 POST /comment 接口
    console.log("准备调用后端接口POST /comment:", commentData);
    
    const response = await postComment(commentData);
    
    if (response.code === 200) {
      ElMessage.success("评论发表成功");
      content.value = ""; // 清空评论内容
      emit("comment-success", response.data); // 触发成功事件
    } else {
      ElMessage.error(response.msg || "评论发表失败");
    }
  } catch (error) {
    console.error("评论发表出错:", error);
    ElMessage.error("评论发表失败，请稍后再试");
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.comment-form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 500;
}

.comment-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  transition: border-color 0.2s;
}

.comment-textarea:focus {
  outline: none;
  border-color: #409eff;
}

.form-footer {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.submit-btn {
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-btn:hover:not(:disabled) {
  background-color: #66b1ff;
}

.submit-btn:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}
</style> 