import request from '../utils/request'

// 获取分类
export function getCategory() {
    console.log('正在请求分类列表...');
    return request({
        url: '/category/list',
        method: 'get',
    }).then(response => {
        console.log('分类列表请求成功，返回数据:', response);
        return response;
    }).catch(error => {
        console.error('分类列表请求失败:', error);
        return Promise.reject(error);
    })
}

// 获取标签
export function getTag(id) {
    console.log(`正在请求标签列表${id ? '，分类ID: ' + id : '(全部)'}...`);
    const url = id ? '/tag/list?categoryId='+id : '/tag/list';
    return request({
        url: url,
        method: 'get',
        // 添加标记，表示这是公开接口，即使401也要返回数据
        skipAuthCheck: true
    }).then(response => {
        console.log(`标签列表请求成功${id ? '，分类ID: ' + id : '(全部)'}，返回数据:`, response);
        return response;
    }).catch(error => {
        console.error(`标签列表请求失败${id ? '，分类ID: ' + id : '(全部)'}:`, error);
        // 对于401错误，返回一个默认的成功响应
        if (error.code === 401) {
            return {
                code: 200,
                rows: [
                    { name: '铁路', tid: 1 },
                    { name: '汽运', tid: 2 },
                    { name: '地区', tid: 3 }
                ],
                msg: '使用默认标签数据'
            };
        }
        return Promise.reject(error);
    })
}

// 获取文章
export function getArticleList(data) {
    console.log("正在请求文章列表，参数:", data);
    
    // 处理tagIds数组 - 转换为逗号分隔的字符串
    const tagIdsArray = data.tagIds ? (Array.isArray(data.tagIds) ? data.tagIds : [data.tagIds]) : 
                        (data.tagId ? [data.tagId] : undefined);
    
    // 构建查询参数，确保符合后端DTO格式
    const requestData = {
        ...data,
        // 将tagIds数组转换为逗号分隔的字符串
        tagIds: tagIdsArray ? tagIdsArray.join(',') : undefined,
        // 删除tagId字段，防止干扰
        tagId: undefined,
        // 添加分页参数
        pageNum: data.pageNum || 1,
        pageSize: data.pageSize || 10
    };
    
    // 移除undefined的属性
    Object.keys(requestData).forEach(key => 
        requestData[key] === undefined && delete requestData[key]
    );
    
    console.log("最终请求参数:", requestData);
    
    return request({
        url: "/article/list",
        method: "get",
        params: requestData, // 使用params传递GET请求参数
        skipAuthCheck: true // 添加标记，表示这是公开接口
    }).then(response => {
        // 检查响应格式
        console.log("文章列表请求成功:", response);
        
        // 如果response已经是业务数据，直接返回
        if (response.rows) {
            return response;
        }
        // 否则返回response.data
        else if (response.data) {
            return response.data;
        }
        // 兜底情况
        return response;
    }).catch(error => {
        console.error("文章列表请求失败:", error);

        // 如果是401错误（未登录），返回示例数据让用户可以看到内容
        if (error.code === 401) {
            console.log("未登录用户访问，返回示例数据");
            return {
                code: 200,
                msg: "示例数据",
                rows: [
                    {
                        id: 'demo-1',
                        title: '欢迎来到小广场',
                        content: '这里是小广场的示例内容，登录后可以查看更多真实内容。',
                        nickName: '系统管理员',
                        time: new Date().toISOString(),
                        isTop: 1,
                        images: [],
                        tags: ['系统', '公告'],
                        memberType: '超级会员'
                    },
                    {
                        id: 'demo-2',
                        title: '登录后查看更多内容',
                        content: '登录后您可以查看所有用户发布的真实内容，并参与讨论。',
                        nickName: '小广场助手',
                        time: new Date(Date.now() - 3600000).toISOString(),
                        isTop: 0,
                        images: [],
                        tags: ['提示'],
                        memberType: '普通会员'
                    }
                ],
                total: 2
            };
        }

        // 其他错误返回空数据
        return {
            code: 500,
            msg: error.message || "请求失败",
            rows: [],
            total: 0
        };
    });
}

// 获取文章详情
export function getArticleDetail(id) {
    return request({
        url: `/article/info/${id}`,
        method: 'get',
        skipAuthCheck: true // 添加标记，表示这是公开接口
    });
}

// Base64转Blob工具函数
function dataURLtoBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
} 

// 发布文章
export function publishArticle(data) {
    console.log("正在发布文章，数据:", data);
    
    // 创建 FormData 对象
    const formData = new FormData();
    
    // 添加文章数据
    formData.append('dto', new Blob([JSON.stringify(data.dto)], {
        type: 'application/json'
    }));
    
    // 添加图片数据
    if (data.images && data.images.length > 0) {
        data.images.forEach((image, index) => {
            // 将 base64 转换为 blob
            const imageBlob = dataURLtoBlob(image);
            formData.append('images', imageBlob, `image_${index}.jpg`);
        });
    }
    
    return request({
        url: '/article',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }).then(response => {
        console.log("文章发布成功:", response);
        return response;
    }).catch(error => {
        console.error("文章发布失败:", error);
        return Promise.reject(error);
    });
}

// 更新文章
export function updateArticle(data) {
    console.log(`正在更新文章，数据:`, data);
    
    // 创建 FormData 对象
    const formData = new FormData();
    
    // 添加文章数据
    formData.append('dto', new Blob([JSON.stringify(data.dto)], {
        type: 'application/json'
    }));
    
    // 添加图片数据
    if (data.images && data.images.length > 0) {
        data.images.forEach((image, index) => {
            // 将 base64 转换为 blob
            const imageBlob = dataURLtoBlob(image);
            formData.append('images', imageBlob, `image_${index}.jpg`);
        });
    }
    
    return request({
        url: `/article`,
        method: 'put',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }).then(response => {
        console.log("文章更新成功:", response);
        return response;
    }).catch(error => {
        console.error("文章更新失败:", error);
        return Promise.reject(error);
    });
} 