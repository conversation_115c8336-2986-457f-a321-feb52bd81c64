<script setup>
import { ref, onMounted } from 'vue'
import imageCache from './utils/imageCache'

const isDev = ref(import.meta.env.DEV)
const stagewiseConfig = {
  plugins: []
}

// 应用启动时预加载常用资源
onMounted(() => {
  console.log('应用启动，初始化图片缓存服务')
  
  // 预加载默认头像
  imageCache.preloadImage('/image/default-avatar.png')
  
  // 预加载默认帖子图片
  imageCache.preloadImage('/image/default-post.jpg')
})
</script>

<template>
  <router-view></router-view>
  <!-- <stagewise-toolbar v-if="isDev" :config="stagewiseConfig" /> -->
</template>

<style>
* {
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100vh;
  overflow-y: auto;
}
</style>
