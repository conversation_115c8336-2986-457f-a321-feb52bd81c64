/**
 * 文章数据处理工具
 */

/**
 * 在新窗口中预览文章数据
 * @param {Object} articleData - 文章数据对象
 */
export const previewArticle = (articleData) => {
  if (!articleData) return;
  
  // 保存到本地存储，以便预览页面读取
  localStorage.setItem('previewArticleData', JSON.stringify(articleData));
  
  // 打开预览页面
  window.open('/article-preview', '_blank');
};

/**
 * 使用路由导航方式预览文章数据
 * @param {Object} articleData - 文章数据对象
 * @param {Object} router - Vue Router实例
 */
export const previewArticleInPage = (articleData, router) => {
  if (!articleData || !router) return;
  
  // 通过路由导航到预览页面
  router.push({
    name: 'ArticlePreview',
    params: {
      articleData
    }
  });
};

/**
 * 格式化文章标签显示
 * @param {Array} tags - 标签数组
 * @returns {String} 格式化后的标签字符串
 */
export const formatTags = (tags) => {
  if (!tags || !Array.isArray(tags) || tags.length === 0) {
    return '无标签';
  }
  
  return tags.map(tag => tag.name).join(', ');
};

/**
 * 格式化文章内容预览
 * @param {String} content - 文章内容
 * @param {Number} maxLength - 最大长度
 * @returns {String} 格式化后的内容预览
 */
export const formatContentPreview = (content, maxLength = 100) => {
  if (!content) return '';
  
  if (content.length <= maxLength) {
    return content;
  }
  
  return content.substring(0, maxLength) + '...';
};

/**
 * 格式化发布时间
 * @param {String} timestamp - 时间戳
 * @returns {String} 格式化后的时间
 */
export const formatPublishTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  // 一天内显示相对时间
  if (diffDay < 1) {
    if (diffHour < 1) {
      if (diffMin < 1) {
        return '刚刚';
      }
      return `${diffMin}分钟前`;
    }
    return `${diffHour}小时前`;
  }
  
  // 超过一天显示日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // 如果是今年，只显示月日
  if (year === now.getFullYear()) {
    return `${month}-${day}`;
  }
  
  // 不是今年，显示年月日
  return `${year}-${month}-${day}`;
};

export default {
  previewArticle,
  previewArticleInPage,
  formatTags,
  formatContentPreview,
  formatPublishTime
}; 