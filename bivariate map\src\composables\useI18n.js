import { ref, watch } from 'vue'
import zhCN from '../locales/zh.js'
import en from '../locales/en.js'

// 创建一个响应式的当前语言变量
const currentLocale = ref(localStorage.getItem('locale') || 'zh-CN')

// 语言包
const messages = {
  'zh-CN': zhCN,
  'en': en
}

export function useI18n() {
  // 翻译函数
  const t = (key, params = {}) => {
    const keys = key.split('.')
    let value = messages[currentLocale.value]
    
    // 遍历key链获取翻译值
    for (const k of keys) {
      if (!value?.[k]) {
        console.warn(`Translation key not found: ${key} in locale ${currentLocale.value}`)
        return key
      }
      value = value[k]
    }
    
    // 替换参数
    if (typeof value === 'string') {
      return value.replace(/\{(\w+)\}/g, (_, key) => params[key] ?? `{${key}}`)
    }
    
    return value
  }

  // 切换语言
  const setLocale = (locale) => {
    console.log('Changing locale to:', locale)
    currentLocale.value = locale
    localStorage.setItem('locale', locale)
  }

  // Alias for compatibility with some components
  const switchLanguage = setLocale
  const currentLanguage = ref(currentLocale.value === 'zh-CN' ? 'zh' : 'en')

  // Keep currentLanguage in sync with currentLocale
  watch(currentLocale, (newLocale) => {
    currentLanguage.value = newLocale === 'zh-CN' ? 'zh' : 'en'
    console.log('Language updated to:', currentLanguage.value)
  })

  return {
    t,
    setLocale,
    switchLanguage,
    currentLocale,
    currentLanguage
  }
}
