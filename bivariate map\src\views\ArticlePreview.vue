<template>
  <div class="article-preview-container">
    <header class="header">
      <div class="header-content">
        <!-- 左侧按钮组 -->
        <div class="header-left">
          <!-- 返回按钮 -->
          <div class="back-button" @click="router.go(-1)">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="19" y1="12" x2="5" y2="12"></line>
              <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            <span>{{ t('article.backButton') }}</span>
          </div>
        </div>

        <!-- 中间Logo -->
        <div class="header-center">
          <img src="../../public/image/newMaptitlelogo.png" alt="New Map" class="logo-image">
        </div>

        <!-- 右侧占位 -->
        <div class="header-right"></div>
      </div>
    </header>

    <!-- 文章内容 -->
    <div class="article-content" v-if="article">
      <div class="article-header">
        <h1 class="article-title">{{ article.title }}</h1>
        <div class="article-meta">
          <div class="author-info">
            <img :src="getAvatarUrl(article.avatar)" :alt="t('article.authorInfo')" class="author-avatar">
            <span class="author-name">{{ article.username || t('article.anonymous') }}</span>
            <span v-if="article.memberType" class="member-type">{{ article.memberType }}</span>
          </div>
          <div class="article-stats">
            <span class="publish-time">{{ formatTime(article.createTime) }}</span>
          </div>
        </div>
      </div>

      <div class="article-tags" v-if="article.tags && article.tags.length > 0">
        <span v-for="tag in article.tags" :key="tag.tid" class="tag">{{ tag.name }}</span>
      </div>

      <div class="article-info-blocks" v-if="article.companyName || article.post">
        <div class="info-block" v-if="article.companyName">
          <div class="info-label">{{ t('article.preview.companyLabel') }}:</div>
          <div class="info-value">{{ article.companyName }}</div>
        </div>
        <div class="info-block" v-if="article.post">
          <div class="info-label">{{ t('article.preview.positionLabel') }}:</div>
          <div class="info-value">{{ article.post }}</div>
        </div>
        <div class="info-block" v-if="article.businessIntroduction">
          <div class="info-label">{{ t('article.preview.businessLabel') }}:</div>
          <div class="info-value">{{ article.businessIntroduction }}</div>
        </div>
      </div>

      <div class="article-body">
        <p class="content-text">{{ article.content }}</p>
      </div>

      <div v-if="article.articleImgList && article.articleImgList.length > 0" class="article-images">
        <h3 class="images-title">{{ t('article.imageAttachments') }}</h3>
        <div :class="['images-grid', getImageLayoutClass(article.articleImgList.length)]">
          <div v-for="image in article.articleImgList" :key="image.id" class="image-wrapper" @click="previewImage(image)">
            <img :src="getImageUrl(image.imgUrl)" :alt="article.title" class="article-image">
          </div>
        </div>
      </div>
    </div>

    <!-- 加载中 -->
    <div v-else-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>{{ t('article.loading') }}</p>
    </div>

    <!-- 加载失败 -->
    <div v-else class="error-container">
      <p>{{ t('article.notFound') }}</p>
      <button class="back-to-list" @click="router.push('/small-square')">{{ t('article.backToList') }}</button>
    </div>

    <!-- 图片预览弹窗 -->
    <div v-if="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
      <div class="preview-container" @click.stop>
        <img :src="previewImageUrl" :alt="t('article.imagePreview')" class="preview-image">
        <button class="close-preview" @click="closeImagePreview">&times;</button>
        
        <!-- 添加图片导航控件 -->
        <div v-if="article.articleImgList && article.articleImgList.length > 1" class="preview-controls">
          <button class="prev-button" @click.stop="prevImage" :disabled="previewState.currentIndex === 0">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <div class="preview-counter">
            {{ previewState.currentIndex + 1 }} / {{ previewState.images.length }}
          </div>
          <button class="next-button" @click.stop="nextImage" :disabled="previewState.currentIndex === previewState.images.length - 1">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import imageCache from '../utils/imageCache';
import { useI18n } from '../composables/useI18n';

const router = useRouter();
const article = ref(null);
const loading = ref(true);
const showImagePreview = ref(false);
const previewImageUrl = ref('');
const baseUrl = import.meta.env.VITE_BASE_API || '';
const { t, currentLocale } = useI18n();

// 预览状态
const previewState = ref({
  show: false,
  images: [],
  currentIndex: 0
});

// 获取头像URL的函数 - 使用缓存服务
const getAvatarUrl = (avatarUrl) => {
  return imageCache.getAvatarUrl(avatarUrl, baseUrl);
};

// 获取图片URL的函数 - 使用缓存服务
const getImageUrl = (imgUrl) => {
  return imageCache.getImageUrl(imgUrl, baseUrl);
};

// 获取图片布局类名
const getImageLayoutClass = (count) => {
  switch (count) {
    case 1: return 'single';
    case 2: return 'double';
    default: return 'triple';
  }
};

// 预览图片
const previewImage = (image) => {
  previewImageUrl.value = getImageUrl(image.imgUrl);
  showImagePreview.value = true;
  
  // 设置预览状态
  previewState.value = {
    show: true,
    images: article.value.articleImgList.map(img => ({
      url: getImageUrl(img.imgUrl),
      description: article.value.title
    })),
    currentIndex: article.value.articleImgList.findIndex(img => img.id === image.id)
  };
};

// 关闭预览
const closeImagePreview = () => {
  showImagePreview.value = false;
  previewState.value.show = false;
};

// 上一张图片
const prevImage = () => {
  if (previewState.value.currentIndex > 0) {
    previewState.value.currentIndex--;
    previewImageUrl.value = previewState.value.images[previewState.value.currentIndex].url;
  }
};

// 下一张图片
const nextImage = () => {
  if (previewState.value.currentIndex < previewState.value.images.length - 1) {
    previewState.value.currentIndex++;
    previewImageUrl.value = previewState.value.images[previewState.value.currentIndex].url;
  }
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 加载文章数据
const loadArticleData = (data) => {
  if (!data) {
    loading.value = false;
    return;
  }
  
  article.value = data;
  
  // 确保文章数据结构完整
  if (!article.value.tags) article.value.tags = [];
  if (!article.value.articleImgList) article.value.articleImgList = [];
  
  loading.value = false;
  console.log('文章数据加载成功:', article.value);
};

onMounted(() => {
  // 从本地存储获取预览数据
  const savedData = localStorage.getItem('previewArticleData');
  if (savedData) {
    try {
      const articleData = JSON.parse(savedData);
      loadArticleData(articleData);
    } catch (error) {
      console.error('解析预览数据失败:', error);
      loading.value = false;
    }
  } else {
    // 尝试直接使用测试数据
    const testData = {
      id: 30, 
      userId: 108, 
      username: "***********",
      avatar: "/profile/avatar/2025/06/11/th_20250611202635A001.jpg",
      businessIntroduction: "xxxxx",
      companyName: "123",
      content: "123",
      createTime: "2025-06-24T13:27:41.000+08:00",
      location: "",
      memberType: "超级会员",
      post: "13",
      tags: [
        {name: "中亚", categoryId: 19, createTime: "2025-06-24T11:22:08.000+08:00", updateTime: null, tid: 15},
        {name: "中欧", categoryId: 19, createTime: "2025-06-24T11:22:23.000+08:00", updateTime: null, tid: 16}
      ],
      title: "123",
      articleImgList: [
        {id: 59, articleId: 30, imgUrl: "/images/article/99dfa5a2-21be-45aa-8a36-b295769540fd.jpg"}
      ],
      viewCount: 0
    };
    loadArticleData(testData);
  }
});
</script>

<style scoped>
.article-preview-container {
  width: 80%;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding-top: 80px;
  /* 为固定定位的header预留空间 */
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 64px;
}

.header-content {
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.header-right {
  flex: 1;
}

.logo-image {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: transform 0.3s;
}

.logo-image:hover {
  transform: scale(1.05);
}



.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  color: #6b7afc;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  opacity: 0.8;
}

.article-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin: 20px auto;
  max-width: 900px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.article-header {
  margin-bottom: 24px;
}

.article-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.4;
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f2f5;
}

.author-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.member-type {
  font-size: 12px;
  background-color: #f6cd61;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
}

.article-stats {
  font-size: 12px;
  color: #666;
}

.publish-time {
  margin-right: 16px;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 24px;
}

.tag {
  font-size: 12px;
  background-color: #f0f2f5;
  color: #5c6bc0;
  padding: 4px 12px;
  border-radius: 16px;
  cursor: pointer;
}

.tag:hover {
  background-color: #e0e4e9;
}

.article-info-blocks {
  background-color: #f8f9fb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.info-block {
  flex: 1 1 200px;
  min-width: 0;
}

.info-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.article-body {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin-bottom: 32px;
  word-break: break-word;
}

.content-text {
  white-space: pre-wrap;
}

.article-images {
  margin-top: 24px;
}

.images-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.images-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.images-grid.single {
  grid-template-columns: 1fr;
}

.images-grid.double {
  grid-template-columns: repeat(2, 1fr);
}

.images-grid.triple {
  grid-template-columns: repeat(3, 1fr);
}

.image-wrapper {
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.image-wrapper:hover {
  transform: translateY(-3px);
}

.article-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  aspect-ratio: 16/9;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  margin: 20px auto;
  max-width: 900px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f0f2f5;
  border-top: 3px solid #6b7afc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: white;
  border-radius: 8px;
  margin: 20px auto;
  max-width: 900px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.error-container p {
  font-size: 18px;
  color: #666;
  margin-bottom: 24px;
}

.back-to-list {
  background-color: #6b7afc;
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.back-to-list:hover {
  background-color: #5c6bc0;
}

.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-container {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.preview-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 4px;
}

.close-preview {
  position: absolute;
  top: -40px;
  right: 0;
  background: transparent;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
}

.preview-controls {
  position: absolute;
  bottom: -50px;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.prev-button,
.next-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.prev-button:disabled,
.next-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.preview-counter {
  color: white;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-preview-container {
    width: 100%;
  }
  
  .header-content {
    width: 95%;
  }
  
  .article-content {
    margin: 10px;
    padding: 16px;
  }
  
  .article-title {
    font-size: 20px;
  }
  
  .images-grid.double,
  .images-grid.triple {
    grid-template-columns: 1fr;
  }
}
</style> 