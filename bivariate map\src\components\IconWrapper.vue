<template>
  <div class="icon-wrapper" :class="size">
    <slot></slot>
    <div v-if="!slotContent" class="icon-fallback" :class="iconName">
      <!-- 基本图标回退形状 -->
      <svg v-if="isPrimaryIcon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path v-if="iconName.includes('edit')" d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
        <path v-if="iconName.includes('edit')" d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        
        <path v-if="iconName.includes('trash')" d="M3 6h18"></path>
        <path v-if="iconName.includes('trash')" d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
        
        <circle v-if="iconName.includes('eye')" cx="12" cy="12" r="2"></circle>
        <path v-if="iconName.includes('eye')" d="M22 12c-2.667 4.667-6 7-10 7s-7.333-2.333-10-7c2.667-4.667 6-7 10-7s7.333 2.333 10 7z"></path>
        
        <rect v-if="iconName.includes('calendar')" x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line v-if="iconName.includes('calendar')" x1="16" y1="2" x2="16" y2="6"></line>
        <line v-if="iconName.includes('calendar')" x1="8" y1="2" x2="8" y2="6"></line>
        <line v-if="iconName.includes('calendar')" x1="3" y1="10" x2="21" y2="10"></line>
      </svg>
      
      <!-- 通用图标 -->
      <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
      </svg>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, useSlots } from 'vue'

// 接收图标名称和尺寸作为props
const props = defineProps({
  name: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'default', // default, sm, lg
  }
})

// 获取插槽内容
const slots = useSlots()
const slotContent = computed(() => {
  return slots.default && slots.default().length > 0
})

// 提取图标名称
const iconName = computed(() => {
  return props.name.toLowerCase()
})

// 判断是否为主要图标
const isPrimaryIcon = computed(() => {
  const primaryIcons = ['edit', 'trash', 'eye', 'calendar']
  return primaryIcons.some(icon => iconName.value.includes(icon))
})

</script>

<style scoped>
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper.sm {
  width: 16px;
  height: 16px;
}

.icon-wrapper.default {
  width: 24px;
  height: 24px;
}

.icon-wrapper.lg {
  width: 32px;
  height: 32px;
}

.icon-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-fallback svg {
  width: 100%;
  height: 100%;
}
</style> 