export const vertexShader = `
  uniform float time;
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  varying vec3 vViewPosition;

  void main() {
    vUv = uv;
    vPosition = position;
    vNormal = normalize(normalMatrix * normal);
    
    vec3 displaced = position;
    float globalPulse = sin(time * 0.8) * 0.1;
    
    bool isEdge = position.z > 4.7;
    
    if(isEdge) {
      float edgeDist = 5.0 - position.z;
      float wave = sin(position.x * 0.1 + time * 1.5) * 0.3;
      displaced += normalize(normal) * wave;
    }
    
    vec4 mvPosition = modelViewMatrix * vec4(displaced, 1.0);
    vViewPosition = -mvPosition.xyz;
    gl_Position = projectionMatrix * mvPosition;
  }
`

export const fragmentShader = `
  uniform float time;
  uniform vec3 baseColor;
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  varying vec3 vViewPosition;

  void main() {
    bool isEdge = vPosition.z > 4.7;
    vec3 finalColor = baseColor;
    
    if(isEdge) {
      float fresnelTerm = dot(normalize(vViewPosition), vNormal);
      fresnelTerm = clamp(1.0 - fresnelTerm, 0.0, 1.0);
      
      float edgePulse = sin(time * 1.5) * 0.5 + 0.5;
      float edgeGlow = fresnelTerm * edgePulse * 2.0;
      
      finalColor += vec3(0.3, 0.7, 1.0) * edgeGlow;
    }
    
    gl_FragColor = vec4(finalColor, 1.0);
  }
`
