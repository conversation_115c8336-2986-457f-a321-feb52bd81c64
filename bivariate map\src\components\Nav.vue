<template>
  <div class="selector-container">
    <div class="language-dropdown">
      <div class="selector-item" @click="toggleLanguageDropdown">
        <div
          class="flag-icon"
          :class="currentLocale === 'zh-CN' ? 'china-flag' : 'english-flag'"
        >
          <template v-if="currentLocale === 'zh-CN'">
            <svg viewBox="0 0 30 20" class="china-flag-svg">
              <rect width="30" height="20" fill="#DE2910" />
              <!-- 主星 -->
              <polygon points="5,4 6.5,8.5 3,6 7,6 3.5,8.5" fill="#FFDE00" />
              <!-- 小星 -->
              <polygon points="10,2 10.75,4 9,3 11,3 9.25,4" fill="#FFDE00" />
              <polygon points="12,4 12.75,6 11,5 13,5 11.25,6" fill="#FFDE00" />
              <polygon points="12,7 12.75,9 11,8 13,8 11.25,9" fill="#FFDE00" />
              <polygon
                points="10,9 10.75,11 9,10 11,10 9.25,11"
                fill="#FFDE00"
              />
            </svg>
          </template>
          <template v-else>
            <svg viewBox="0 0 20 20" class="english-flag-svg">
              <!-- 蓝色背景 -->
              <rect width="20" height="20" fill="#012169" />
              <!-- 白色斜十字 -->
              <path
                d="M0,0 L20,20 M20,0 L0,20"
                stroke="white"
                stroke-width="4"
              />
              <!-- 红色斜十字 -->
              <path
                d="M0,0 L20,20 M20,0 L0,20"
                stroke="#C8102E"
                stroke-width="2"
              />
              <!-- 白色直十字 -->
              <path
                d="M10,0 L10,20 M0,10 L20,10"
                stroke="white"
                stroke-width="6"
              />
              <!-- 红色直十字 -->
              <path
                d="M10,0 L10,20 M0,10 L20,10"
                stroke="#C8102E"
                stroke-width="4"
              />
            </svg>
          </template>
        </div>
        <span>{{
          t("nav." + (currentLocale === "zh-CN" ? "chinese" : "english"))
        }}</span>
        <div class="dropdown-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-chevron-down"
          >
            <path d="m6 9 6 6 6-6" />
          </svg>
        </div>
      </div>
      <div class="language-dropdown-menu" v-if="showLanguageDropdown">
        <div
          class="dropdown-item"
          @click="selectLanguage('zh-CN')"
          :class="{ active: currentLocale === 'zh-CN' }"
        >
          <div class="flag-icon china-flag">
            <svg viewBox="0 0 30 20" class="china-flag-svg">
              <rect width="30" height="20" fill="#DE2910" />
              <!-- 主星 -->
              <polygon points="5,4 6.5,8.5 3,6 7,6 3.5,8.5" fill="#FFDE00" />
              <!-- 小星 -->
              <polygon points="10,2 10.75,4 9,3 11,3 9.25,4" fill="#FFDE00" />
              <polygon points="12,4 12.75,6 11,5 13,5 11.25,6" fill="#FFDE00" />
              <polygon points="12,7 12.75,9 11,8 13,8 11.25,9" fill="#FFDE00" />
              <polygon
                points="10,9 10.75,11 9,10 11,10 9.25,11"
                fill="#FFDE00"
              />
            </svg>
          </div>
          <span>{{ t("nav.chinese") }}</span>
        </div>
        <div
          class="dropdown-item"
          @click="selectLanguage('en')"
          :class="{ active: currentLocale === 'en' }"
        >
          <div class="flag-icon english-flag">
            <svg viewBox="0 0 20 20" class="english-flag-svg">
              <!-- 蓝色背景 -->
              <rect width="20" height="20" fill="#012169" />
              <!-- 白色斜十字 -->
              <path
                d="M0,0 L20,20 M20,0 L0,20"
                stroke="white"
                stroke-width="4"
              />
              <!-- 红色斜十字 -->
              <path
                d="M0,0 L20,20 M20,0 L0,20"
                stroke="#C8102E"
                stroke-width="2"
              />
              <!-- 白色直十字 -->
              <path
                d="M10,0 L10,20 M0,10 L20,10"
                stroke="white"
                stroke-width="6"
              />
              <!-- 红色直十字 -->
              <path
                d="M10,0 L10,20 M0,10 L20,10"
                stroke="#C8102E"
                stroke-width="4"
              />
            </svg>
          </div>
          <span>{{ t("nav.english") }}</span>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="selector-item" @click="toggle3DMode">
      <div class="mode-icon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#4285F4"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-layers"
        >
          <path
            d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"
          />
          <path d="m22 12.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 12.5" />
          <path d="m22 17.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 17.5" />
        </svg>
      </div>
      <span>{{ is3DMode ? t("nav.switch2D") : t("nav.switch3D") }}</span>
    </div>
    <div class="divider"></div>
    <!-- 消息按钮 -->
    <div class="selector-item message-item" @click="handleMessageClick">
      <div class="message-icon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#10B981"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-message-circle"
        >
          <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z" />
        </svg>
        <!-- 消息提示小红点 -->
        <div class="message-badge" v-if="hasUnreadMessages">
          <span>{{
            chatModalStore.weiduCount > 99 ? "99+" : chatModalStore.weiduCount
          }}</span>
        </div>
      </div>
      <span>{{ t("nav.messages") || "消息" }}</span>
    </div>
    <div class="divider"></div>
    <!-- 会员部分 -->
    <div class="selector-item" @click="handleMemberClick">
      <div class="member-icon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#FFD700"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-crown"
        >
          <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7z" />
          <path d="M3 16h18v4H3z" />
        </svg>
      </div>
      <span>{{ t("nav.member") }}</span>
    </div>
    <div class="divider"></div>
    <div
      class="selector-item"
      v-if="userStore.token"
      @click="router.push('/userinfo')"
    >
      <div class="profile-icon">
        <img
          :src="userStore.getAvatar"
          alt="Profile"
          class="profile-img"
        />
        <!-- VIP 徽章 - 根据会员等级显示 -->
        <div v-if="userStore.userInfo.memberType === '普通会员'" class="vip-badge vip-regular">
          VIP
        </div>
        <div v-else-if="userStore.userInfo.memberType === '超级会员'" class="vip-badge vip-super">
         SVIP
        </div>
      </div>
    </div>  
    <button
      class="crystal-blue-button"
      @click="handleLoginClick"
      ref="loginButton"
      v-else
    >
      <span>{{ t("nav.login") }}</span>
      <div class="crystal-shine"></div>
    </button>

    <!-- 聊天弹窗 -->
    <!-- <ChatModal
      ref="chatModalRef"
      :is-visible="showChatModal"
      @close="closeChatModal"
    /> -->
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, computed, watch } from "vue";
import ChatModal from "./chat_modal.vue";
import { useUserStore } from "../store/user";
import router from "../router";
import { useI18n } from "../composables/useI18n";
import { useChatModalStore } from "../store/chatModal";
import { getUnreadCount } from "../api/pusher";
// import {useUserStore} from '../store/user';
// import { useUserStore } from '../store/user'
const userStore = useUserStore();
const { t, setLocale, currentLocale } = useI18n();
const chatModalStore = useChatModalStore();
console.log('未读信息',chatModalStore.weiduCount);

const is3DMode = ref(false);
const showLanguageDropdown = ref(false);
const showChatModal = ref(false); // 聊天弹窗显示状态

// 消息相关状态
const hasUnreadMessages = computed(() => chatModalStore.weiduCount > 0);
let messageCheckInterval = null; // 用于存储消息检查定时器

const chatModalRef = ref(null);

// 修改 emit 声明
const emit = defineEmits([
  "map-control",
  "language-change",
  "toggle-3d",
  "login-click",
]);

const toggleLanguageDropdown = () => {
  showLanguageDropdown.value = !showLanguageDropdown.value;
};

const selectLanguage = (lang) => {
  setLocale(lang);
  showLanguageDropdown.value = false;
  emit("language-change", lang);
};

const toggle3DMode = () => {
  console.log("Nav组件: 切换3D/2D模式", !is3DMode.value ? "启用3D" : "禁用3D");
  is3DMode.value = !is3DMode.value;
  emit("toggle-3d", is3DMode.value);
};

// Add a function to check for new messages
const checkForNewMessages = async () => {
  if (!userStore.token) return;
  
  try {
    // Use the sync method from store to update unread count
    await chatModalStore.syncUnreadCount();
  } catch (error) {
    console.error("获取未读消息失败:", error);
  }
};

// 修改消息点击处理函数
const handleMessageClick = () => {
  if (!userStore.token) {
    emit("login-click"); // 触发登录事件
    emit("map-control", false);
  } else {
    chatModalStore.open(); // 使用store打开聊天弹窗
    emit("map-control", false);
  }
};

// Watch for chat modal visibility changes
watch(() => chatModalStore.visible, (isVisible) => {
  // 如果聊天弹窗关闭，开始定时检查新消息
  if (!isVisible) {
    // 建立定时器，每60秒检查一次新消息
    messageCheckInterval = setInterval(checkForNewMessages, 60000);
  } else {
    // 如果聊天弹窗打开，清除定时器，不再检查新消息
    if (messageCheckInterval) {
      clearInterval(messageCheckInterval);
      messageCheckInterval = null;
    }
  }
});

// 新增：处理从avaterCart来的open-chat事件
const handleOpenChat = async (supplier) => {
  console.log("Nav: handleOpenChat", supplier); // 添加日志
  showChatModal.value = true;
  await nextTick();
  chatModalRef.value?.initializeChat(supplier);
  emit("map-control", false);
};

// 关闭聊天弹窗
const closeChatModal = () => {
  showChatModal.value = false;
  emit("map-control", true); // 恢复地图控制
};

// 会员点击处理函数
const handleMemberClick = () => {
  if (!userStore.token) {
    emit("login-click"); // 触发登录事件
    emit("map-control", false);
  } else {
    router.push("/member");
  }
};

const loginButton = ref(null);

// 登录按钮点击，触发WorldMap组件中的登录弹窗
const handleLoginClick = () => {
  emit("login-click");
  emit("map-control", false);
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  const dropdown = document.querySelector(".language-dropdown");
  if (
    dropdown &&
    !dropdown.contains(event.target) &&
    showLanguageDropdown.value
  ) {
    showLanguageDropdown.value = false;
  }
};

onMounted(async () => {
  document.addEventListener("click", handleClickOutside);
  
  // 初始化时检查是否有未读消息
  if (userStore.token) {
    await checkForNewMessages();
    
    // 如果聊天弹窗没有打开，则启动定时检查
    if (!chatModalStore.visible) {
      messageCheckInterval = setInterval(checkForNewMessages, 60000);
    }
  }
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  // 清除定时器
  if (messageCheckInterval) {
    clearInterval(messageCheckInterval);
    messageCheckInterval = null;
  }
});
</script>

<style scoped>
/* 保持原有样式不变 */
.login-button {
  position: fixed;
  top: 20px;
  right: 100px;
  z-index: 1000;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.login-button:hover {
  background: #2563eb;
}

.selector-container {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 8px 16px;
  width: 140%;
  max-width: 200%;
  margin: 0;
  max-height: 50px;
  justify-content: space-between;
}

.language-dropdown {
  position: relative;
}

.language-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 140px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 8px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.active {
  background-color: #f0f7ff;
}

.selector-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 1;
}

.selector-item:hover {
  background-color: #f5f5f5;
  transform: translateY(-1px);
}

.message-item {
  position: relative;
}

.message-item:hover {
  background-color: #f0fdf4;
}

.message-item:hover .message-icon svg {
  stroke: #059669;
  transform: scale(1.1);
}

.flag-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
}

.china-flag-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.china-flag {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

/* 移除之前的伪元素样式 */
.china-flag::before,
.china-flag::after {
  content: none;
}

.english-flag {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

/* 移除之前的渐变样式 */
.english-flag::before,
.english-flag::after {
  content: none;
}

.mode-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-icon svg {
  filter: drop-shadow(0 0 2px rgba(16, 185, 129, 0.3));
  transition: all 0.2s ease;
}

.message-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.member-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-icon svg {
  filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.5));
}

.profile-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: visible; /* 改为 visible 以显示徽章 */
  background-color: #f0f0f0;
  position: relative; /* 添加相对定位 */
}

.profile-img {
  width: 100%;
  height: 100%;
  border-radius: 50%; /* 确保图片是圆形 */
  object-fit: cover;
}

/* VIP 徽章样式 */
.vip-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  border:none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 10;
  line-height: 1;
  min-width: 20px;
  text-align: center;
  animation: vip-glow 2s ease-in-out infinite alternate;
}

.vip-regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.vip-super {
  background: linear-gradient(135deg, #1f2937, #000000);
  color: #d5b449;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border-color: #000000;
  animation: svip-glow 2s ease-in-out infinite alternate;
}

@keyframes vip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(59, 130, 246, 0.5);
  }
}

@keyframes svip-glow {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 1px 6px rgba(251, 191, 36, 0.5);
  }
}

.divider {
  width: 1px;
  height: 24px;
  background-color: #e0e0e0;
  margin: 0 4px;
  flex-shrink: 2;
}

.dropdown-icon {
  color: #999;
}

span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.crystal-blue-button {
  position: relative;
  padding: 6px 16px;
  background: rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  color: #2563eb;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.crystal-blue-button:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

.crystal-shine {
  position: absolute;
  top: -100%;
  left: -100%;
  width: 50%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  transition: all 0.3s ease;
}

.crystal-blue-button:hover .crystal-shine {
  top: 100%;
  left: 100%;
}

/* 媒体查询，针对小屏幕设备进行适配 */
@media screen and (max-width: 1280px) {
  .selector-item span {
    font-size: 13px;
  }
}

@media screen and (max-width: 1080px) {
  .selector-item span {
    font-size: 12px;
  }

  .selector-item {
    padding: 8px 6px;
    gap: 3px;
  }

  .divider {
    margin: 0 3px;
  }
}

/* 添加更多的响应式调整 */
@media screen and (max-width: 960px) {
  .selector-item span {
    font-size: 11px;
  }

  .selector-item {
    padding: 6px 4px;
    gap: 2px;
  }

  .divider {
    margin: 0 2px;
    height: 20px;
  }

  .flag-icon,
  .mode-icon,
  .message-icon,
  .member-icon {
    transform: scale(0.9);
  }

  .crystal-blue-button {
    padding: 4px 10px;
    font-size: 11px;
  }
}

@media screen and (max-width: 768px) {
  .selector-container {
    padding: 6px 8px;
  }

  .selector-item span {
    font-size: 0; /* 隐藏文字，只显示图标 */
  }

  .selector-item {
    padding: 6px;
  }

  .flag-icon,
  .mode-icon,
  .message-icon,
  .member-icon {
    transform: scale(0.85);
  }

  .crystal-blue-button {
    padding: 4px 8px;
    font-size: 10px;
  }

  /* 调整下拉菜单位置和大小 */
  .language-dropdown-menu {
    width: 120px;
    right: 0;
    left: auto;
  }

  /* 移动端VIP徽章调整 */
  .vip-badge {
    font-size: 6px;
    padding: 1px 3px;
    min-width: 16px;
    bottom: -1px;
    right: -1px;
  }
}

/* 超小屏幕设备适配 */
@media screen and (max-width: 480px) {
  .selector-container {
    padding: 4px 6px;
    gap: 2px;
  }

  .selector-item {
    padding: 4px;
  }

  .divider {
    height: 16px;
    margin: 0 1px;
  }

  .flag-icon,
  .mode-icon,
  .message-icon,
  .member-icon {
    transform: scale(0.75);
  }

  .profile-icon {
    width: 24px;
    height: 24px;
  }

  .crystal-blue-button {
    padding: 3px 6px;
    font-size: 9px;
  }

  /* 超小屏幕VIP徽章调整 */
  .vip-badge {
    font-size: 5px;
    padding: 0.5px 2px;
    min-width: 14px;
  }
}

/* 横屏模式特殊处理 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
  .selector-item span {
    font-size: 11px; /* 在横屏模式下保留文字 */
  }

  .selector-container {
    padding: 6px 10px;
  }

  /* 确保横屏模式下图标正常显示 */
  .flag-icon,
  .mode-icon,
  .message-icon,
  .member-icon {
    transform: scale(0.9);
  }
}

/* iPhone特殊处理 */
@media screen and (max-width: 414px) and (max-height: 896px) {
  .selector-container {
    padding: 3px 5px;
  }

  .selector-item {
    padding: 3px;
  }

  .divider {
    height: 14px;
  }

  .flag-icon,
  .mode-icon,
  .message-icon,
  .member-icon {
    transform: scale(0.7);
  }
}

/* 移动端横屏适配 */
@media screen and (max-width: 926px) and (orientation: landscape) {
  .selector-container {
    transform: scale(0.8);
    transform-origin: top right;
    max-height: 40px;
    padding: 4px 8px;
  }

  .selector-item {
    padding: 4px 8px;
  }

  .language-dropdown-menu {
    transform: scale(0.9);
    transform-origin: top left;
  }

  .crystal-blue-button {
    padding: 4px 12px;
    font-size: 12px;
  }
}
</style>