{"name": "bivariate-map", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "download-tiles": "node scripts/downloadTiles.js", "download-geojson": "node scripts/download-geojson.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@types/three": "^0.176.0", "axios": "^1.9.0", "d3-geo": "^3.1.1", "element-plus": "^2.9.9", "leaflet": "^1.9.4", "leaflet-measure": "^3.1.0", "lucide-vue-next": "^0.511.0", "overpass-frontend": "^3.3.2", "pinia": "2.0.0-rc.10", "pusher-js": "^8.4.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "three": "^0.176.0", "topojson-client": "^3.1.0", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "vue-yandex-maps": "^2.1.7"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.1.2", "@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}