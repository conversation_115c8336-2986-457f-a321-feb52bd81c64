import {listStation} from '../api/maps.js';
export async function getHotStations() {
  try {
    const res = await listStation({ isHot: 1 });
    return res.rows.map(item => ({
      name: item.stationNameZh =='莫斯科（埃列克特罗乌格利）'?'莫斯科':item.stationNameZh,
      nameEn: item.stationNameEn =='ELEKTROUGLI,MOSCOW'?'MOSCOW':item.stationNameEn,
      lat: item.latitude,
      lng: item.longitude,
    }));
  } catch (error) {
    console.error('获取热门站点失败:', error);
    return [];
  }
}
export async function getHotStationsels() {
  try {
    const res = await listStation({ routeType: '中俄' });
    return res.rows.map(item => ({
      name: item.stationNameZh =='莫斯科（埃列克特罗乌格利）'?'莫斯科':item.stationNameZh,
      nameEn: item.stationNameEn =='ELEKTROUGLI,MOSCOW'?'MOSCOW':item.stationNameEn,
      lat: item.latitude,
      lng: item.longitude,
    }));
  } catch (error) {
    console.error('获取热门站点失败:', error);
    return [];
  }
}
export async function getHotStationszhongya() {
  try {
    const res = await listStation({ routeType: '中亚' });
    return res.rows.map(item => ({
      name: item.stationNameZh,
      nameEn: item.stationNameEn,
      lat: item.latitude,
      lng: item.longitude,
    }));
  } catch (error) {
    console.error('获取热门站点失败:', error);
    return [];
  }
}
export async function getHotStationszhongou() {
  try {
    const res = await listStation({ routeType: '中欧' });
    return res.rows.map(item => ({
      name: item.stationNameZh,
      nameEn: item.stationNameEn,
      lat: item.latitude,
      lng: item.longitude,
    }));
  } catch (error) {
    console.error('获取热门站点失败:', error);
    return [];
  }
}
export async function getHotStationsyunnan() {
  try {
    const res = await listStation({ routeType: '东南亚' });
    return res.rows.map(item => ({
      name: item.stationNameZh,
      nameEn: item.stationNameEn,
      lat: item.latitude,
      lng: item.longitude,
    }));
  } catch (error) {
    console.error('获取热门站点失败:', error);
    return [];
  }
}

//  const hotStations = [
//   {
//     name: '北京',
//     nameEn: 'Beijing',
//     lat: 39.90402,
//     lng: 116.39465,
//   },
//   {
//     name: '上海',
//     nameEn: 'Shanghai',
//     lat: 31.05673,
//     lng: 121.47034,
//   },
//   { 
//     name: '广州南站', 
//     lat: 22.9874, 
//     lng: 113.2699,
//   },
//   // ... 更多热门站点
// ];



// 根据客流量获取站点大小
export function getStationSize(passengers) {
  if (passengers >= 120000) {
    return 38;
  } else if (passengers >= 80000) {
    return 24;
  }
  return 10;
}

// 根据等级获取站点颜色
export function getStationColor(level) {
  switch(level) {
    case 'special': return '#FF0000'; // 改为红色
    case 'first': return '#FF4D4F';   // 淡红色
    case 'second': return '#FF7875';  // 浅红色
    default: return '#FFA39E';        // 最浅红色
  }
}