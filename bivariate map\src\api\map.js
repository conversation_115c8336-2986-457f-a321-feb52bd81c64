import request from '@/utils/request'


export function getRoute(data) {
  return request({
      url: '/route/list',
      method: 'get',
     params:{endStationNameEn:data.endStationNameEn, startStationNameZh:data.startStationNameZh,startStationNameEn:data.startStationNameEn,endStationNameZh:data.endStationNameZh}
      
  })}
export function getQrcode(){
  return request({
    url:'/contactWay/findContactWayList',
    method: 'get',
  })
}
export function getadvertisement(){
  return request({
    url:'/advertisement/list',
    method: 'get'
  })
}

export function gettonyonggfx(type){
  return request({
    url:`/commonText/info?type=`+ type,
    method: 'get'
  })
}