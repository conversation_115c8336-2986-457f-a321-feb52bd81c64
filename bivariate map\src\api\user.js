import request from '../utils/request'

export function login(data) {
  return request({
    url: '/api/loginByPhone',
    method: 'post',
    data
  })
}

export function getUserInfo() {
  return request({
    url: `/system/user/profile`,
    method: 'get'
  })
}
export function updataUserInfo(formData) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: formData
    // 不再手动设置 headers，让 axios 自动添加 multipart 边界
  })
}

export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data: data
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

export function getsendSms(phone) {
  return request({
    method: 'post',
    url: '/api/sendSms?phone=' + phone
  })
}
export function register(data) {

  return request({
    method: 'post',
    url: '/api/register',
    data
  })
}
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
export function loginpassword(data) {
  return request({
    url: '/login/frontend',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}
export function uploadAvatar(data) {
  // 确保data是FormData类型
  let formData;
  if (data instanceof FormData) {
    formData = data;
    
    // 确保FormData中包含user字段
    let hasUserField = false;
    for (let [key] of formData.entries()) {
      if (key === 'user') {
        hasUserField = true;
        break;
      }
    }
    
    // 如果没有user字段，添加一个空的user对象
    if (!hasUserField) {
      formData.append('user', new Blob([JSON.stringify({})], {
        type: 'application/json'
      }), 'user.json');
    }
  } else {
    formData = new FormData();
    
    // 添加user字段（带文件名）
    formData.append('user', new Blob([JSON.stringify(data || {})], {
      type: 'application/json'
    }), 'user.json');
    
    // 如果是普通对象，将其属性添加到FormData
    if (data && typeof data === 'object') {
      Object.keys(data).forEach(key => {
        if (key !== 'user' && data[key] instanceof File) {
          formData.append(key, data[key], data[key].name);
        }
      });
    }
  }
  
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: formData
    // 不再手动设置 headers，让 axios 自动添加 multipart 边界
  })
}

export function getopenidboolen(){
  return request({
    url:'/wechat/ifBind',
    method:'get'
  })
}