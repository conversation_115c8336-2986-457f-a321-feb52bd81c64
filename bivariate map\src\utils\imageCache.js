// 图片URL缓存服务
const imageCache = {
  cache: new Map(),
  
  // 获取图片URL，优先从缓存获取
  getImageUrl(imgUrl, baseUrl) {
    if (!imgUrl) return '';
    
    // 生成缓存键
    const cacheKey = `${baseUrl || ''}:${imgUrl}`;
    
    // 如果缓存中存在，直接返回
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    // 否则处理URL并缓存结果
    let resultUrl = '';
    
    // 检查imgUrl是否已经包含http前缀
    if (imgUrl.startsWith('http')) {
      resultUrl = imgUrl;
    }
    // 检查是否已经包含/images/路径
    else if (imgUrl.startsWith('/images/')) {
      resultUrl = baseUrl + imgUrl;
    }
    // 检查是否以下划线开头的特殊格式
    else if (imgUrl.startsWith('_')) {
      resultUrl = baseUrl + '/file/' + imgUrl;
    }
    // 对于测试数据，返回默认图片
    else if (imgUrl === 'test.jpg' || imgUrl === 'test3.jpg') {
      resultUrl = '/image/default-post.jpg';
    }
    // 默认路径处理
    else {
      resultUrl = baseUrl + '/file/' + imgUrl;
    }
    
    // 缓存结果
    this.cache.set(cacheKey, resultUrl);
    return resultUrl;
  },
  
  // 获取头像URL，优先从缓存获取
  getAvatarUrl(avatarUrl, baseUrl) {
    if (!avatarUrl) return '/image/default-avatar.png';
    
    // 生成缓存键
    const cacheKey = `avatar:${baseUrl || ''}:${avatarUrl}`;
    
    // 如果缓存中存在，直接返回
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    // 否则处理URL并缓存结果
    let resultUrl = '';
    
    // 检查avatarUrl是否已经包含http前缀
    if (avatarUrl.startsWith('http')) {
      resultUrl = avatarUrl;
    }
    // 检查是否以/profile或/images路径开头
    else if (avatarUrl.startsWith('/profile/') || avatarUrl.startsWith('/images/')) {
      resultUrl = baseUrl + avatarUrl;
    }
    else {
      resultUrl = baseUrl + '/file/' + avatarUrl;
    }
    
    // 缓存结果
    this.cache.set(cacheKey, resultUrl);
    return resultUrl;
  },
  
  // 预加载图片
  preloadImage(url) {
    if (!url) return;
    const img = new Image();
    img.src = url;
  },
  
  // 清除缓存
  clearCache() {
    this.cache.clear();
  }
};

export default imageCache; 