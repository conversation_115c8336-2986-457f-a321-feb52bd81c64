﻿/* 省略号相关的所有代码分析 */


/* 问题描述 */
问题：在没有图片的文章卡片中，省略号显示在文本中间位置，而不是在底部最后一行末尾。


/* 原HTML模板部分 */
<!-- 没有图片时显示文章内容 -->
<div v-if="!hasArticleImage(article)" class="article-content-text">
  {{ article.content || '暂无内容' }}
</div>


/* 原CSS样式部分 */
/* 文章内容文本样式 */
.article-content-text {
  position: absolute;
  bottom: 10px;
  left: 16px;
  right: 16px;
  font-size: 14px;
  color: #666;
  max-height: 3em; /* 使用em单位，更好地适应字体大小 */
  overflow: hidden;
  text-align: left;
  line-height: 1.5;
  margin-top: 10px;
  white-space: normal;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制为2行 */
  -webkit-box-orient: vertical;
}


/* 没有图片时的文章信息样式 */
.no-image-info {
  padding-bottom: 60px; /* 为内容预留空间 */
}

.article-info {
  padding: 16px;
  position: relative;
}


/* 原JavaScript部分 */
// 判断文章是否有图片
const hasArticleImage = (article) => {
  return article.articleImgList && article.articleImgList.length > 0 && article.articleImgList[0].imgUrl;
};


/* 可能的问题原因 */
1. 文本可能包含特殊字符（如换行符）导致CSS的text-overflow和-webkit-line-clamp不能正常工作
2. 中文文本在某些浏览器中可能不会正确应用text-overflow: ellipsis
3. 文本可能包含HTML标签，导致显示异常
4. 文本长度可能不足以触发省略号机制，或过长导致省略号出现在中间
5. CSS属性的顺序可能影响省略号的显示位置


/* 可能的解决方案 */
1. 纯CSS解决方案（优化现有代码）：
.article-content-text {
  position: absolute;
  bottom: 10px;
  left: 16px;
  right: 16px;
  font-size: 14px;
  color: #666;
  max-height: 3em;
  overflow: hidden;
  text-align: left;
  line-height: 1.5;
  margin-top: 10px;
  /* 调整属性顺序和值 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  word-break: break-word; /* 使用break-word替代break-all */
  white-space: normal;
}
