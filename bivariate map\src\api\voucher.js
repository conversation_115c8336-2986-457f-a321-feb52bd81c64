import request from '../utils/request';
// 获取抵值券
export function getvoucher(data){
    let {userId, issuanceSource} = data
    let operatorId = 0
    return request({
        url:'/coupon?userId='+userId+'&issuanceSource='+issuanceSource+'&operatorId='+operatorId,
        method:'get'
    })
}
//获取用户所有抵值券
export function getCouponList(userId){
return request({
    url:'/coupon/getCouponByUserId?userId='+userId,
    method:'get'
})
}
