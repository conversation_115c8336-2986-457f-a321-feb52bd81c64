import fs from 'fs'
import path from 'path'
import https from 'https'

const TILES_DIR = path.join(process.cwd(), 'public', 'tiles')
const ZOOM_LEVELS = [2, 3, 4, 5, 6] // 需要下载的缩放级别

// 创建瓦片存储目录
if (!fs.existsSync(TILES_DIR)) {
  fs.mkdirSync(TILES_DIR, { recursive: true })
}

// 下载单个瓦片
function downloadTile(z, x, y) {
  const url = `https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/${z}/${y}/${x}`
  const dir = path.join(TILES_DIR, `${z}`, `${x}`)
  const file = path.join(dir, `${y}.png`)

  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }

  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download tile: ${response.statusCode}`))
        return
      }

      const fileStream = fs.createWriteStream(file)
      response.pipe(fileStream)
      fileStream.on('finish', () => {
        fileStream.close()
        resolve()
      })
    }).on('error', reject)
  })
}

// 主函数
async function main() {
  for (const z of ZOOM_LEVELS) {
    const numTiles = Math.pow(2, z)
    for (let x = 0; x < numTiles; x++) {
      for (let y = 0; y < numTiles; y++) {
        try {
          await downloadTile(z, x, y)
          console.log(`Downloaded tile: z=${z}, x=${x}, y=${y}`)
        } catch (err) {
          console.error(`Failed to download tile: z=${z}, x=${x}, y=${y}`, err)
        }
      }
    }
  }
}

main()
