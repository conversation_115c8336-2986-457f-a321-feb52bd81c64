<template>
  <div class="test-form-container">
    <h3>测试评论接口</h3>
    <div class="form-group">
      <label>评论内容</label>
      <textarea v-model="content" class="form-control" rows="4"></textarea>
    </div>
    <div class="form-group">
      <label>targetId</label>
      <input v-model.number="targetId" type="number" class="form-control" />
    </div>
    <div class="form-group">
      <label>targetType</label>
      <input v-model="targetType" type="text" class="form-control" />
    </div>
    <div class="form-group">
      <label>parentId</label>
      <input v-model.number="parentId" type="number" class="form-control" />
    </div>
    <div class="form-actions">
      <button @click="sendDirectRequest" class="btn-primary" :disabled="isLoading">
        {{ isLoading ? "发送中..." : "直接发送请求" }}
      </button>
    </div>
    <div v-if="result" class="result">
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';

// 表单数据
const content = ref('测试评论内容');
const targetId = ref(2);
const targetType = ref('article');
const parentId = ref(null); // 默认为空

const isLoading = ref(false);
const result = ref(null);

// 直接发送请求到后端，不经过任何过滤
const sendDirectRequest = async () => {
  isLoading.value = true;
  result.value = null;
  
  try {
    // 构建请求体
    const requestData = {
      content: content.value,
      targetId: targetId.value,
      targetType: targetType.value
    };
    
    // 只有当parentId有值且不为0时才添加到请求中
    if (parentId.value && parentId.value !== 0) {
      requestData.parentId = parentId.value;
    }
    
    console.log('直接发送的数据:', requestData);
    
    // 使用axios直接发送请求，不经过拦截器
    const baseURL = import.meta.env.VITE_BASE_API || '';
    const response = await axios.post(`${baseURL}/comment`, requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    result.value = response.data;
    ElMessage.success('请求发送成功');
  } catch (error) {
    console.error('请求发送失败:', error);
    result.value = {
      error: error.message,
      response: error.response?.data
    };
    ElMessage.error('请求发送失败');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.test-form-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

textarea.form-control {
  resize: vertical;
}

.form-actions {
  margin-top: 20px;
}

.btn-primary {
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
}

.btn-primary:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow-x: auto;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
}
</style> 