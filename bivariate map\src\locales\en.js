export default {

  chatModal: {
    contactDetails: "Contact Details",
    companyName: "Company Name",
    introduction: "Introduction",
    email: "Email",
    phone: "Phone",
    qq: "QQ",
    wechat: "WeChat",
    memberType: "Member Type",
    contactList: "Contact List",
    searchResults: "Search Results",
    searchPlaceholder: "Search contacts by phone or name",
    search: "Search",
    startChat: "Click to start chatting",
    messageInput: "Type a message...",
    send: "Send",
    justNow: "Just now",
    minutesAgo: "{minutes} minutes ago",
    hoursAgo: "{hours} hours ago",
    yesterday: "Yesterday",
    superMember: "Super Member"
  },
  comments: {
    title: "Comments",
    writeComment: "Write a comment...",
    loginFirst: "Please login to comment",
    submit: "Submit",
    submitting: "Submitting...",
    reply: "Reply",
    delete: "Delete",
    noComments: "No comments yet. Be the first to comment!",
    replyToComment: "Reply to Comment",
    writeReply: "Write your reply...",
    cancel: "Cancel",
    sending: "Sending...",
    deleteConfirm: "Are you sure you want to delete this comment?",
    deleteSuccess: "Comment deleted",
    deleteFailed: "Delete failed",
    postSuccess: "Comment posted successfully",
    postFailed: "Failed to post comment",
    replySuccess: "Reply posted successfully",
    replyFailed: "Failed to post reply",
    showAllReplies: "View all {count} replies",
    hideReplies: "Hide replies",
    anonymous: "Anonymous User",
    replyTo: "Reply to @{name}:",
    emptyComment: "Comment cannot be empty",
    charLimit: "{current}/{max} characters"
  },
  article: {
    backButton: 'Back',
    authorInfo: 'Author Info',
    anonymous: 'Anonymous User',
    imageAttachments: 'Image Attachments',
    imagePreview: 'Image Preview',
    notFound: 'Article does not exist or has been deleted',
    notProvided: 'Not provided',
    loading: 'Loading article...',
    backToList: 'Back to List',
    userInfo: {
      title: 'User Information',
      position: 'Position',
      businessIntro: 'Business Introduction',
      companyName: 'Company Name',
      phone: 'Contact Phone',
      userId: 'User ID'
    },
    debug: {
      articleId: 'Article ID',
      routeParamId: 'Route Param ID',
      none: 'None'
    },
    dateFormat: '{year}-{month}-{day} {hours}:{minutes}:{seconds}',
    imageLoadError: 'Image failed to load, please try again later',
    imageLoading: 'Loading image...'
  },
  createPost: {
    title: 'Create Post',
    editTitle: 'Edit Post',
    back: 'Back',
    loading: 'Loading...',
    form: {
      titleLabel: 'Title',
      titlePlaceholder: 'Enter title (max 20 characters)',
      contentLabel: 'Content',
      contentPlaceholder: 'Enter content',
      uploadLabel: 'Upload Images',
      uploadOptional: '(optional)',
      addImage: 'Add image',
      tagsLabel: 'Tags',
      tagsOptional: '(optional)',
      tagsSelected: '{count} tags selected',
      tagsHint: 'Please select relevant tags',
      noTags: 'No tags available',
      converting: 'Processing...'
    },
    actions: {
      publish: 'Publish',
      publishing: 'Publishing...',
      saveChanges: 'Save Changes',
      saving: 'Saving...',
      cancelEdit: 'Cancel Edit'
    },
    imageLimit: 'Maximum 3 images allowed',
    uploadFailed: 'Upload failed',
    membership: {
      title: 'Member-only Feature',
      description1: 'Posting is a member-only feature',
      description2: 'Become a member to post content and connect with partners',
      benefits: {
        posting: 'Create Posts',
        vouchers: 'Get Vouchers',
        support: 'Dedicated Support'
      },
      cancel: 'Not Now',
      confirm: 'Activate Now'
    },
    voucher: {
      title: 'Congratulations on Your Voucher',
      type: 'Member Exclusive Voucher',
      validity: 'Valid for {days} days',
      success: 'You\'ve received a membership voucher!',
      later: 'Use Later',
      useNow: 'Use Now'
    }
  },
  nav: {
    chinese: 'Chinese',
    english: 'English',
    switch3D: 'Switch to 3D',
    switch2D: 'Switch to 2D',
    member: 'Member',
    login: 'Login',
    messages: 'Messages'
  },
  login: {
    title: 'Account Login',
    codeLogin: 'Code Login',
    passwordLogin: 'Password Login',
    phoneNumber: 'Phone Number',
    verificationCode: 'Verification Code',
    password: 'Password',
    captcha: 'Captcha',
    seconds: 's',
    enterPhoneNumber: 'Please enter phone number',
    enterVerificationCode: 'Please enter verification code',
    enterPassword: 'Please enter password',
    enterCaptcha: 'Please enter captcha',
    refreshCaptcha: 'Click to refresh captcha',
    getCode: 'Get Code',
    agreementPrefix: 'I have read and agree to',
    userAgreement: 'User Agreement',
    and: 'and',
    privacyPolicy: 'Privacy Policy',
    login: 'Login',
    otherLoginMethods: 'Other Login Methods',
    phoneVerification: 'Mobile phone number registration',
    wechatScan: 'WeChat Scan',
    noAccount: 'Don\'t have an account?',
    register: 'Register',
    invalidPhone: 'Please enter a valid phone number',
    codeSent: 'Verification code sent',
    codeSendFailed: 'Failed to send verification code',
    codeSendError: 'Failed to send verification code, please try again later',
    codeSendErrorLog: 'Verification code sending error:',
    codeFormatError: 'Verification code must be numbers',
    agreementRequired: 'Please read and agree to the User Agreement and Privacy Policy',
    success: 'Login successful',
    failure: 'Login failed, please check your account information',
    errorRetry: 'Login failed, please try again later',
    loginError: 'Login error:',
    wechatInstructions: 'Scan QR code with WeChat'
  },
  register: {
    phoneTitle: 'Phone Registration',
    phoneNumber: 'Phone Number',
    verificationCode: 'Verification Code',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    enterPhoneNumber: 'Please enter phone number',
    enterVerificationCode: 'Please enter verification code',
    enterPassword: 'Please enter password',
    enterPasswordAgain: 'Please confirm password',
    secondsRemaining: 's to resend',
    getCode: 'Get Code',
    agreementPrefix: 'I have read and agree to',
    userAgreement: 'User Agreement',
    and: 'and',
    privacyPolicy: 'Privacy Policy',
    register: 'Register',
    otherRegisterMethods: 'Other Registration Methods',
    wechatScan: 'WeChat Scan',
    alreadyHaveAccount: 'Already have an account?',
    goToLogin: 'Go to Login',
    codeSent: 'Verification code sent',
    codeSendFailed: 'Failed to send verification code',
    codeSendError: 'Failed to send verification code, please try again later',
    codeSendErrorLog: 'Verification code sending error:',
    agreementRequired: 'Please read and agree to the User Agreement and Privacy Policy',
    completeInfo: 'Please fill in all information',
    passwordMismatch: 'The two passwords do not match',
    registerLoginSuccess: 'Registered and logged in successfully',
    registerSuccessLoginManually: 'Registration successful, please login manually',
    autoLoginFailed: 'Auto login failed:',
    success: 'Registration successful',
    failure: 'Registration failed',
    registerError: 'Registration error:',
    errorRetry: 'Registration failed, please try again later'
  },
  agreement: {
    confirmTitle: 'Agreement Confirmation',
    wechatConfirmText: 'Using WeChat QR code login requires agreeing to our User Agreement and Privacy Policy. Do you agree?',
    userAgreement: 'User Agreement',
    and: 'and',
    privacyPolicy: 'Privacy Policy',
    cancel: 'Cancel',
    agreeAndContinue: 'Agree and Continue',
    cancelConfirmTitle: 'Cancel Agreement Confirmation',
    cancelConfirmText: 'WeChat QR code login requires agreement to User Agreement and Privacy Policy. Canceling will return to the previous page.',
    confirmCancel: 'Do you want to confirm cancellation?',
    dontCancel: 'Don\'t Cancel',
    confirmReturn: 'Confirm Return',
    userAgreementTitle: 'User Agreement',
    userAgreementContent: 'This is the user agreement content...',
    privacyAgreementTitle: 'Privacy Policy',
    privacyAgreementContent: 'This is the privacy policy content...',
    agreeButton: 'Agree'
  },
  worldMap: {
    popularsites: 'Popular Sites',
    searchPlaceholder: 'Search stations...',
    searching: 'Searching...',
    inquire: 'Search',
    clear: 'Clear',
    tabs: {
      hot: 'Popular',
      history: 'History',
      cnRu: 'CN-RU',
      cnAs: 'CN-Asia',
      cnEu: 'CN-EU',
      cnSa: 'Southeast Asia'
    },
    routeOptions: {
      title: 'Available Routes',
      count: '{count} routes',
      expand: 'Show more routes',
      collapse: 'Show less'
    },
    transport: {
      title: 'Transport Mode',
      road: 'Road',
      rail: 'Rail',
      air: 'Air',
      sea: 'Sea'
    },
    stationType: {
      start: 'Start Station',
      destination: 'Destination'
    },
    placeholders: {
      start: 'Enter start location...',
      destination: 'Enter destination...'
    },
    routeInfo: {
      mileage: 'Distance',
      borderPort: 'Border Port',
      duration: 'Duration',
      trafficCondition: 'Traffic',
      availability: 'Availability'
    },
    rotateHint: {
      title: 'Please rotate your device',
      subtitle: 'Rotate for better experience'
    },
    searchResults: 'Search Results',
    noResults: 'No results found',
    planRoute: 'Plan Route',
    planning: 'Planning...',
    routeDetails: {
      title: 'Route Details',
      stations: 'Via Stations',
      distance: 'Total Distance',
      time: 'Estimated Time',
      congestion: 'Traffic Conditions',
      capacity: 'Capacity Status'
    },
    businessIntro: {
      title: 'Business Introduction',
      contact: 'private letter',
      close: 'Close'
    }
  },
  right: {
    share: 'Share',
    miniProgram: 'Official',
    gonzhao: 'Scan to enter the official account',
    wechat: 'WeChat',
    weixinshequn:'Scan the code to enter the WeChat community',
    phone: 'Phone',
    online: 'Online',
    customer: 'Support',
    shares:"share to",
    shareTip: 'The content to be shared has been automatically copied. Just paste it into the chat box and send.'
  },
  userCenter: {
    title: 'User Center',
    profile: {
      title: 'Profile',
      subtitle: 'Complete your profile information',
      required: 'Required Information',
      optional: 'Optional Information',
      upload: 'File Upload',
      businessCard: {
        title: 'Business Card',
        desc: 'Click to upload your business card'
      },
      businessLicense: {
        title: 'Business License',
        desc: 'Click to upload your business license'
      },
      uploadSuccess: 'Upload Success',
      reset: 'Reset',
      save: 'Save'
    },
    nav: {
      vouchers: 'My Vouchers',
      profile: 'Profile',
      settings: 'Settings',
      help: 'Help',
      Mylikes: 'My Likes'
    }
  },
  userInfo: {
    title: 'User Center',
    settings: 'Account Settings',
    backToHome: 'Back to Home',
    changeAvatar: 'Change Avatar',
    nav: {
      profile: 'Profile',
      help: 'Business Intro',
      vouchers: 'My Coupons',
      paymentRecords: 'Payment Records',
      consult: 'My Posts',
      settings: 'Account Settings',
      likes: 'My Likes'
    },
    profile: {
      title: 'Profile',
      promptdialogbox: 'By filling in, you agree to display',
      subtitle: 'Complete your profile to let others know you better',
      required: 'Required Information',
      optional: 'Optional Information',
      name: 'Name',
      phone: 'Phone',
      qq: 'QQ',
      wechat: 'WeChat',
      email: 'Email',
      company: 'Company',
      businessIntro: 'Business Introduction',
      fileUpload: 'File Upload',
      businessCardUpload: 'Business Card',
      businessCardUploadHint: 'Click to upload your business card',
      businessLicenseUpload: 'Business License',
      businessLicenseUploadHint: 'Click to upload your business license',
      uploadSuccess: 'Upload Successful',
      reset: 'Reset',
      Avatar: 'Upload Avatar',
      save: 'Save',
      logout: 'Logout',
      position: 'Position',
      locationLoading: 'Getting location...',
      namePlaceholder: 'Enter your name',
      phonePlaceholder: 'Enter your phone number',
      qqPlaceholder: 'Enter your QQ number',
      wechatPlaceholder: 'Enter your WeChat ID',
      emailPlaceholder: 'Enter your email address',
      companyPlaceholder: 'Enter your company name',
      clicktoview: 'Click to view',
      downloadBusinessCard: 'View business card',
      downloadBusinessLicense: 'Check the business license',
      accountInfo: 'Account Information',
      memberType: 'Member Type',
      regularUser: 'Regular User',
      sitesInfo: 'Active Sites',
      addSite: 'Add New Site',
      wechatBinding: {
        title: 'WeChat Binding',
        bindButton: 'Bind WeChat Account',
        alreadyBound: 'WeChat Account Bound',
        rebindHint: 'Scan again to change bound WeChat account',
        loadingQrCode: 'Loading QR code...',
        scanInstruction: 'Scan with WeChat to bind your account',
        loginRequired: 'Please login first',
        bindSuccess: 'WeChat binding successful',
        bindFailed: 'Binding failed, please try again',
        qrCodeFailed: 'Failed to load QR code, please try again',
        scanSuccess: 'Scan successful, waiting for confirmation...',
        authSuccess: 'Authorization successful, processing...',
        qrExpired: 'QR code expired, please refresh',
        bindingComplete: 'WeChat binding successful!',
        binding: 'Binding your WeChat account...',
        loginFailure: 'Binding failed, please login first'
      }
    },
    vouchers: {
      title: 'My Coupons',
      empty: 'No coupons available',
      threshold: 'Minimum purchase: ¥{amount}',
      validUntil: 'Valid until',
      remainingDays: '{days} days left',
      expired: 'Expired',
      used: 'Used',
      useNow: 'Use Now',
      goUse: 'Use Now',
      loginRequired: 'Please login to view your coupons'
    },
    payment: {
      title: 'Payment Records',
      subtitle: 'View your payment history',
      loading: 'Loading payment records...',
      empty: {
        title: 'No payment records',
        subtitle: 'Your payment history will be displayed here'
      },
      search: {
        orderNo: 'Order Number',
        status: 'Status',
        all: 'All',
        paid: 'Paid',
        unpaid: 'Unpaid',
        canceled: 'Canceled',
        refunded: 'Refunded',
        searchBtn: 'Search',
        resetBtn: 'Reset'
      },
      table: {
        orderNo: 'Order Number',
        productName: 'Product',
        amount: 'Amount',
        status: 'Status',
        payTime: 'Payment Time',
        actions: 'Actions',
        unpaidText: 'Not paid',
        viewDetails: 'Details'
      },
      detail: {
        title: 'Payment Details',
        orderInfo: 'Order Information',
        orderNo: 'Order Number',
        productName: 'Product Name',
        createTime: 'Create Time',
        stationInfo: 'Station Information',
        userInfo: 'User Information',
        userId: 'User ID',
        nickname: 'Nickname',
        notSet: 'Not set',
        close: 'Close',
        cancelOrder: 'Cancel Order',
        amount: 'Amount'
      }
    },
    businessIntro: {
      title: 'Business Introduction',
      subtitle: 'Introduce your business to potential partners',
      placeholder: 'Describe your business services, specialties, or other information you want to share...',
      charCount: '{count}/500 characters',
      emptyText: 'No business introduction yet. Click the edit button to add your business information.',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel'
    },
    consult: {
      title: 'My Posts',
      subtitle: 'Manage your published posts',
      empty: {
        title: 'No posts yet',
        subtitle: 'Your published posts will appear here'
      },
      deleteConfirm: {
        title: 'Delete Post',
        content: 'Are you sure you want to delete this post? This action cannot be undone.',
        confirm: 'Delete',
        cancel: 'Cancel'
      },
      authorAvatar: 'Author Avatar',
      articleImage: 'Article Image',
      likeCount: '{count} likes'
    },
    likes: {
      title: 'My Likes',
      subtitle: 'Posts you have liked',
      empty: {
        title: 'No liked posts',
        subtitle: 'Posts you like will appear here'
      },
      unlikeButton: 'Unlike'
    }
  },
  changePassword: {
    title: 'Change Password',
    subtitle: 'Please enter your current password and new password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    currentPasswordPlaceholder: 'Enter current password',
    newPasswordPlaceholder: 'Enter new password',
    confirmPasswordPlaceholder: 'Confirm new password',
    strengthLabel: 'Password Strength: ',
    cancel: 'Cancel',
    submit: 'Change Password',
    requirements: {
      minLength: 'At least 8 characters',
      number: 'At least 1 number'
    },
    strength: {
      veryWeak: 'Very Weak',
      weak: 'Weak',
      medium: 'Medium',
      strong: 'Strong'
    },
    success: 'Password changed successfully!',
    error: {
      currentPasswordRequired: 'Please enter current password',
      newPasswordRequired: 'Please enter new password',
      confirmPasswordRequired: 'Please confirm new password',
      passwordMismatch: 'Passwords do not match',
      minLength: 'Password must be at least 8 characters',
      requireNumber: 'Password must contain at least one number',
      sameAsOld: 'New password cannot be the same as current password',
      generalError: 'Failed to change password, please try again later'
    },
    errors: {
      minLength: 'Password must be at least 8 characters',
      mismatch: 'Passwords do not match',
      currentPassword: 'Please enter current password',
      newPassword: 'Please enter new password',
      confirmPassword: 'Please confirm new password',
      sameAsCurrent: 'New password cannot be the same as current password',
      number: 'Password must contain at least one number'
    },
    failure: 'Failed to change password, please try again later'
  },
  member: {
    title: 'Membership Center',
    memberStatus: 'Membership Status',
    statusDesc: 'Your current membership information and benefits',
    regularMember: 'Regular Member',
    premiumMember: 'Premium Member',
    notActive: 'Not Activated',
    expiryDate: 'Expiry Date',
    memberBenefits: 'Activate membership for more benefits',
    memberPlans: 'Membership Plans',
    recommended: 'Recommended',
    alreadyActivated: 'Renew',
    premium: 'Premium',
    pricePerMonth: 'month',
    premiumRenewalNotice: 'Please contact customer service to renew premium membership',
    benefits: {
      unlimitedViews: 'Unlimited Content Views',
      prioritySupport: 'Priority Customer Support',
      monthlyDownloads: '10 Downloads per Month',
      allRegularBenefits: 'All Regular Member Benefits',
      dedicatedSupport: 'Dedicated 1-on-1 Support',
      unlimitedDownloads: 'Unlimited Downloads',
      priorityAccess: 'Priority Access to Premium Content'
    },
    buttons: {
      activate: 'Activate Now',
      contactService: 'Contact Support',
      uploadDocs: 'Upload Documents',
      explore: 'Learn About Benefits',
      renew: 'Renew'
    },
    payment: {
      title: 'Regular Membership Payment',
      titleRenew: 'Renew Regular Membership',
      titleStations: 'Station Purchase',
      memberAmount: 'Membership Fee',
      stationAmount: 'Station Fee',
      discount: 'Coupon',
      total: 'Total Amount',
      coupon: {
        use: 'Use Coupon (¥20)',
        available: 'Available Coupons',
        select: 'Select a coupon',
        none: 'No coupon',
        expiry: 'Valid until',
        invalid: 'This coupon is not available'
      },
      method: {
        title: 'Payment Method',
        wechat: 'WeChat Pay',
        alipay: 'Alipay',
        card: 'Bank Card'
      },
      confirm: 'Confirm Payment',
      cancel: 'Cancel',
      success: 'Payment Successful',
      processing: 'Processing Payment',
      alipay: {
        title: 'Alipay Payment',
        prompt: 'Please contact customer service when using Alipay to pay.',
        contactService: 'Contact Customer Service'
      },
      bankTransfer: {
        title: 'Bank Transfer Information',
        companyName: 'Company Name',
        bankName: 'Bank Name',
        accountNumber: 'Account Number',
        transferAmount: 'Transfer Amount',
        notes: 'Transfer Notes',
        notesTitle: 'Important Notes:',
        notesList: [
          'Please include your User ID in the transfer remarks: ',
          'Please save your transfer receipt and contact customer service',
          'Your membership will be activated within 1-2 business days after payment confirmation'
        ],
        contactService: 'Contact Customer Service',
        close: 'Close'
      }
    },
    upload: {
      title: 'Upload Documents',
      type: {
        label: 'Consultation Type',
        general: 'General Inquiry',
        technical: 'Technical Support',
        billing: 'Billing Issue',
        membership: 'Membership Related'
      },
      content: {
        label: 'Content',
        placeholder: 'Please describe your issue in detail...'
      },
      attachment: {
        label: 'Attachments',
        drag: 'Click or drag files here to upload',
        hint: 'Supports PNG, JPG, PDF, max 10MB',
        uploaded: 'Uploaded Files:'
      },
      submit: 'Submit',
      cancel: 'Cancel'
    },
    station: {
      zhushi:'The site is valid during the membership period. It will be suspended upon expiration and resume after membership renewal.',
      selectStation: 'Purchase station',
      stationFee: 'Single-site fee',
      selectStationTitle: 'Purchase station',
      selectStationSubtitle: 'Please select your station',
      searchPlaceholder: 'Search station name...',
      hotStations: 'Popular Stations',
      stationHot: 'Popular',
      stationExit: 'Exit Port',
      noResults: 'No matching stations found',
      noResultsHint: 'Please try other keywords',
      loading: 'Loading station information...',
      confirm: 'Confirm purchase',
      cancel: 'Cancel',
      needMember: 'Please become a member first before selecting a station',
      multiSelectHint: 'Multiple selection',
      singleSelectHint: 'Single selection',
      selectedStations: 'Selected Stations',
      selectedStation: 'Selected Station',
      totalPrice: 'Total Price'
    },
    wxpay: {
      title: 'WeChat Payment',
      scanTip: 'Please scan the QR code with WeChat to complete payment',
      closeConfirm: 'Do you want to close the payment QR code? The order will become invalid after closing!!!',
      timeout: 'Payment timed out, please try again',
      generateFailed: 'Failed to generate QR code, please try again',
      getFailed: 'Failed to get payment QR code',
      createOrderFailed: 'Failed to create order',
      requestFailed: 'Payment request failed, please try again'
    },
    memberBenefitsTitle: 'Member Benefits'
  },
  avaterCart: {
    siteMembers: 'This site is a proxy',
    superMember: 'VIP',
    normalMember: 'Regular',
    vipMembers: 'VIP Members',
    normalMembers: 'Regular Members',
    expand: 'Expand',
    collapse: 'Collapse',
    more: 'more',
    notProvided: 'Not provided',
    contactQQ: 'Contact via QQ',
    qqNotProvided: 'This supplier has not provided a QQ number',
    contactNow: 'Contact Now',
    businessIntro: 'Business Intro',
    loginFirst: 'Please login first',
    noCompanyInfo: 'No company info',
    noNickname: 'No nickname',
    userId: 'ID'
  },
  plaza: {
    smallPlaza: "Plaza",
    searchPlaceholder: "Search titles, content",
    publish: "Post",
    exitPlaza: "Exit Plaza",
    tags: "Tags",
    all: "All",
    topPost: "Top",
    noContent: "No content available",
    postImage: "Post Image",
    previewImage: "Preview Image",
    companyIntro: "Company Introduction",
    edit: "Edit",
    delete: "Delete",
    report: "Report",
    notProvided: "Not provided",
    position: "Position",
    businessIntro: "Business Introduction",
    companyName: "Company Name",
    contactPhone: "Contact Phone",
    userId: "User ID",
    membersOnlyMessage: "Posting feature is only available to members",
    tip: "Tip",
    confirm: "Confirm",
    systemBusy: "System busy, please try again later",
    supplierInfo: {
      title: "Supplier Information",
      contactPerson: "Contact Person",
      position: "Position",
      businessScope: "Business Scope",
      companyName: "Company Name",
      phone: "Phone"
    },
    searchResults: {
      notFound: "No matching content found",
      tryOtherKeywords: "No results for '{keyword}'. Please try other keywords",
      noContent: "No content available"
    },
    loading: "Loading...",
    allLoaded: "All content loaded",
    scrollToLoadMore: "Scroll down to load more",
    dateFormat: "{year}-{month}-{day} {hours}:{minutes}",
    articleImage: "Article Image",
    userAvatar: "User Avatar",
    noTitle: "No Title",
    user: "User",
    anonymous: "Anonymous User",
    unknownTime: "Unknown Time",
    dateFormatError: "Date Format Error",
    loginRequired: "Please login first",
    likeSuccess: "Liked successfully",
    likeFailed: "Failed to like",
    unlikeSuccess: "Unliked successfully",
    unlikeFailed: "Failed to unlike",
    networkError: "Network error, operation failed"
  },
  wechat: {
    scanTitle: 'WeChat QR Code Login',
    loadingQrCode: 'Loading QR code...',
    agreementPrefix: 'I have read and agree to',
    userAgreement: 'User Agreement',
    and: 'and',
    privacyPolicy: 'Privacy Policy',
    otherLoginMethods: 'Other Login Methods',
    phoneVerification: 'Mobile phone number registration',
    alreadyHaveAccount: 'Already have an account?',
    goToLogin: 'Go to Login',
    scanInstructions: 'Please use WeChat to scan the QR code',
    qrCodeError: 'Failed to get QR code, please try again',
    qrCodeErrorLog: 'Failed to get QR code:',
    loginUrlLog: 'WeChat login URL:',
    iframeLoaded: 'WeChat login iframe loaded',
  },
  bindPhone: {
    title: 'Bind Phone Number to WeChat Account',
    description: 'To complete WeChat login, please bind your phone number',
    phone: 'Phone Number',
    verificationCode: 'Verification Code',
    password: 'Set Password',
    getCode: 'Get Code',
    seconds: 's to resend',
    phonePlaceholder: 'Please enter your phone number',
    codePlaceholder: 'Please enter verification code',
    passwordPlaceholder: 'Please set your password',
    agreementPrefix: 'I have read and agree to',
    cancel: 'Cancel',
    confirm: 'Bind Now',
    success: 'Successfully bound WeChat account',
    failure: 'Failed to bind, please try again',
    phoneRequired: 'Please enter your phone number',
    phoneFormat: 'Please enter a valid phone number',
    codeRequired: 'Please enter verification code',
    passwordRequired: 'Please set a password',
    agreementRequired: 'Please read and agree to the User Agreement and Privacy Policy'
  }
}
