import { defineStore } from 'pinia'
import { login, logout, getUserInfo, loginpassword } from '../api/user'
import { ElMessageBox } from 'element-plus'
import router from '@/router'
import { getvoucher } from '../api/voucher'
import { getUnreadCount } from '../api/pusher' // 假设这是获取未读消息数量的接口
export const useUserStore = defineStore('user', {
    state: () => ({
        token: localStorage.getItem('token') || '',
        userInfo: (() => {
            try {
                const savedInfo = localStorage.getItem('userInfo')
                return savedInfo ? JSON.parse(savedInfo) : null
            } catch (e) {
                console.warn('Failed to parse saved user info:', e)
                return null 
            }
        })(),
        avater: 'https://www.keaitupian.cn/cjpic/frombd/0/253/2279408239/3825398873.jpg', // 默认头像
        uuid: '',
        ii: 0,
        showLoginDialog: false, // 添加控制登录框显示的状态
        showVoucherModal: false,
        currentVoucher: {
          amount: 0,
          expiryDate: 0,
          remainingDays: 0
        },
        voucherShownForCurrentLogin: false, // 新增标记当前登录是否已显示
        unreadCount: parseInt(localStorage.getItem('unreadCount')) || 0,
        weiduCount: parseInt(localStorage.getItem('weiduCount')) || 0, // 未读消息数量
        pendingWechatOpenid: null, // 存储待绑定的微信openid
        showBindPhoneModal: false, // 控制是否显示绑定手机号弹窗
        isWechatBinding: false, // 是否正在进行微信绑定流程
    }),

    getters: {
        isLogin: (state) => !!state.token,
        getUserInfo: (state) => state.userInfo,
        getAvatar: (state) => {
            if (!state.userInfo || !state.userInfo.avatar) {
                return state.defaultAvatar;
            }
            
            return state.userInfo.avatar.startsWith('http') 
                ? state.userInfo.avatar 
                : import.meta.env.VITE_BASE_API + state.userInfo.avatar;
        }
    },

    actions: {
        async setmemberExpireTimeAction(memberExpireTime){
          this.userInfo.memberExpireTime = memberExpireTime
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        },
        //更改用户会员状态
        async setmembertypeAction(membertype){
          this.userInfo.memberType = membertype
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        },
        async setuuidAction(uuid) {
            try {
                this.uuid = uuid
                localStorage.setItem('uuid', uuid)
            } catch (error) {
                console.error('获取UUID失败:', error)
                throw error
            }
        },

        // 登录
        async loginAction(res) {
            // Handle both token formats
            const token = res.data?.token || res.token;
            
            if (!token) {
                console.error('No token found in response:', res);
                throw new Error('No token found in response');
            }
            
            this.token = token;
            localStorage.setItem('token', token);
            await this.getUserInfoAction();
            await this.getWeiduCount(); // 添加获取未读消息
            this.voucherShownForCurrentLogin = false; // 重置标记
            
            try {
                const voucherRes = await getvoucher({
                    userId: this.userInfo.userId,
                    issuanceSource: '登录'
                });
                
                if (voucherRes.code === 200 && voucherRes.data) {
                    this.currentVoucher = voucherRes;
                    this.showVoucherModal = true;
                    this.voucherShownForCurrentLogin = true;
                }
            } catch (error) {
                console.error('获取优惠券失败:', error);
            }
        },
        async loginpasswordaction(res) {
            // Handle both token formats
            const token = res.data?.token || res.token;
            
            if (!token) {
                console.error('No token found in response:', res);
                throw new Error('No token found in response');
            }
            
            this.token = token;
            localStorage.setItem('token', token);
            await this.getUserInfoAction();
            await this.getWeiduCount(); // 添加获取未读消息 
            this.voucherShownForCurrentLogin = false; // 重置标记
            
            try {
                const voucherRes = await getvoucher({
                    userId: this.userInfo.userId,
                    issuanceSource: '登录'
                });
                
                if (voucherRes.code === 200 && voucherRes.data) {
                    this.currentVoucher = voucherRes;
                    this.showVoucherModal = true;
                    this.voucherShownForCurrentLogin = true;
                }
            } catch (error) {
                console.error('获取优惠券失败:', error);
            }
        },

        // 添加微信登录处理
        async wechatLoginAction(res) {
            // Handle both token formats
            const token = res.data?.token || res.token;
            
            if (!token) {
                console.error('No token found in response:', res);
                throw new Error('No token found in response');
            }
            
            this.token = token;
            localStorage.setItem('token', token);
            await this.getUserInfoAction();
            await this.getWeiduCount();
            this.voucherShownForCurrentLogin = false;
            
            try {
                const voucherRes = await getvoucher({
                    userId: this.userInfo.userId,
                    issuanceSource: '登录'
                });
                
                if (voucherRes.code === 200 && voucherRes.data) {
                    this.currentVoucher = voucherRes;
                    this.showVoucherModal = true;
                    this.voucherShownForCurrentLogin = true;
                }
            } catch (error) {
                console.error('获取优惠券失败:', error);
            }
        },

        // 获取用户信息
        async getUserInfoAction() {
            try {
                const res = await getUserInfo()
                this.userInfo = res.data
                localStorage.setItem('userInfo', JSON.stringify(res.data))
                
                // 获取会员状态并保存
                try {
                    const { getmembertype } = await import('../api/pay')
                    const memberRes = await getmembertype()
                    if (memberRes.code === 200) {
                        this.setMemberType(memberRes.data)
                    } else {
                        console.warn('获取会员状态失败:', memberRes?.message)
                    }
                } catch (memberError) {
                    console.error('获取会员状态出错:', memberError)
                }

                return res.data
            } catch (error) {
                console.error('获取用户信息失败:', error)
                throw error
            }
        },

        // 获取未读消息数量
        async getWeiduCount() {
            try {
                const res = await getUnreadCount()
                if(res.code === 200) {
                    const total = res.data
                    this.weiduCount = total
                    localStorage.setItem('weiduCount', total.toString())
                }
            } catch(error) {
                console.error('获取未读消息失败:', error) 
            }
        },

        // 退出登录
        async logoutAction() {
            try {
                this.token = ''
                this.userInfo = {}
                this.uuid = ''
                localStorage.removeItem('token')
                localStorage.removeItem('uuid')

                ElMessageBox.confirm('用户登录已过期,是否前往登录?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.showLoginDialog = true
                }).catch(() => {
                    router.push('/')
                })
            } catch (error) {
                console.error('退出登录失败:', error)
            }
        },

        // 关闭弹窗
        closeVoucherModal() {
            this.showVoucherModal = false
            this.currentVoucher = null
        },

        setToken(token) {
            this.token = token
            if (token) {
                localStorage.setItem('token', token)
            } else {
                localStorage.removeItem('token')
            }
        },

        setUserInfo(info) {
            this.userInfo = info
            if (info) {
                localStorage.setItem('userInfo', JSON.stringify(info))
            } else {
                localStorage.removeItem('userInfo')
            }
        },

        setMemberType(memberType) {
            if (this.userInfo) {
                this.userInfo.memberType = memberType
                // 更新本地存储中的用户信息
                localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
            }
        },

        clearUserInfo() {
            this.token = ''
            this.userInfo = null
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
        },

        updateUnreadCount(count) {
            this.unreadCount = count;
            localStorage.setItem('unreadCount', count.toString());
        },

        incrementUnreadCount() {
            const newCount = this.unreadCount + 1;
            this.updateUnreadCount(newCount);
        },

        decrementUnreadCount() {
            const newCount = Math.max(0, this.unreadCount - 1);
            this.updateUnreadCount(newCount);
        },

        resetUnreadCount() {
            this.updateUnreadCount(0);
        },

        // 设置待绑定的微信openid
        setPendingWechatOpenid(openid) {
            this.pendingWechatOpenid = openid;
            if (openid) {
                console.log('设置了待绑定的微信openid:', openid);
                
                // 如果有openid，自动显示登录框
                this.showLoginDialog = true;
            }
        },
        
        // 设置是否显示绑定手机号弹窗
        setShowBindPhoneModal(show) {
            this.showBindPhoneModal = show;
            console.log('设置绑定手机号弹窗状态:', show);
            
            // 如果要显示绑定弹窗，同时也要显示登录框
            if (show) {
                this.showLoginDialog = true;
            }
        },
        
        // 清除待绑定的微信openid
        clearPendingWechatOpenid() {
            this.pendingWechatOpenid = null;
            this.showBindPhoneModal = false;
        },
    }
})