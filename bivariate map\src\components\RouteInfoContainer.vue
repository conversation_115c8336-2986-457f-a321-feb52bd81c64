<template>
  <div class="route-info-container">
    <!-- 添加 transform 过渡效果 -->
    <div class="container" :class="{ 'sidebar-hidden': !showSidebar }" :style="{ height: containerHeight }"
      @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
      <!-- 固定部分 -->
      <div class="fixed-header">
        <div class="header">
          <div class="logo-container">
            <img src="/image/newMaptitlelogo.png" alt="New Map" class="logo-image">
          </div>
        </div>   

        <div class="search-form">
          <div class="form-group">
                          <div class="label">Transport Mode(选择运输方式)</div>
            <div class="select-container" @click="toggleTransportDropdown">
              <div class="select-value">{{ transportMethod === "铁路" ? "Rail(铁路)" : "Road(公路运输)" }}</div>
              <div class="select-arrow" :class="{ 'open': isTransportDropdownOpen }">▼</div>
              <div v-if="isTransportDropdownOpen" class="select-dropdown">
                <div v-for="method in transportMethods" :key="method.value" class="select-option"
                  :class="{ 'selected': method.value === transportMethod, 'disabled': method.disabled }"
                  @click="onSelectTransportMethod(method)">
                  {{ method.label }}
                </div>
              </div>
            </div>
          </div>

          <!-- 如果是汽运则显示初始站 -->
          <div class="form-group" v-if="false">
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" v-model="localStationType" value="start" class="radio-input">
                <span class="radio-custom"></span>
                <span>{{ t('worldMap.stationType.start') }}</span>
              </label>
            </div>
            <div class="select-container">
              <input v-model="localStartPoint" type="text" :placeholder="t('worldMap.placeholders.start')"
                class="search-input">
            </div>
          </div>

          <div class="form-group">
            <div class="radio-group" v-if="!is3DMode">
              <label class="radio-label">
                <input type="radio" v-model="localStationType" value="destination" class="radio-input">
                <span class="radio-custom"></span>
                <span>{{ t('worldMap.stationType.destination') }}</span>
              </label>
            </div>
            <div class="select-container">
              <input v-model="localDestinationPoint" 
                     @input="onDestinationInput"
                     @focus="onDestinationFocus" 
                     @blur="onDestinationBlur" 
                     @keyup.enter="onPlanRoute"
                     type="text" 
                     :placeholder="searchPlaceholder" 
                     class="search-input"
                     autocomplete="off">
              <div v-if="showSuggestions && stationSuggestions.length" class="station-suggestions-list">
                <div v-for="(station, idx) in stationSuggestions" :key="station.nameZh + station.nameEn + idx"
                  class="station-suggestion-item" @mousedown.prevent="onSelectStationSuggestion(station)">
                  {{ station.nameZh }}<span v-if="station.nameEn">({{ station.nameEn }})</span>
                </div>
              </div>
            </div>
          </div>

          <div class="button-group">
            <button class="search-button" @click="onPlanRoute">
              <el-icon class="search-icon"><Search /></el-icon>
              {{ isSearching ? t('worldMap.searching') : t('worldMap.inquire') }}
            </button>
            <button class="clear-button" @click="onClearRoutes" title="清空">
              <el-icon><Delete /></el-icon>
            </button>
          </div>
        </div>
        <div class="tabs">
          <div v-for="tab in ['hot', 'history', 'cnRu', 'cnAs', 'cnEu']" :key="tab"
            :class="['tab', { active: currentTab === tabMapping[tab] }]" @click="onTabClick(tab)">
            {{ t(`worldMap.tabs.${tab}`) }}
          </div>
        </div>
      </div>
      <!-- 可滚动部分 -->
      <div class="scrollable-content">
        <div v-if="availableRoutes.length > 0" class="route-options">
          <div class="route-options-title">可选路线方案
            <span class="route-count">共{{ availableRoutes.length }}条路线</span>
            <span style="float: right; font-size: 80%; color: #548efe;position: relative;top: -3px; cursor: pointer;"
              @click="onToggleAllRoutes">{{ expanded ? '收起其他路线' : '展开其他路线' }}<svg style="position: relative; top: 3px;"
                class="dropdown-arrow" :class="{ 'is-expanded': expanded }" xmlns="http://www.w3.org/2000/svg"
                width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1"
                stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg></span>
          </div>
          <div class="route-options-list">
            <!-- 当展开全部路线时，不显示单独的选中路线 -->
            <template v-if="!showAllRoutes">
              <div class="listcard active" :key="selectedRouteIndex">
                <div class="headercard">
                  <div class="route-name" style="color:#fca61b;font-weight: 900;font-size: 14px;">{{
                    availableRoutes[selectedRouteIndex].stations[0].name }} - {{
                      availableRoutes[selectedRouteIndex].stations[availableRoutes[selectedRouteIndex].stations.length -
                        1].name }}</div>
                  <div class="tag">路线 {{ selectedRouteIndex + 1 }}</div>
                </div>

                <div class="info-grid">
                  <div class="info-row">
                    <div class="info-item">
                      <div class="info-label">里程数：</div>
                      <div class="info-value">2km</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">转关口岸：</div>
                      <div class="info-value">{{availableRoutes[selectedRouteIndex].stations.slice(1, -1).map(s =>
                        s.name).join('、')}}</div>
                    </div>
                  </div>

                  <div class="info-row">
                    <div class="info-item">
                      <div class="info-label" style="font-weight: 900;">耗时：</div>
                      <div class="info-value" style="font-weight: 900;">{{
                        availableRoutes[selectedRouteIndex].totalTime
                      }}</div>
                    </div>

                    <div class="info-item" style="margin-right:63px">
                      <div class="info-label">拥堵情况：</div>
                      <div class="info-value">{{ trafficCondition }}</div>
                    </div>
                  </div>
                  <div class="info-item" style="margin-left:130px">
                    <div class="info-label">舱位情况：</div>
                    <div class="info-value">{{ gpsStatus }}</div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 展示所有路线 -->
            <template v-if="showAllRoutes">
              <div class="listcard" v-for="(route, index) in availableRoutes" :key="index"
                :class="{ active: index === selectedRouteIndex }" @click="onSelectRoute(index)">
                <div class="headercard">
                  <div class="route-name" :class="{ qweactive: index === selectedRouteIndex }">{{
                    route.stations[0].name }} - {{ route.stations[route.stations.length -
                      1].name }}</div>
                  <div class="tag">路线 {{ index + 1 }}</div>
                </div>

                <div class="info-grid">
                  <div class="info-row">
                    <div class="info-item">
                      <div class="info-label">里程数：</div>
                      <div class="info-value">2km</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">转关口岸：</div>
                      <div class="info-value">{{route.stations.slice(1, -1).map(s => s.name).join('、')}}</div>
                    </div>
                  </div>

                  <div class="info-row">
                    <div class="info-item">
                      <div class="info-label" style="font-weight: 900;">耗时：</div>
                      <div class="info-value" style="font-weight: 900;">{{ route.totalTime }}</div>
                    </div>

                    <div class="info-item" style="margin-right:63px">
                      <div class="info-label">拥堵情况：</div>
                      <div class="info-value">{{ trafficCondition }}</div>
                    </div>
                  </div>
                  <div class="info-item" style="margin-left:130px">
                    <div class="info-label">舱位情况：</div>
                    <div class="info-value">{{ gpsStatus }}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-else class="results-list">
          <div v-for="(item, index) in currentList" :key="index" class="result-item"
            @click="onResultItemClick(item)">
            {{ item.name }}
            <span class="result-description">
              {{ item.description }}
              <span v-if="currentTab === '历史'" class="history-time">
                {{ item.timestamp }}
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加控制按钮组 -->
    <div class="control-buttons" :class="{ 'buttons-shifted': !showSidebar }">
      <button class="control-btn toggle-btn" @click="onToggleSidebar" :title="showSidebar ? '收起侧边栏' : '展开侧边栏'">
        <span>{{ showSidebar ? '◀' : '▶' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Search, Delete } from '@element-plus/icons-vue'
import { useI18n } from '../composables/useI18n'

const props = defineProps({
  transportMethod: {
    type: String,
    default: '铁路'
  },
  transportMethods: {
    type: Array,
    default: () => []
  },
  stationType: {
    type: String,
    default: 'destination'
  },
  startPoint: {
    type: String,
    default: ''
  },
  destinationPoint: {
    type: String,
    default: ''
  },
  showSuggestions: {
    type: Boolean,
    default: false
  },
  stationSuggestions: {
    type: Array,
    default: () => []
  },
  isSearching: {
    type: Boolean,
    default: false
  },
  currentTab: {
    type: String,
    default: '热门'
  },
  tabMapping: {
    type: Object,
    default: () => ({})
  },
  availableRoutes: {
    type: Array,
    default: () => []
  },
  selectedRouteIndex: {
    type: Number,
    default: null
  },
  showAllRoutes: {
    type: Boolean,
    default: true
  },
  expanded: {
    type: Boolean,
    default: true
  },
  trafficCondition: {
    type: String,
    default: '通畅'
  },
  gpsStatus: {
    type: String,
    default: '充足'
  },
  currentList: {
    type: Array,
    default: () => []
  },
  showSidebar: {
    type: Boolean,
    default: true
  },
  is3DMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:transportMethod',
  'update:stationType',
  'update:startPoint',
  'update:destinationPoint',
  'select-transport-method',
  'station-input',
  'station-focus',
  'station-blur',
  'select-station-suggestion',
  'plan-route',
  'clear-routes',
  'tab-click',
  'toggle-all-routes',
  'select-route',
  'result-item-click',
  'toggle-sidebar',
  'mouse-enter',
  'mouse-leave',
  'search-3d-location',
])

const { t } = useI18n()

// 本地状态，用于双向绑定
const localStationType = computed({
  get: () => props.stationType,
  set: (val) => emit('update:stationType', val)
})

const localStartPoint = computed({
  get: () => props.startPoint,
  set: (val) => emit('update:startPoint', val)
})

const localDestinationPoint = computed({
  get: () => props.destinationPoint,
  set: (val) => emit('update:destinationPoint', val)
})

// 下拉菜单状态
const isTransportDropdownOpen = ref(false)

// 容器高度计算
const containerHeight = computed(() => {
  return props.expanded && props.showAllRoutes ? '600px' : '430px'
})

// 事件处理方法
const onMouseEnter = () => emit('mouse-enter')
const onMouseLeave = () => emit('mouse-leave')

const toggleTransportDropdown = () => {
  isTransportDropdownOpen.value = !isTransportDropdown.value
}

const onSelectTransportMethod = (method) => {
  if (!method.disabled) {
    emit('select-transport-method', method)
    isTransportDropdownOpen.value = false
  }
}

const onDestinationInput = (e) => {
  emit('station-input', e.target.value)
}

const onDestinationFocus = (e) => {
  emit('station-focus', e.target.value)
}

const onDestinationBlur = () => {
  emit('station-blur')
}

const onSelectStationSuggestion = (station) => {
  emit('select-station-suggestion', station)
}

const onPlanRoute = () => {
  if (props.is3DMode) {
    // 3D模式下执行搜索
    emit('search-3d-location', localDestinationPoint.value)
  } else {
    // 2D模式下执行原有路线规划
    emit('plan-route')
  }
}

const onClearRoutes = () => {
  emit('clear-routes')
}

const onTabClick = (tab) => {
  emit('tab-click', tab)
}

const onToggleAllRoutes = () => {
  emit('toggle-all-routes')
}

const onSelectRoute = (index) => {
  emit('select-route', index)
}

const onResultItemClick = (item) => {
  emit('result-item-click', item)
}

const onToggleSidebar = () => {
  emit('toggle-sidebar')
}

// 修改搜索框placeholder的计算属性
const searchPlaceholder = computed(() => {
  return props.is3DMode ? 
    '搜索地点...' : 
    t('worldMap.placeholders.destination')
})
</script>

<style scoped>
/* 添加侧边栏过渡效果 */
.container {
  transition: transform 0.3s ease;
}

.sidebar-hidden {
  transform: translateX(-100%);
}

/* 控制按钮样式 */
.control-buttons {
  position: absolute;
  left: 300px;
  top: 10%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: all 0.3s ease;
}

.buttons-shifted {
  right: auto;
  left: 15px;
}

.control-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: #f5f5f5;
  transform: scale(1.05);
}

.clear-btn {
  background: #ff4d4f;
  color: white;
}

.clear-btn:hover {
  background: #ff7875;
}

.listcard {
  width: 250px;
  max-width: 280px;
  /* 减小最大宽度 */
  background-color: #ffffff;
  border-radius: 4px;
  padding: 6px 8px;
  /* 减小内边距 */
  margin-bottom: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.listcard:hover,
.listcard.active {
  background-color: #ECF3FF;
  border: 1px solid #548efe;
}

.headercard {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.route-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  max-width: 140px;
  /* 控制文字宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag {
  background-color: #548efe;
  color: white;
  padding: 1px 6px;
  border-radius: 2px;
  font-size: 10px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  gap: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 11px;
  line-height: 1.2;
  width: auto;
  /* 移除固定宽度限制 */
}

.info-label {
  color: #666;
  margin-right: 2px;
  white-space: nowrap;
}

.info-value {
  color: #333;
  font-weight: 500;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80px;
  /* 控制值的最大宽度 */
}

.route-options-list {
  padding: 6px;
  width: 220px;
  /* 控制整个列表容器宽度 */
}

/* 调整里程数和耗时的样式 */
.info-item[style*="font-weight: 900"] {
  color: #548efe;
}

.info-item[style*="font-weight: 900"] .info-value {
  color: #548efe;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
}

.logo-image {
  height: 32px;
  width: auto;
  object-fit: contain;
}



.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.search-icon {
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding: 0 12px;
}

.search-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 36px;
  background: #548efe;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0 16px;
}

.search-button:hover {
  background: #4071d6;
}

.clear-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: none;
  border-radius: 4px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-button:hover {
  background: #e8e8e8;
  color: #ff4d4f;
}

/* 站点建议下拉框样式 */
.station-suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 1002;
  max-height: 220px;
  overflow-y: auto;
}

.station-suggestion-item {
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: background 0.2s;
}

.station-suggestion-item:hover {
  background: #f5f7fa;
}

/* 其他必要样式 */
.dropdown-arrow {
  transition: transform 0.3s ease;
}

.dropdown-arrow.is-expanded {
  transform: rotate(180deg);
}

.qweactive {
  color: #fca61b;
  font-weight: 900;
  font-size: 14px;
}

/* 添加3D模式下的特殊样式 */
.is-3d-mode .search-input {
  background-color: rgba(255, 255, 255, 0.9);
}

.is-3d-mode .select-container {
  background-color: transparent;
}
</style>